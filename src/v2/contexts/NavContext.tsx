import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";

type NavContextType = {
  currentTab: string;
  setCurrentTab: (value: string) => void;
};

const NavContext = createContext<NavContextType | undefined>(undefined);

type NavProviderProps = {
  children: ReactNode;
  initialTab?: string;
};

export function NavProvider({ children, initialTab = "" }: NavProviderProps) {
  // Get initial tab from URL if not provided
  const getInitialTabFromURL = () => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      const tab = searchParams.get("tab");
      return tab || initialTab;
    }
    return initialTab;
  };

  const [currentTab, setCurrentTabState] = useState<string>(getInitialTabFromURL);

  const setCurrentTab = useCallback((value: string) => {
    setCurrentTabState(value);
    // Update URL query parameter
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set("tab", value);
      const newRelativePathQuery =
        window.location.pathname + "?" + searchParams.toString();
      window.history.pushState(null, "", newRelativePathQuery);
    }
  }, []);

  return (
    <NavContext.Provider value={{ currentTab, setCurrentTab }}>
      {children}
    </NavContext.Provider>
  );
}

export function useNavContext() {
  const context = useContext(NavContext);
  if (context === undefined) {
    throw new Error("useNavContext must be used within a NavProvider");
  }
  return context;
} 
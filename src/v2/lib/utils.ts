import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Extracts the filename from a file path, handling both Unix (/) and Windows (\) separators
 */
export function getFilename(filePath: string): string {
  if (!filePath) return '';
  // Handle both Unix and Windows path separators
  const normalizedPath = filePath.replace(/\\/g, '/');
  return normalizedPath.split('/').pop() || filePath;
}

"use client"

import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { ChevronDownIcon } from "lucide-react"
import "./styles.css"

import { cn } from "~/v2/lib/utils"

function AccordionRoot({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" className="AccordionRoot" {...props} />
}

function AccordionItem({
  className,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn("border-b last:border-b-0 AccordionItem", className)}
      {...props}
    />
  )
}

function AccordionTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        data-slot="accordion-trigger"
        className={cn(
          "focus-visible:border-ring",
          "focus-visible:ring-ring/50",
          "flex",
          "flex-1",
          "items-start",
          "gap-1",
          "rounded-md",
          "py-1",
          "text-left",
          "text-sm",
          "font-medium",
          "transition-all",
          "outline-none",
          "focus-visible:ring-[3px]",
          "disabled:pointer-events-none",
          "disabled:opacity-50",
          "pr-1",
          "[&[data-state=open]>svg]:rotate-180",
          className
        )}
        {...props}
      >
        <div className="flex flex-1 gap-1 px-1 items-center">
          {children}
        </div>
        <ChevronDownIcon width={"24px"} height={"24px"} className="pointer-events-none size-2 shrink-0 transition-transform duration-200" />
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  )
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn(className)}>{children}</div>
    </AccordionPrimitive.Content>
  )
}

function AccordionHeader({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Header>) {
  return <AccordionPrimitive.Header className={cn("flex", className)} {...props}>{children}</AccordionPrimitive.Header>
}

export {
  AccordionRoot,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
  AccordionHeader,
}

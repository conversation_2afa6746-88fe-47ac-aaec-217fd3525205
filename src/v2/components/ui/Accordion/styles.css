.AccordionItem {
    background-color: var(--background);
    border-radius: var(--radius);
    margin-bottom: var(--spacing-2);
    box-shadow: var(--drop-shadow-sm-offset-x, 0px) var(--drop-shadow-sm-offset-y, 1px) var(--drop-shadow-sm-blur-radius, 2px) var(--drop-shadow-sm-spread-radius, 0px) var(--drop-shadow-sm-color, rgba(0, 0, 0, 0.15));
    border: 1px solid var(--border);
    margin-bottom: var(--spacing-2);
}

.AccordionRoot {
    padding: var(--spacing-2);
}
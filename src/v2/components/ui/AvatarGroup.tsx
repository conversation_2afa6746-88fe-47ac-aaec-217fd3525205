import React from "react";
import { cn } from "~/v2/lib/utils";

export interface AvatarGroupProps {
    children: React.ReactNode[] | React.ReactNode;
    max?: number;
    className?: string;
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({ children, max = 4, className }) => {
    const avatars = React.Children.toArray(children);
    const visibleAvatars = avatars.slice(0, max);
    const extraCount = avatars.length - max;

    return (
        <div className={cn("flex gap-0.5", className)}>
            {visibleAvatars.map((child, idx) => (
                <div
                    key={idx}
                    style={{ zIndex: max - idx }}
                >
                    {child}
                </div>
            ))}
            {extraCount > 0 && (
                <div
                    className="flex items-center justify-center rounded-[16px] bg-muted text-xs font-medium text-muted-foreground border-2 border-white rounded-[16px] z-0 w-4 h-4"
                >
                    +{extraCount}
                </div>
            )}
        </div>
    );
}; 
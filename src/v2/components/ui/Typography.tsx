import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "~/v2/lib/utils"
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides"

const typographyVariants = cva(overrideMuiTheme, {
  variants: {
    variant: {
      h1: "scroll-m-20 text-center text-4xl font-extrabold tracking-tight text-balance text-grey-800",
      h2: "scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0 text-grey-800",
      h3: "scroll-m-20 text-2xl font-semibold tracking-tight text-grey-800",
      h4: "scroll-m-20 text-xl font-semibold tracking-tight text-grey-800",
      h5: "scroll-m-20 text-lg font-semibold tracking-tight text-grey-800",
      h6: "scroll-m-20 text-base font-medium tracking-tight text-grey-800",
      caption: "text-sm text-muted-foreground",
      body: "text-sm",
      boldBody: "text-sm font-semibold",
      sectionHeader: "text-sm font-semibold",
      // markdown variants
      markdownH1: "scroll-m-20 text-center text-sm font-extrabold tracking-tight text-balance text-grey-800",
      markdownH2: "scroll-m-20 border-b pb-1 text-xs font-bold tracking-tight first:mt-0 text-grey-800",
    },
    noWrap: {
      true: "whitespace-nowrap overflow-hidden text-ellipsis",
      false: "",
    },
  },
  defaultVariants: {
    variant: "body",
    noWrap: false,
  },
});

const variantToComponent = {
  h1: "h1",
  h2: "h2",
  h3: "h3",
  h4: "h4",
  h5: "h5",
  h6: "h6",
  body: "p",
  caption: "p",
  boldBody: "p",
  markdownH1: "h1",
  markdownH2: "h2",
  sectionHeader: "h3",
};

function Typography({
  className,
  variant,
  noWrap = false,
  asChild = false,
  ...props
}: React.ComponentProps<"div"> &
  VariantProps<typeof typographyVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : variantToComponent[variant ?? "body"];

  return (
    <Comp
      data-slot="button"
      className={cn(typographyVariants({ variant, noWrap, className }))}
      {...props}
    />
  );
}

export { Typography, typographyVariants }

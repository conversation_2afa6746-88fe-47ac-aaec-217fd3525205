import React from "react";
import { cn } from "~/v2/lib/utils";

export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
    size?: number;
    bgColor?: string;
}

export const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
    ({ bgColor, className, style, children, ...props }, ref) => {
        return (
            <div
                ref={ref}
                className={cn(
                    "flex items-center justify-center font-medium select-none overflow-hidden black rounded-[32px] w-[32px] h-[32px] bg-muted",
                    className
                )}
                style={{
                    ...style,
                }}
                {...props}
            >
                {children}
            </div>
        );
    }
);
Avatar.displayName = "Avatar"; 
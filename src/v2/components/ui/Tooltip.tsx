"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "~/v2/lib/utils"

function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  )
}

function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  )
}

function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />
}

function TooltipContent({
  className,
  sideOffset = 8,
  children,
  arrowHalf = "top",
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content> & { arrowHalf?: "top" | "bottom" }) {
  // Define the clip-path for top or bottom half
  const arrowClipPath =
    arrowHalf === "top"
      ? "polygon(0% 0%, 100% 0%, 100% 50%, 0% 50%)"
      : "polygon(0% 50%, 100% 50%, 100% 100%, 0% 100%)";
  // Adjust translateY to keep the visible half centered
  const arrowTranslateY = arrowHalf === "top" ? "-25%" : "-75%";
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          "bg-white text-black animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-1.5 py-0.75 text-xs text-balance",
          className
        )}
        style={{
          boxShadow: "0 0 6px -1px rgba(0, 0, 0, 0.10), 0 0 4px -2px rgba(0, 0, 0, 0.10)"
        }}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow
          className="absolute left-1/2 z-40 -translate-x-1/2 rotate-45 rounded-[2px]"
          style={{ width: '0.9rem', height: '0.9rem', boxShadow: "0 0 6px -1px rgba(0, 0, 0, 0.10), 0 0 4px -2px rgba(0, 0, 0, 0.10)", background: "transparent", fill: "transparent", clipPath: arrowClipPath, top: '50%', transform: `translate(-50%, ${arrowTranslateY})` }}
        />
        <TooltipPrimitive.Arrow
          className="absolute left-1/2 z-50 -translate-x-1/2 rotate-45 rounded-[2px] bg-white fill-white z-0"
          style={{ width: '0.9rem', height: '0.9rem', clipPath: arrowClipPath, top: '50%', transform: `translate(-50%, ${arrowTranslateY})` }}
        />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  )
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }

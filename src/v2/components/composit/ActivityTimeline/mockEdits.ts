import { type DocumentEdit } from "~/v2/components/composit/ActivityTimeline/types";

export const mockEdits: DocumentEdit[] = [
  {
    id: "1",
    createdBy: { name: "<PERSON>" },
    createdAt: new Date("2025-07-01T10:00:00Z"),
    content: { valueBefore: "Old Value", valueAfter: "New Value" },
  },
  {
    id: "6",
    createdBy: { name: "<PERSON>" },
    createdAt: new Date("2025-06-24T12:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "6",
    createdBy: { name: "<PERSON>" },
    createdAt: new Date("2025-06-02T12:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "7",
    createdBy: { name: "<PERSON>" },
    createdAt: new Date("2025-06-02T13:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "8",
    createdBy: { name: "<PERSON>" },
    createdAt: new Date("2025-05-02T13:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "4",
    createdBy: { name: "Bob" },
    createdAt: new Date("2025-03-02T12:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "2",
    createdBy: { name: "Bob" },
    createdAt: new Date("2024-11-02T12:30:00Z"),
    content: { valueBefore: "Old Text", valueAfter: "Updated Text" },
  },
  {
    id: "3",
    createdBy: { name: "Charlie" },
    createdAt: new Date("2024-11-03T15:45:00Z"),
    content: { valueBefore: "Previous Data", valueAfter: "Revised Data" },
  },
  {
    id: "33",
    createdBy: { name: "Charlie" },
    createdAt: new Date("2024-11-03T15:43:00Z"),
    content: { valueBefore: "Previous Data", valueAfter: "Revised Data" },
  },
];

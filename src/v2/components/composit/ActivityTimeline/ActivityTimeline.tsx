"use client";

import { useState, useMemo } from "react";
import {
  getRelativeTime,
  isWithin24Hours,
  getFullTimestamp,
} from "~/utils/format-time";
import { cn } from "~/v2/lib/utils";

type Props<ItemData extends { timestamp: Date }> = {
  data: ItemData[];
  children: (item: ItemData, index: number) => React.ReactNode;
};

const getMonthsDiff = (timestamp: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - timestamp.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  return Math.floor(diffInDays / 30);
};

export function ActivityTimeline<ItemData extends { timestamp: Date }>({
  data,
  children,
}: Props<ItemData>) {
  const [visibleMonths, setVisibleMonths] = useState(3);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const sortedActivies = useMemo(
    () =>
      [...data].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()),
    [data],
  );

  const { recentActivies, olderActivies } = useMemo(() => {
    const recent = sortedActivies.filter((activity) =>
      isWithin24Hours(activity.timestamp),
    );
    const older = sortedActivies.filter(
      (activity) => !isWithin24Hours(activity.timestamp),
    );
    return { recentActivies: recent, olderActivies: older };
  }, [sortedActivies]);

  const { visibleActivies, hiddenActivies } = useMemo(() => {
    const visible = olderActivies.filter(
      (activity) => getMonthsDiff(activity.timestamp) < visibleMonths,
    );

    const hidden = olderActivies.filter(
      (activity) => getMonthsDiff(activity.timestamp) >= visibleMonths,
    );

    return { visibleActivies: visible, hiddenActivies: hidden };
  }, [olderActivies, visibleMonths]);

  const groupedVisibleActiviesArray = useMemo(() => {
    const groups = visibleActivies.reduce(
      (groups, activity) => {
        const dayKey = getRelativeTime(activity.timestamp);
        groups[dayKey] ??= [];
        groups[dayKey].push(activity);
        return groups;
      },
      {} as Record<string, ItemData[]>,
    );

    return Object.entries(groups)
      .map(([dayKey, activities]) => ({ dayKey, activities }))
      .sort((a, b) => {
        const aDate = a.activities[0]?.timestamp;
        const bDate = b.activities[0]?.timestamp;
        if (!aDate || !bDate) return 0;
        return bDate.getTime() - aDate.getTime();
      });
  }, [visibleActivies]);

  const { multiActivityGroups, singleActivities } = useMemo(() => {
    const multiGroups = groupedVisibleActiviesArray.filter(
      (group) => group.activities.length > 1,
    );
    const singles = groupedVisibleActiviesArray
      .filter((group) => group.activities.length === 1)
      .flatMap((group) => group.activities);

    return { multiActivityGroups: multiGroups, singleActivities: singles };
  }, [groupedVisibleActiviesArray]);

  const allActivities = useMemo(() => {
    const unsortedActivities = [
      ...recentActivies.map((activity) => ({
        type: "recent" as const,
        data: activity,
        date: activity.timestamp,
      })),
      ...singleActivities.map((activity) => ({
        type: "single" as const,
        data: activity,
        date: activity.timestamp,
      })),
      ...multiActivityGroups.map((group) => ({
        type: "grouped" as const,
        data: group,
        date: group.activities[0]?.timestamp ?? new Date(0), // most recent in group
      })),
    ];

    return unsortedActivities.sort(
      (a, b) => b.date.getTime() - a.date.getTime(),
    );
  }, [recentActivies, singleActivities, multiActivityGroups]);

  const handleViewOlder = () => {
    setVisibleMonths((prev) => prev + 3);
  };

  const toggleGroupExpansion = (dayKey: string) => {
    setExpandedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(dayKey)) {
        newSet.delete(dayKey);
      } else {
        newSet.add(dayKey);
      }
      return newSet;
    });
  };

  return (
    <div className="space-y-4 [--spacing:0.25rem]">
      <ul className="space-y-0">
        {allActivities.map((item, index) => {
          // Timeline visuals: vertical bar, icon, date/timestamp
          const isFirst = index === 0;
          const isLast = index === allActivities.length - 1;
          // Top vertical bar
          const showTopBar = !isFirst;
          // Bottom vertical bar
          const showBottomBar = !isLast;

          if (item.type === "recent" || item.type === "single") {
            const activity = item.data;
            return (
              <li
                key={activity.timestamp.toISOString()}
                className="grid grid-cols-[auto_1fr] grid-rows-[auto_auto_1fr] items-center w-full relative"
              >
                {/* Top vertical line */}
                {showTopBar && (
                  <div className="col-start-1 row-start-1 flex justify-center h-full">
                    <div className="w-1 h-full bg-black/30" />
                  </div>
                )}
                {/* Timestamp */}
                {/* Checkmark icon with vertical line behind */}
                <div className="col-start-1 row-start-2 flex flex-col items-center justify-center relative mx-2">
                  <div className="absolute w-1 h-full bg-black/30 z-0" />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="h-6 w-6 text-green-500 z-10 bg-white rounded-full p-0.5"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div
                  className={cn(
                    "col-start-2 row-start-1 flex items-end pb-2 self-end",
                    isFirst ? "pt-0" : "pt-4",
                  )}
                  title={getFullTimestamp(activity.timestamp)}
                >
                  <span className="text-right text-xs">
                    {getRelativeTime(activity.timestamp)}
                  </span>
                </div>
                {/* Event details */}
                <div className="h-full col-start-2 row-start-2 row-span-3 flex items-center">
                  {children(activity, index)}
                </div>
                {/* Bottom vertical line */}
                {showBottomBar && (
                  <div className="col-start-1 row-start-3 flex justify-center h-full">
                    <div className="w-1 h-full bg-black/30" />
                  </div>
                )}
              </li>
            );
          } else if (item.type === "grouped") {
            const group = item.data;
            const isExpanded = expandedGroups.has(group.dayKey);
            return (
              <li
                key={group.dayKey}
                className="grid grid-cols-[auto_1fr] grid-rows-[auto_auto_auto] items-center w-full relative"
              >
                {/* Top vertical line */}
                {showTopBar && (
                  <div className="col-start-1 row-start-1 flex justify-center h-full">
                    <div className="w-1 h-full bg-black/30" />
                  </div>
                )}
                {/* Group icon with vertical line behind */}
                <div className="col-start-1 row-start-2 flex flex-col items-center justify-center relative mx-2">
                  <div className="absolute w-1 h-full bg-black/30 z-0" />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className={cn(
                      "h-6 w-6 text-blue-500 z-10 bg-white rounded-full p-0.5",
                      "transition-all duration-200 ease-in-out",
                    )}
                  >
                    {isExpanded ? (
                      // Minus icon when expanded
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z"
                        clipRule="evenodd"
                        className="transition-all duration-200 ease-in-out"
                      />
                    ) : (
                      // Plus icon when collapsed
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                        clipRule="evenodd"
                        className="transition-all duration-200 ease-in-out"
                      />
                    )}
                  </svg>
                </div>
                {/* Group label */}
                <div
                  className={cn(
                    "col-start-2 row-start-1 flex items-end pb-2 self-end",
                    isFirst ? "pt-0" : "pt-4",
                  )}
                  title={
                    group.activities[0]
                      ? getFullTimestamp(group.activities[0].timestamp)
                      : group.dayKey
                  }
                >
                  <span className="text-right text-xs">{group.dayKey}</span>
                </div>
                {/* Group description with expandable functionality */}
                <div className="h-full col-start-2 row-start-2 text-xs flex items-center">
                  <button
                    type="button"
                    className={cn(
                      "border border-black/30 rounded px-2 py-1",
                      "flex items-center justify-center hover:bg-gray-50 text-xs select-none",
                      "focus:outline-none",
                    )}
                    onClick={() => toggleGroupExpansion(group.dayKey)}
                    aria-expanded={isExpanded}
                    aria-label={`${isExpanded ? "Collapse" : "Expand"} ${group.activities.length} activities from ${group.dayKey}`}
                  >
                    {group.activities.length} Activities
                  </button>
                </div>
                {/* Bottom vertical line */}
                {showBottomBar && (
                  <div className="col-start-1 row-start-3 flex justify-center h-full">
                    <div className="w-1 h-full bg-black/30" />
                  </div>
                )}
                {/* Expanded activities with staggered animation */}
                <div
                  className={cn(
                    "col-start-2 row-start-3 col-span-1 space-y-1 overflow-hidden",
                    "transition-all duration-150 ease-out",
                    isExpanded
                      ? "max-h-96 opacity-100 mt-1 mb-2 ml-4"
                      : "max-h-0 opacity-0 mt-0",
                  )}
                >
                  {group.activities.map((activity, index) =>
                    children(activity, index),
                  )}
                </div>
              </li>
            );
          }
        })}
      </ul>

      {hiddenActivies.length > 0 && (
        <div className="flex justify-center pt-4">
          <button
            onClick={handleViewOlder}
            className={cn(
              "px-4 py-2 text-sm font-medium text-gray-700 bg-white",
              "border border-gray-300 rounded-md hover:bg-gray-50",
              "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
              "transition-colors duration-200",
            )}
          >
            View older
          </button>
        </div>
      )}
    </div>
  );
}

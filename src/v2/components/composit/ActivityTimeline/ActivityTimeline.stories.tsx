import { type Meta, type StoryFn } from "@storybook/nextjs";
import { ActivityTimeline } from "./ActivityTimeline";
import { mockEdits } from "./mockEdits";
import { ActivityTimelineItem } from "./ActivityTimelineItem";

export default {
    title: "Components/ActivityTimeline",
    component: ActivityTimeline,
    parameters: {
        layout: "fullscreen",
    },
} as Meta;

const Template: StoryFn<typeof ActivityTimeline<typeof mockEdits[number] & { timestamp: Date }>> = (args) => (
    <div style={{ maxWidth: "800px", margin: "0 auto", padding: "16px" }}>
        <ActivityTimeline data={args.data}>
            {activity => (
                <ActivityTimelineItem>
                    <div>
                        <h3>{activity.createdBy.name}</h3>
                        <p>{activity.content.valueBefore} → {activity.content.valueAfter}</p>
                    </div>
                </ActivityTimelineItem>
            )}
        </ActivityTimeline>
    </div>
);

export const Default = Template.bind({});
Default.args = {
    data: mockEdits.map(edit => ({
        ...edit,
        timestamp: edit.createdAt,
    })), // Mock edits data
};

export const NoEdits = Template.bind({});
NoEdits.args = {
    data: [], // Empty edits list
};

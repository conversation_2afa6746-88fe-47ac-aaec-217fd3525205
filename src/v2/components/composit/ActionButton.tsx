"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { Button } from "~/v2/components/ui/Button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "~/v2/components/ui/DropdownMenu";
import { type VariantProps } from "class-variance-authority";
import { buttonVariants } from "~/v2/components/ui/Button";

type VoidFunction = () => void;
type AsyncVoidFunction = () => Promise<void>;
export interface ActionOption {
    label: string;
    action: AsyncVoidFunction | VoidFunction;
    disabled?: boolean;
}

export interface ActionButtonProps extends VariantProps<typeof buttonVariants> {
    options: ActionOption[];
    disabled?: boolean;
    label?: string | null;
    className?: string;
    menuClassName?: string;
    size?: "default" | "sm" | "lg" | "icon";
    icon?: React.ReactNode;
}

export function ActionButton({
    options,
    disabled = false,
    label = "Actions",
    className,
    menuClassName,
    variant = "default",
    size = "default",
    icon,
}: ActionButtonProps) {
    const [open, setOpen] = useState(false);

    const handleSelect = (action: AsyncVoidFunction | VoidFunction, e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        e.preventDefault();
        void action();
        setOpen(false);
    };

    if (options.length === 1) {
        return <Button
            variant={variant}
            size={size}
            disabled={disabled}
            className={`flex items-center gap-2 select-none ${className || ""}`}
            onClick={(e: React.MouseEvent<HTMLElement>) => {
                if (options[0]?.action) {
                    handleSelect(options[0]?.action, e);
                }
            }}
        >
            {options[0]?.label}
        </Button>
    }

    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button
                    variant={variant}
                    size={size}
                    disabled={disabled}
                    className={`flex items-center gap-2 select-none ${className || ""}`}
                >
                    {label}
                    {icon ? icon : <ChevronDown className="h-4 w-4" />}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={menuClassName}>
                {options.map((option, index) => (
                    <DropdownMenuItem
                        key={index}
                        onClick={(e: React.MouseEvent<HTMLElement>) => handleSelect(option.action, e)}
                        disabled={disabled || option.disabled}
                    >
                        {option.label}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

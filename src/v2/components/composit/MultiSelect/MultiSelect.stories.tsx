import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import { useState } from "react";
import {
  MultiSelect,
  MultiSelectValues,
  MultiSelectTrigger,
  MultiSelectContent,
  MultiSelectCommand,
  MultiSelectCommandList,
  MultiSelectCommandItem,
  MultiSelectCommandEmpty,
  MultiSelectCommandGroup,
  MultiSelectClear,
  MultiSelectSeparator,
  MultiSelectList,
  MultiSelectItem,
  type Option,
} from "./MultiSelect";

const meta: Meta<typeof MultiSelect> = {
  title: "Components/MultiSelect",
  component: MultiSelect,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A flexible multi-select dropdown component that supports both command-based search and simple list selection patterns.",
      },
    },
  },
  argTypes: {
    options: {
      control: { type: "object" },
      description: "Array of options to display in the dropdown",
    },
    selected: {
      control: { type: "object" },
      description: "Array of selected option values",
    },
    onClose: {
      action: "closed",
      description: "Callback when selection is closed",
    },
    open: {
      control: { type: "boolean" },
      description: "Controlled open state",
    },
    variant: {
      control: { type: "select" },
      options: ["default", "minimal"],
      description: "Visual variant of the component",
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample options
const defaultOptions: Option[] = [
  { value: "react", label: "React" },
  { value: "vue", label: "Vue" },
  { value: "angular", label: "Angular" },
  { value: "svelte", label: "Svelte" },
  { value: "nextjs", label: "Next.js" },
  { value: "nuxt", label: "Nuxt" },
  { value: "gatsby", label: "Gatsby" },
  { value: "remix", label: "Remix" },
];

const optionsWithIcons: Option[] = [
  {
    value: "react",
    label: "React",
    icon: <span className="mr-2">⚛️</span>,
  },
  {
    value: "vue",
    label: "Vue",
    icon: <span className="mr-2">🟢</span>,
  },
  {
    value: "angular",
    label: "Angular",
    icon: <span className="mr-2">🔴</span>,
  },
  {
    value: "svelte",
    label: "Svelte",
    icon: <span className="mr-2">🟠</span>,
  },
];

const manyOptions: Option[] = [
  { value: "javascript", label: "JavaScript" },
  { value: "typescript", label: "TypeScript" },
  { value: "python", label: "Python" },
  { value: "java", label: "Java" },
  { value: "csharp", label: "C#" },
  { value: "cpp", label: "C++" },
  { value: "go", label: "Go" },
  { value: "rust", label: "Rust" },
  { value: "swift", label: "Swift" },
  { value: "kotlin", label: "Kotlin" },
  { value: "php", label: "PHP" },
  { value: "ruby", label: "Ruby" },
  { value: "scala", label: "Scala" },
  { value: "elixir", label: "Elixir" },
  { value: "clojure", label: "Clojure" },
];

const categorizedOptions: Record<string, Option[]> = {
  "Frontend Frameworks": [
    { value: "react", label: "React" },
    { value: "vue", label: "Vue" },
    { value: "angular", label: "Angular" },
  ],
  "Backend Frameworks": [
    { value: "django", label: "Django" },
    { value: "flask", label: "Flask" },
    { value: "express", label: "Express" },
    { value: "ruby-on-rails", label: "Ruby on Rails" },
  ],
};

// Interactive wrapper for command-based MultiSelect
const CommandMultiSelectWrapper = ({
  options,
  ...props
}: {
  options: Option[];
}) => {
  const [selected, setSelected] = useState<string[]>([]);

  return (
    <MultiSelect
      options={options}
      selected={selected}
      onClose={setSelected}
      {...props}
    >
      <MultiSelectTrigger>
        <MultiSelectValues placeholder="Select frameworks..." />
      </MultiSelectTrigger>
      <MultiSelectContent>
        <MultiSelectCommand>
          <MultiSelectCommandList>
            {options.map((option: Option) => (
              <MultiSelectCommandItem key={option.value} value={option.value}>
                {option.icon}
                {option.label}
              </MultiSelectCommandItem>
            ))}
          </MultiSelectCommandList>
          <MultiSelectCommandEmpty>
            No frameworks found.
          </MultiSelectCommandEmpty>
          <MultiSelectClear />
        </MultiSelectCommand>
      </MultiSelectContent>
    </MultiSelect>
  );
};

// Interactive wrapper for list-based MultiSelect
const ListMultiSelectWrapper = ({
  options,
  ...props
}: {
  options: Option[];
}) => {
  const [selected, setSelected] = useState<string[]>([]);

  return (
    <MultiSelect
      options={options}
      selected={selected}
      onClose={setSelected}
      {...props}
    >
      <MultiSelectTrigger>
        <MultiSelectValues placeholder="Select frameworks..." />
      </MultiSelectTrigger>
      <MultiSelectContent>
        <MultiSelectList>
          {options.map((option: Option) => (
            <MultiSelectItem key={option.value} value={option.value}>
              {option.icon}
              {option.label}
            </MultiSelectItem>
          ))}
        </MultiSelectList>
      </MultiSelectContent>
    </MultiSelect>
  );
};

export const Default: Story = {
  render: () => <CommandMultiSelectWrapper options={defaultOptions} />,
  parameters: {
    docs: {
      description: {
        story: "Default MultiSelect with command-based search functionality.",
      },
    },
  },
};

export const WithIcons: Story = {
  render: () => <CommandMultiSelectWrapper options={optionsWithIcons} />,
  parameters: {
    docs: {
      description: {
        story: "MultiSelect with icons for each option.",
      },
    },
  },
};

export const MinimalVariant: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>([]);

    return (
      <MultiSelect
        options={defaultOptions}
        selected={selected}
        onClose={setSelected}
        variant="minimal"
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              {defaultOptions.map((option) => (
                <MultiSelectCommandItem key={option.value} value={option.value}>
                  {option.label}
                </MultiSelectCommandItem>
              ))}
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
            <MultiSelectClear />
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Minimal variant with compact display showing only the first selected item and a count badge.",
      },
    },
  },
};

export const ManyOptions: Story = {
  render: () => <CommandMultiSelectWrapper options={manyOptions} />,
  parameters: {
    docs: {
      description: {
        story:
          "MultiSelect with many options, demonstrating search functionality.",
      },
    },
  },
};

export const WithGroups: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>([]);

    const frontendOptions = defaultOptions.filter((opt) =>
      ["react", "vue", "angular", "svelte"].includes(opt.value),
    );
    const backendOptions = defaultOptions.filter((opt) =>
      ["nextjs", "nuxt", "gatsby", "remix"].includes(opt.value),
    );

    return (
      <MultiSelect
        options={defaultOptions}
        selected={selected}
        onClose={setSelected}
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              <MultiSelectCommandGroup heading="Frontend Frameworks">
                {frontendOptions.map((option) => (
                  <MultiSelectCommandItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </MultiSelectCommandItem>
                ))}
              </MultiSelectCommandGroup>
              <MultiSelectCommandGroup heading="Full-Stack Frameworks">
                {backendOptions.map((option) => (
                  <MultiSelectCommandItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </MultiSelectCommandItem>
                ))}
              </MultiSelectCommandGroup>
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
            <MultiSelectClear />
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "MultiSelect with grouped options for better organization.",
      },
    },
  },
};

export const WithoutClearButton: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>([]);

    return (
      <MultiSelect
        options={defaultOptions}
        selected={selected}
        onClose={setSelected}
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              {defaultOptions.map((option) => (
                <MultiSelectCommandItem key={option.value} value={option.value}>
                  {option.label}
                </MultiSelectCommandItem>
              ))}
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "MultiSelect without a clear button to remove all selections.",
      },
    },
  },
};

export const ListBased: Story = {
  render: () => <ListMultiSelectWrapper options={defaultOptions} />,
  parameters: {
    docs: {
      description: {
        story: "Simple list-based MultiSelect without search functionality.",
      },
    },
  },
};

export const WithSeparators: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>([]);

    const frontendOptions = [
      { value: "react", label: "React" },
      { value: "vue", label: "Vue" },
      { value: "angular", label: "Angular" },
      { value: "svelte", label: "Svelte" },
    ];
    const fullStackOptions = [
      { value: "nextjs", label: "Next.js" },
      { value: "nuxt", label: "Nuxt" },
      { value: "gatsby", label: "Gatsby" },
      { value: "remix", label: "Remix" },
    ];
    const backendOptions = [
      { value: "django", label: "Django" },
      { value: "flask", label: "Flask" },
      { value: "express", label: "Express" },
      { value: "ruby-on-rails", label: "Ruby on Rails" },
    ];

    return (
      <MultiSelect
        options={[...frontendOptions, ...fullStackOptions, ...backendOptions]}
        selected={selected}
        onClose={setSelected}
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              <MultiSelectCommandGroup heading="Frontend Frameworks">
                {frontendOptions.map((option) => (
                  <MultiSelectCommandItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </MultiSelectCommandItem>
                ))}
              </MultiSelectCommandGroup>
              <MultiSelectSeparator />
              <MultiSelectCommandGroup heading="Backend Frameworks">
                {backendOptions.map((option) => (
                  <MultiSelectCommandItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </MultiSelectCommandItem>
                ))}
              </MultiSelectCommandGroup>
              <MultiSelectSeparator />
              <MultiSelectCommandGroup heading="Full-Stack Frameworks">
                {fullStackOptions.map((option) => (
                  <MultiSelectCommandItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </MultiSelectCommandItem>
                ))}
              </MultiSelectCommandGroup>
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
            <MultiSelectClear />
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "MultiSelect with separators between groups.",
      },
    },
  },
};

export const PreSelected: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>(["react", "vue"]);

    return (
      <MultiSelect
        options={defaultOptions}
        selected={selected}
        onClose={setSelected}
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              {defaultOptions.map((option) => (
                <MultiSelectCommandItem key={option.value} value={option.value}>
                  {option.label}
                </MultiSelectCommandItem>
              ))}
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "MultiSelect with pre-selected options.",
      },
    },
  },
};

export const Controlled: Story = {
  render: () => {
    const [selected, setSelected] = useState<string[]>([]);
    const [open, setOpen] = useState(false);

    return (
      <MultiSelect
        options={defaultOptions}
        selected={selected}
        onClose={setSelected}
        open={open}
      >
        <MultiSelectTrigger>
          <MultiSelectValues placeholder="Select frameworks..." />
        </MultiSelectTrigger>
        <MultiSelectContent>
          <MultiSelectCommand>
            <MultiSelectCommandList>
              {defaultOptions.map((option) => (
                <MultiSelectCommandItem key={option.value} value={option.value}>
                  {option.label}
                </MultiSelectCommandItem>
              ))}
            </MultiSelectCommandList>
            <MultiSelectCommandEmpty>
              No frameworks found.
            </MultiSelectCommandEmpty>
          </MultiSelectCommand>
        </MultiSelectContent>
      </MultiSelect>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "Controlled MultiSelect with external open state management.",
      },
    },
  },
};

export const MultipleSelects: Story = {
  render: () => {
    const [selectedFunds, setSelectedFunds] = useState<string[]>([]);
    const [selectedDDQs, setSelectedDDQs] = useState<string[]>([]);

    const fundOptions: Option[] = [
      { value: "cm846hloc001rqzpaam1s5xp5", label: "Vistria Housing Fund" },
      { value: "cm846hosq001vqzpaz3nu35tt", label: "Green Forrest Fund" },
      { value: "cm846hqdz001zqzpaeykicyy9", label: "CCP VII" },
      { value: "cm846hqdz0020qzpa5wd65ei5", label: "CCP VI" },
      { value: "cm846hqdz0021qzpato3jjju9", label: "CCP V" },
    ];

    const ddqOptions: Option[] = [
      {
        value: "cmd4sd1zi00011xph1bgqcvlw",
        label: "Clearlake Capital Partners VIII Standard DDQ",
      },
      {
        value: "cmd4sd1zi001i1xpht5q9plce",
        label: "PE DD request BLANK",
      },
      {
        value: "cmd4sd1zi001l1xphrny0w3f5",
        label: "DD Request not found test",
      },
      {
        value: "cmd4sd1zi001m1xph0jjrjzmx",
        label: "DD Request",
      },
      {
        value: "cmd4sd1zi001n1xphjxoyj349",
        label: "Gilad Test Hamilton Lane",
      },
    ];

    return (
      <div className="space-y-4 w-full max-w-4xl p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Funds</label>
            <MultiSelect
              options={fundOptions}
              selected={selectedFunds}
              onClose={setSelectedFunds}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select funds..." />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {fundOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectCommandEmpty>
                    No funds found.
                  </MultiSelectCommandEmpty>
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">DDQs</label>
            <MultiSelect
              options={ddqOptions}
              selected={selectedDDQs}
              onClose={setSelectedDDQs}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select DDQs..." />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {ddqOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectCommandEmpty>
                    No DDQs found.
                  </MultiSelectCommandEmpty>
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
          </div>
        </div>

        <div className="mt-6 p-4 bg-white rounded-lg border">
          <h4 className="text-sm font-medium mb-2">Selected Items Summary:</h4>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Funds:</strong>{" "}
              {selectedFunds.length > 0
                ? selectedFunds.join(", ")
                : "None selected"}
            </div>
            <div>
              <strong>DDQs:</strong>{" "}
              {selectedDDQs.length > 0
                ? selectedDDQs.join(", ")
                : "None selected"}
            </div>
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Multiple MultiSelect components working together in a form layout.",
      },
    },
  },
};

export const AllVariants: Story = {
  render: () => {
    const [selected1, setSelected1] = useState<string[]>([]);
    const [selected2, setSelected2] = useState<string[]>([]);
    const [selected3, setSelected3] = useState<string[]>([]);

    return (
      <div className="space-y-6 w-full max-w-md">
        <div>
          <h3 className="text-sm font-medium mb-2">Default Variant</h3>
          <MultiSelect
            options={defaultOptions}
            selected={selected1}
            onClose={setSelected1}
          >
            <MultiSelectTrigger>
              <MultiSelectValues placeholder="Select frameworks..." />
            </MultiSelectTrigger>
            <MultiSelectContent>
              <MultiSelectCommand>
                <MultiSelectCommandList>
                  {defaultOptions.map((option) => (
                    <MultiSelectCommandItem
                      key={option.value}
                      value={option.value}
                    >
                      {option.label}
                    </MultiSelectCommandItem>
                  ))}
                </MultiSelectCommandList>
                <MultiSelectCommandEmpty>
                  No frameworks found.
                </MultiSelectCommandEmpty>
                <MultiSelectClear />
              </MultiSelectCommand>
            </MultiSelectContent>
          </MultiSelect>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Minimal Variant</h3>
          <MultiSelect
            options={defaultOptions}
            selected={selected2}
            onClose={setSelected2}
            variant="minimal"
          >
            <MultiSelectTrigger>
              <MultiSelectValues placeholder="Select frameworks..." />
            </MultiSelectTrigger>
            <MultiSelectContent>
              <MultiSelectCommand>
                <MultiSelectCommandList>
                  {defaultOptions.map((option) => (
                    <MultiSelectCommandItem
                      key={option.value}
                      value={option.value}
                    >
                      {option.label}
                    </MultiSelectCommandItem>
                  ))}
                </MultiSelectCommandList>
                <MultiSelectCommandEmpty>
                  No frameworks found.
                </MultiSelectCommandEmpty>
                <MultiSelectClear />
              </MultiSelectCommand>
            </MultiSelectContent>
          </MultiSelect>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">With Icons</h3>
          <MultiSelect
            options={optionsWithIcons}
            selected={selected3}
            onClose={setSelected3}
          >
            <MultiSelectTrigger>
              <MultiSelectValues placeholder="Select frameworks..." />
            </MultiSelectTrigger>
            <MultiSelectContent>
              <MultiSelectCommand>
                <MultiSelectCommandList>
                  {optionsWithIcons.map((option) => (
                    <MultiSelectCommandItem
                      key={option.value}
                      value={option.value}
                    >
                      {option.icon}
                      {option.label}
                    </MultiSelectCommandItem>
                  ))}
                </MultiSelectCommandList>
                <MultiSelectCommandEmpty>
                  No frameworks found.
                </MultiSelectCommandEmpty>
                <MultiSelectClear />
              </MultiSelectCommand>
            </MultiSelectContent>
          </MultiSelect>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Comparison of different MultiSelect variants and configurations.",
      },
    },
  },
};

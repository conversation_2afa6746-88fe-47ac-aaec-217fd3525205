import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { ActionButton, type ActionOption } from "./ActionButton";

const meta: Meta<typeof ActionButton> = {
    title: "Components/ActionButton",
    component: ActionButton,
    parameters: {
        layout: "centered",
        docs: {
            description: {
                component: "A dropdown button component that displays a list of actions when clicked.",
            },
        },
    },
    argTypes: {
        variant: {
            control: { type: "select" },
            options: ["default", "destructive", "outline", "secondary", "ghost", "link"],
            description: "The visual style variant of the button",
        },
        size: {
            control: { type: "select" },
            options: ["default", "sm", "lg", "icon"],
            description: "The size of the button",
        },
        disabled: {
            control: { type: "boolean" },
            description: "Whether the button is disabled",
        },
        label: {
            control: { type: "text" },
            description: "The text displayed on the button",
        },
        className: {
            control: { type: "text" },
            description: "Additional CSS classes for the button",
        },
        menuClassName: {
            control: { type: "text" },
            description: "Additional CSS classes for the dropdown menu",
        },
    },
    tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample action handlers
const handleAction1 = () => {
    console.log("Action 1 executed");
};

const handleAction2 = () => {
    console.log("Action 2 executed");
};

const handleAction3 = () => {
    console.log("Action 3 executed");
};

const handleAsyncAction = async () => {
    console.log("Async action started");
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log("Async action completed");
};

// Sample options
const defaultOptions: ActionOption[] = [
    {
        label: "Action 1",
        action: handleAction1,
    },
    {
        label: "Action 2",
        action: handleAction2,
    },
    {
        label: "Disabled Action",
        action: handleAction3,
        disabled: true,
    },
];

const asyncOptions: ActionOption[] = [
    {
        label: "Sync Action",
        action: handleAction1,
    },
    {
        label: "Async Action",
        action: handleAsyncAction,
    },
];

const manyOptions: ActionOption[] = [
    { label: "Edit", action: handleAction1 },
    { label: "Copy", action: handleAction2 },
    { label: "Delete", action: handleAction3 },
    { label: "Share", action: handleAction1 },
    { label: "Export", action: handleAction2 },
    { label: "Archive", action: handleAction3 },
    { label: "Settings", action: handleAction1 },
];

export const Default: Story = {
    args: {
        options: defaultOptions,
        label: "Actions",
        variant: "default",
        size: "default",
    },
};

export const Outline: Story = {
    args: {
        options: defaultOptions,
        label: "Outline Actions",
        variant: "outline",
    },
};

export const Secondary: Story = {
    args: {
        options: defaultOptions,
        label: "Secondary Actions",
        variant: "secondary",
    },
};

export const Small: Story = {
    args: {
        options: defaultOptions,
        label: "Small Actions",
        variant: "default",
        size: "sm",
    },
};

export const Large: Story = {
    args: {
        options: defaultOptions,
        label: "Large Actions",
        variant: "default",
        size: "lg",
    },
};

export const Disabled: Story = {
    args: {
        options: defaultOptions,
        label: "Disabled Actions",
        variant: "default",
        disabled: true,
    },
};

export const WithAsyncActions: Story = {
    args: {
        options: asyncOptions,
        label: "Async Actions",
        variant: "default",
    },
};

export const ManyOptions: Story = {
    args: {
        options: manyOptions,
        label: "Many Actions",
        variant: "default",
    },
};

export const Destructive: Story = {
    args: {
        options: [
            { label: "Delete", action: handleAction3 },
            { label: "Archive", action: handleAction2 },
        ],
        label: "Destructive Actions",
        variant: "destructive",
    },
};

export const Ghost: Story = {
    args: {
        options: defaultOptions,
        label: "Ghost Actions",
        variant: "ghost",
    },
};

export const Link: Story = {
    args: {
        options: defaultOptions,
        label: "Link Actions",
        variant: "link",
    },
};

// Comprehensive example showing all variants in a layout
export const AllVariants: Story = {
    render: () => {
        const handleAction1 = () => {
            console.log("Action 1 executed");
        };

        const handleAction2 = () => {
            console.log("Action 2 executed");
        };

        const handleAction3 = () => {
            console.log("Action 3 executed");
        };

        const options: ActionOption[] = [
            {
                label: "Action 1",
                action: handleAction1,
            },
            {
                label: "Action 2",
                action: handleAction2,
            },
            {
                label: "Disabled Action",
                action: handleAction3,
                disabled: true,
            },
        ];

        return (
            <div className="space-y-4">
                {/* Default ActionButton */}
                <ActionButton
                    options={options}
                    label="Default Actions"
                    variant="default"
                />

                {/* Outline variant */}
                <ActionButton
                    options={options}
                    label="Outline Actions"
                    variant="outline"
                />

                {/* Secondary variant */}
                <ActionButton
                    options={options}
                    label="Secondary Actions"
                    variant="secondary"
                />

                {/* Small size */}
                <ActionButton
                    options={options}
                    label="Small Actions"
                    variant="default"
                    size="sm"
                />

                {/* Large size */}
                <ActionButton
                    options={options}
                    label="Large Actions"
                    variant="default"
                    size="lg"
                />

                {/* Disabled state */}
                <ActionButton
                    options={options}
                    label="Disabled Actions"
                    variant="default"
                    disabled={true}
                />

                {/* Destructive variant */}
                <ActionButton
                    options={[
                        { label: "Delete", action: handleAction3 },
                        { label: "Archive", action: handleAction2 },
                    ]}
                    label="Destructive Actions"
                    variant="destructive"
                />

                {/* Ghost variant */}
                <ActionButton
                    options={options}
                    label="Ghost Actions"
                    variant="ghost"
                />

                {/* Link variant */}
                <ActionButton
                    options={options}
                    label="Link Actions"
                    variant="link"
                />
            </div>
        );
    },
    parameters: {
        docs: {
            description: {
                story: "This example shows all the different variants and sizes of the ActionButton component in a single layout.",
            },
        },
    },
}; 
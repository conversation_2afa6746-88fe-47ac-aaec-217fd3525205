# ActionButton Component

A reusable dropdown action button component that provides a clean interface for multiple actions.

## Features

- **Configurable Options**: Accepts an array of action options with labels and callbacks
- **Flexible Styling**: Supports all button variants (default, outline, secondary, etc.)
- **Size Options**: Multiple size variants (default, sm, lg, icon)
- **Disabled State**: Can be disabled globally or per option
- **Accessible**: Built with Radix UI primitives for accessibility

## Usage

```tsx
import {
  ActionButton,
  type ActionOption,
} from "~/v2/components/composit/ActionButton";

const options: ActionOption[] = [
  {
    label: "Edit",
    action: () => console.log("Edit clicked"),
  },
  {
    label: "Delete",
    action: () => console.log("Delete clicked"),
  },
  {
    label: "Disabled Action",
    action: () => console.log("This won't execute"),
    disabled: true,
  },
];

<ActionButton
  options={options}
  label="Actions"
  variant="default"
  disabled={false}
/>;
```

## Props

### ActionButtonProps

| Prop        | Type                                                                          | Default     | Description                    |
| ----------- | ----------------------------------------------------------------------------- | ----------- | ------------------------------ |
| `options`   | `ActionOption[]`                                                              | -           | Array of action options        |
| `disabled`  | `boolean`                                                                     | `false`     | Whether the button is disabled |
| `label`     | `string`                                                                      | `"Actions"` | Button label text              |
| `className` | `string`                                                                      | -           | Additional CSS classes         |
| `variant`   | `"default" \| "destructive" \| "outline" \| "secondary" \| "ghost" \| "link"` | `"default"` | Button variant                 |
| `size`      | `"default" \| "sm" \| "lg" \| "icon"`                                         | `"default"` | Button size                    |

### ActionOption

| Prop       | Type         | Default | Description                                |
| ---------- | ------------ | ------- | ------------------------------------------ |
| `label`    | `string`     | -       | Display text for the option                |
| `action`   | `() => void` | -       | Function to execute when option is clicked |
| `disabled` | `boolean`    | `false` | Whether this specific option is disabled   |

## Examples

### Basic Usage

```tsx
<ActionButton
  options={[
    { label: "Save", action: handleSave },
    { label: "Cancel", action: handleCancel },
  ]}
  label="File Actions"
/>
```

### With Different Variants

```tsx
<ActionButton
  options={options}
  label="Danger Actions"
  variant="destructive"
  size="sm"
/>
```

### Disabled State

```tsx
<ActionButton options={options} label="Actions" disabled={isLoading} />
```

### Individual Option Disabled

```tsx
const options = [
  { label: "Edit", action: handleEdit },
  { label: "Delete", action: handleDelete, disabled: !canDelete },
];
```

## Migration from ProcessActionButtons

The `ProcessActionButtons` component has been refactored to use the new `ActionButton` component. The functionality remains the same, but now uses the reusable component internally.

"use client";

import { Protect } from "@clerk/clerk-react";
import type { MenuItemWithPermissionType } from "~/layouts/config-nav-dashboard";
import { MenuItem } from "./MenuItem";

type MenuItemWithPermissionProps = {
    item: MenuItemWithPermissionType;
    isActive: boolean;
    isSubMenu?: string;
};

export function MenuItemWithPermission({ item, ...props }: MenuItemWithPermissionProps) {
    if (item.permission) {
        return (
            <Protect permission={item.permission} fallback={<></>}>
                <MenuItem item={item} {...props} />
            </Protect>
        );
    }
    return <MenuItem item={item} {...props} />;
}
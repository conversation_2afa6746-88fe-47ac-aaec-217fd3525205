"use client";

import Link from "next/link";
import {
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "~/v2/components/ui/Sidebar";
import type { MenuItemWithPermissionType } from "~/layouts/config-nav-dashboard";

type MenuItemProps = {
  item: MenuItemWithPermissionType;
  isActive: boolean;
  isSubMenu?: string;
};

export function MenuItem({ item, isActive, isSubMenu }: MenuItemProps) {
  const linkContent = (
    <Link
      href={isSubMenu ? `${isSubMenu}/${item.url}` : item.url}
      data-active={isActive}
      className="select-none"
    >
      <item.icon />
      <span>{item.title}</span>
    </Link>
  );

  if (isSubMenu) {
    return (
      <SidebarMenuSubItem>
        <SidebarMenuSubButton
          asChild
          className="data-[active=true]:bg-sidebar-active data-[active=true]:font-medium rounded-[8px]"
        >
          {linkContent}
        </SidebarMenuSubButton>
      </SidebarMenuSubItem>
    );
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        className="data-[active=true]:bg-sidebar-active data-[active=true]:font-medium rounded-[8px]"
      >
        {linkContent}
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

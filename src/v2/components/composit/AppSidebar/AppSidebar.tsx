"use client";

import { useClerk } from "@clerk/clerk-react";
import { ChevronDown, Expand, Minimize2 } from "lucide-react";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useOrg } from "~/app/providers/OrgProvider";
import { useLocalStorage } from "~/hooks/use-local-storage";
import {
  navData as dashboardNavData,
  MenuItemWithPermissionType,
} from "~/layouts/config-nav-dashboard";
import Logo from "~/v2/components/icons/Logo";
import LogoMini from "~/v2/components/icons/LogoMini";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  useSidebar,
} from "~/v2/components/ui/Sidebar";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { cn } from "~/v2/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../ui/DropdownMenu";
import Image from "next/image";
import { MenuItemWithPermission } from "./MenuItemWithPermission";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../../ui/Collapsible";

const DEFAULT_SIDEBAR_LOCKED = true;

export function AppSidebar() {
  const { open, setOpen } = useSidebar();
  const { orgName, orgLogo } = useOrg();

  const pathname = usePathname();
  const { state: sidebarLockedStorage, setState: setSidebarLockedStorage } =
    useLocalStorage<boolean>("sidebar-locked", DEFAULT_SIDEBAR_LOCKED);
  const [isLocked, setIsLocked] = useState(
    sidebarLockedStorage ?? DEFAULT_SIDEBAR_LOCKED,
  );
  const [isHovering, setIsHovering] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);

  useEffect(() => {
    setIsLocked(sidebarLockedStorage);
  }, [sidebarLockedStorage]);

  // Initialize sidebar state based on lock status
  useEffect(() => {
    if (isLocked) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [isLocked, setOpen]);

  // Handle hover behavior
  useEffect(() => {
    if (menuOpen || isLocked) return;

    if (isHovering) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [isHovering, isLocked, menuOpen, setOpen]);

  // Handle lock/unlock
  const handleLockToggle = () => {
    if (isLocked) {
      // Unlock: collapse sidebar and return to hover mode
      setIsLocked(false);
      setSidebarLockedStorage(false);
      setOpen(false);
    } else {
      // Lock: keep sidebar expanded
      setIsLocked(true);
      setSidebarLockedStorage(true);
      setOpen(true);
    }
  };

  const lockButton = open && (
    <button
      onClick={handleLockToggle}
      className="absolute top-1 right-1 w-6 h-6 text-sidebar-foreground flex items-center justify-center rounded-full bg-transparent hover:cursor-pointer transition z-100 p-1"
      aria-label={isLocked ? "Unlock sidebar" : "Lock sidebar"}
    >
      {isLocked ? <Minimize2 /> : <Expand />}
    </button>
  );

  const clerk = useClerk();

  return (
    <Sidebar
      collapsible="icon"
      variant={isLocked ? "sidebar" : "hover"}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      className={`${overrideMuiTheme} p-2 pt-3 bg-sidebar`}
    >
      <SidebarHeader className="pb-6">
        <div className="relative w-full h-10 flex items-center pb-9 pt-6">
          <div className="relative flex items-center w-full">
            {/* Large Logo */}
            <div
              className={cn(
                "absolute left-0 top-0 flex items-center",
                "pt-2 pb-3",
                "transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
                !open && "left-[-8px]",
                open
                  ? "opacity-100 pointer-events-auto"
                  : "opacity-0 pointer-events-none",
              )}
            >
              <Logo className="h-7" />
            </div>
            {/* Mini Logo, superimposed and scales down when collapsed */}
            <div
              className={cn(
                "absolute left-[1px] top-[0.5px] flex items-center justify-center",
                "pt-2 pb-3",
                "transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
                !open && "left-[-8px]",
              )}
            >
              <LogoMini className="w-8 h-7" />
            </div>
          </div>
        </div>
        <div className="absolute top-3 right-2">
          <div
            className={cn(
              "transition-opacity duration-300",
              "ease-[cubic-bezier(0.4,0,0.2,1)]",
              open
                ? "opacity-100 delay-100"
                : "opacity-0 pointer-events-none delay-0",
            )}
          >
            {lockButton}
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {dashboardNavData.map((item: MenuItemWithPermissionType) =>
            item.items?.length ? (
              <Collapsible
                className="group/collapsible"
                key={item.title}
                defaultOpen={pathname.includes(item.url)}
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      asChild
                      className="data-[active=true]:bg-sidebar-active data-[active=true]:font-medium rounded-[8px] cursor-pointer select-none"
                    >
                      <div>
                        <item.icon />
                        <span>{item.title}</span>
                        <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                      </div>
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pl-4">
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <MenuItemWithPermission
                          key={subItem.title}
                          item={subItem}
                          isActive={pathname === `${item.url}/${subItem.url}`}
                          isSubMenu={item.url}
                        />
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ) : (
              <MenuItemWithPermission
                key={item.title}
                item={item}
                isActive={pathname === item.url}
              />
            ),
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu
              onOpenChange={(open) => {
                if (open) {
                  setMenuOpen(true);
                } else {
                  setMenuOpen(false);
                }
              }}
            >
              <SidebarMenuButton
                asChild
                className="data-[active=true]:bg-sidebar-active data-[active=true]:font-medium rounded-[8px] p-0 w-full cursor-pointer"
              >
                <DropdownMenuTrigger className="w-full">
                  {orgLogo && (
                    <Image
                      src={orgLogo}
                      alt={orgName ?? "Organization"}
                      className="w-8 h-8 rounded-[8px]"
                      width={32}
                      height={32}
                    />
                  )}
                  <span className="font-bold">{orgName ?? "Organization"}</span>
                </DropdownMenuTrigger>
              </SidebarMenuButton>
              <DropdownMenuContent align="start">
                <DropdownMenuItem
                  onClick={() => clerk.signOut()}
                  className="text-red-500"
                >
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}

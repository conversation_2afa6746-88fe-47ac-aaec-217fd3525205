import { type IconProps } from "./types";

export default function LogoMini(props: IconProps) {
  const { className } = props;
  return (
    <svg
      width={32}
      height={28}
      className={className}
      viewBox="0 0 32 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.0625 0.25H8.00688C8.8118 0.250078 9.6025 0.461876 10.2997 0.864141C10.997 1.26633 11.5761 1.84477 11.9791 2.54156L19.9523 16.352C20.3545 17.0498 20.5662 17.8412 20.5662 18.6466C20.5662 19.452 20.3545 20.2433 19.9523 20.9411L15.9801 27.8218L0.0625 0.25Z"
        fill="#11363F"
      />
      <path
        d="M31.8976 0.25L25.8932 10.6504C25.837 10.7483 25.7522 10.8267 25.6502 10.875C25.5482 10.9234 25.4339 10.9393 25.3225 10.9208C25.2111 10.9023 25.1081 10.8502 25.0271 10.7714C24.9463 10.6927 24.8914 10.591 24.8699 10.4802L23.737 4.68633C23.7096 4.55836 23.6395 4.44336 23.5382 4.36055L19.638 1.23289C19.5495 1.16094 19.4853 1.06352 19.4544 0.953751C19.4234 0.843985 19.4271 0.727344 19.465 0.619766C19.5028 0.512188 19.573 0.418829 19.6659 0.352657C19.7588 0.286485 19.8699 0.250703 19.9839 0.25H31.8976Z"
        fill="#5E8779"
      />
    </svg>
  );
}

import { type IconProps } from "./types";

export default function Logo(props: IconProps) {
  const { className } = props;
  return (
    <svg
      width={92}
      height={29}
      className={className}
      viewBox="0 0 92 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M32.7656 11.575H35.7672V28.1376H32.7656V11.575Z"
        fill="#11363F"
      />
      <path
        d="M45.0151 23.3783H42.5474V28.1382H39.546V11.5755H46.6584C47.7204 11.5604 48.7671 11.8339 49.6922 12.3681C50.5661 12.8675 51.2892 13.6037 51.7823 14.4958C52.277 15.4035 52.5309 16.428 52.5189 17.4676C52.5351 18.7065 52.1661 19.9183 51.465 20.9278C50.7639 21.9372 49.7673 22.6914 48.6196 23.0812L53.2231 28.1382H49.255L45.0151 23.3783ZM46.2673 20.7369C46.7008 20.7524 47.1327 20.6766 47.5366 20.5144C47.9404 20.3521 48.3074 20.1068 48.615 19.7933C48.915 19.4895 49.1514 19.126 49.3096 18.7252C49.468 18.3247 49.5448 17.8951 49.5358 17.4631C49.545 17.0337 49.4682 16.6066 49.3098 16.2091C49.1514 15.8114 48.9148 15.4515 48.615 15.1514C48.3069 14.8374 47.9391 14.5918 47.5345 14.4294C47.1298 14.2673 46.6969 14.1916 46.2627 14.2079H42.543V20.7132L46.2673 20.7369Z"
        fill="#11363F"
      />
      <path
        d="M68.4144 16.5437C67.9758 15.6583 67.3108 14.9114 66.4902 14.3832C65.3684 13.7367 64.0751 13.4729 62.7971 13.6297C61.5194 13.7866 60.3234 14.3563 59.3825 15.256C58.8576 15.8301 58.4491 16.5051 58.1804 17.2418C57.9118 17.9788 57.7884 18.7629 57.8171 19.5488C57.7804 20.3483 57.8992 21.1475 58.1663 21.8999C58.4334 22.652 58.8437 23.3426 59.3732 23.9312C59.9075 24.4827 60.5483 24.9138 61.2545 25.1974C61.9608 25.4809 62.717 25.6102 63.4749 25.5774C64.6794 25.6254 65.8679 25.2838 66.8724 24.6011C67.7593 23.9792 68.4098 23.0628 68.7139 22.0065H62.591V19.478H71.9502V19.5251C71.9727 21.0552 71.6163 22.5661 70.9143 23.9168C70.2326 25.2167 69.2066 26.2928 67.9541 27.021C66.5999 27.7982 65.0682 28.1922 63.5163 28.1626C62.3019 28.1738 61.0968 27.9448 59.9671 27.4882C58.9318 27.0778 57.991 26.4512 57.2048 25.6482C56.4528 24.8613 55.8599 23.9301 55.4601 22.9075C54.6191 20.7415 54.6191 18.3276 55.4601 16.1616C55.8599 15.1405 56.4528 14.2108 57.2048 13.4257C57.9921 12.6286 58.9327 12.0082 59.9671 11.6046C61.0979 11.1533 62.3026 10.9276 63.5163 10.9397C65.2766 10.8906 67.0075 11.4085 68.4651 12.4207C69.8586 13.4314 70.8941 14.8782 71.416 16.5437H68.4144Z"
        fill="#11363F"
      />
      <path
        d="M74.8741 11.575H77.8802V28.1376H74.8832L74.8741 11.575Z"
        fill="#11363F"
      />
      <path
        d="M81.6375 11.575H84.6342V25.4442H92V28.1376H81.6419L81.6375 11.575Z"
        fill="#11363F"
      />
      <path
        d="M0.747192 0.835938H8.42958C9.20876 0.836506 9.97406 1.04702 10.6489 1.44623C11.3236 1.84526 11.8841 2.41915 12.2742 3.11024L19.9838 16.7974C20.3728 17.4877 20.5777 18.271 20.5777 19.0683C20.5777 19.8654 20.3728 20.6487 19.9838 21.339L16.1394 28.1617L0.747192 0.835938Z"
        fill="#11363F"
      />
      <path
        d="M31.5382 0.835938L25.727 11.1437C25.6726 11.2397 25.5909 11.3165 25.493 11.3637C25.3951 11.4109 25.2852 11.4265 25.1784 11.4081C25.0716 11.3899 24.973 11.3388 24.8952 11.2617C24.8175 11.1843 24.7644 11.0845 24.7435 10.9757L23.6441 5.23738C23.618 5.10898 23.5507 4.99348 23.4529 4.90871L19.6835 1.81587C19.5981 1.74399 19.5365 1.6467 19.5067 1.53784C19.4769 1.42879 19.4804 1.3131 19.5171 1.20633C19.5535 1.09955 19.6211 1.00662 19.7107 0.940435C19.8004 0.874246 19.9076 0.837644 20.0181 0.835938H31.5382Z"
        fill="#5E8779"
      />
    </svg>
  );
}

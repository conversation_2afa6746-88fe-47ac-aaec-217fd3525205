import { useState } from "react";
import { api } from "~/trpc/react";

export const useDeleteCategory = () => {
    const [deleting, setDeleting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [pendingDelete, setPendingDelete] = useState<{
        categoryId: string;
        categoryName: string;
        hasChildren: boolean;
    } | null>(null);

    const deleteCategory = api.category.deleteCategory.useMutation();
    const utils = api.useUtils?.() || {};

    const confirmDelete = (categoryId: string, categoryName: string, hasChildren: boolean) => {
        if (hasChildren) {
            setError("Cannot delete category that has children");
            return;
        }

        setPendingDelete({ categoryId, categoryName, hasChildren });
        setConfirmOpen(true);
    };

    const handleDelete = async () => {
        if (!pendingDelete) return;

        setDeleting(true);
        setError(null);
        setConfirmOpen(false);

        try {
            await deleteCategory.mutateAsync({ id: pendingDelete.categoryId });

            // Invalidate queries to refresh the data
            if (utils.category?.getCategoryTreeWithDocuments?.invalidate) {
                utils.category.getCategoryTreeWithDocuments.invalidate();
            }

            setPendingDelete(null);
            return true;
        } catch (err: any) {
            setError(err.message || "Error deleting category");
            return false;
        } finally {
            setDeleting(false);
        }
    };

    const cancelDelete = () => {
        setConfirmOpen(false);
        setPendingDelete(null);
        setError(null);
    };

    return {
        confirmDelete,
        handleDelete,
        cancelDelete,
        deleting,
        error,
        confirmOpen,
        pendingDelete,
        clearError: () => setError(null),
    };
};

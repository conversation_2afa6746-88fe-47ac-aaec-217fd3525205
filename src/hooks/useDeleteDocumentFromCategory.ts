import { useState } from "react";
import { api } from "~/trpc/react";

export const useDeleteDocumentFromCategory = () => {
    const [deleting, setDeleting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [pendingDelete, setPendingDelete] = useState<{
        documentId: string;
        documentName: string;
        categoryIds: string[];
    } | null>(null);

    const removeDocumentFromCategory = api.category.removeDocumentFromCategory.useMutation();
    const utils = api.useUtils?.() || {};

    const confirmDelete = (documentId: string, documentName: string, categoryIds: string[]) => {
        setPendingDelete({ documentId, documentName, categoryIds });
        setConfirmOpen(true);
    };

    const handleDelete = async () => {
        if (!pendingDelete) return;

        setDeleting(true);
        setError(null);
        setConfirmOpen(false);

        try {
            await removeDocumentFromCategory.mutateAsync({
                documentId: pendingDelete.documentId,
                categoryIds: pendingDelete.categoryIds
            });

            console.log('Successfully removed document from category');

            // Invalidate queries to refresh the data
            if (utils.category?.getCategoryTreeWithDocuments?.invalidate) {
                utils.category.getCategoryTreeWithDocuments.invalidate();
            }

            setPendingDelete(null);
            return true;
        } catch (err: any) {
            console.error('Error removing document from category:', err);
            setError(err.message || "Error removing document from category");
            return false;
        } finally {
            setDeleting(false);
        }
    };

    const cancelDelete = () => {
        setConfirmOpen(false);
        setPendingDelete(null);
        setError(null);
    };

    return {
        confirmDelete,
        handleDelete,
        cancelDelete,
        deleting,
        error,
        confirmOpen,
        pendingDelete,
        clearError: () => setError(null),
    };
}; 
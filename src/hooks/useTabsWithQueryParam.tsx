"use client";

import { useMemo, useState, useCallback } from "react";
import { Tabs, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/v2/components/ui/Tabs";

type TabType = { value: string; label: string };

const getInitialTab = (tabs: TabType[]) => {
  const searchParams = new URLSearchParams(window.location.search);
  const tab = searchParams.get("tab");
  if (tab && tabs.some((t) => t.value === tab)) {
    return tab;
  }

  return tabs[0]?.value ?? "";
};

export function useTabsWithQueryParam({
  tabs,
}: {
  tabs?: TabType[];
}) {
  const [currentTab, setCurrentTab] = useState<TabType["value"]>(getInitialTab(tabs ?? []));

  const onTabChange = useCallback(
    (value: TabType["value"]) => {
      setCurrentTab(value);
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set("tab", value);
      const newRelativePathQuery =
        window.location.pathname + "?" + searchParams.toString();
      window.history.pushState(null, "", newRelativePathQuery);
    },
    [tabs],
  );

  const TabsComponent = useMemo(() => {
    if (!tabs) {
      return null;
    }

    return (
      <Tabs
        defaultValue="all"
        value={currentTab}
        onValueChange={(value) => onTabChange(value as TabType["value"])}
      >
        <TabsList>
          {tabs.map((tabItem) => (
            <TabsTrigger key={tabItem.value} value={tabItem.value}>
              {tabItem.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    )
  }, [currentTab, onTabChange, tabs]);

  return {
    Tabs: TabsComponent,
    currentTab,
  };
}

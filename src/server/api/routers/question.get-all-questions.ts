import {
  QuestionCategory,
  QuestionStatusType,
  Prisma,
  ResponseStatus,
} from "@prisma/client";

import { z } from "zod";
import { protectedProcedure } from "~/server/api/trpc";
import type { JsonObject } from "@prisma/client/runtime/library";
import type {
  GetQuestionsType,
  QuestionWithSearchType,
  QuestionWithVectorData,
} from "./question.common";
import { GetQuestionsInclude } from "./question.common";
import { VoyageAIEmbedding } from "~/lib/rag-response/utils/voyageai-embedding-openai-compat";
import { VoyageAIReranker } from "~/lib/rag-response/utils/voyageai-reranker";
import * as pgvector from "pgvector";
import { type QuestionGroupResponse } from "~/types/question";
import { getPyrpcApiUrl } from "~/utils/url";
import { env } from "~/env";

type RankingWithoutRelevance = {
  questionId: string;
  best_question_distance: number | null;
  best_response_distance: number | null;
  response_text: string | null;
};

type VectorRankingWithoutRelevance = RankingWithoutRelevance & {
  question_content_vector: string;
};

const QUESTION_SQL_QUERY_LIMIT = 5;
const RESPONSE_SQL_QUERY_LIMIT = 50;
const QUESTION_TOP_K = 5;
const RESPONSE_TOP_K = 5;
export const getAllQuestions = protectedProcedure
  .input(
    z.object({
      category: z.nativeEnum(QuestionCategory).array(),
      search: z.string().optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
      ddq: z.string().array().optional(),
      tagIds: z.string().array().optional(),
      fundIds: z.string().array().optional(),
      page: z.number().min(0).default(0),
      pageSize: z.number().min(1).max(100).optional(),
      emptyResponses: z.boolean().optional().default(false),
      status: z.nativeEnum(QuestionStatusType).optional(),
      responseStatus: z.nativeEnum(ResponseStatus).array().optional(),
    }),
  )
  .query(async ({ ctx, input }) => {
    if (input.search !== undefined) {
      // Generate embedding for the search query
      const embedding = new VoyageAIEmbedding();
      const qEmb: number[] = await embedding.embedQuery(input.search);
      const queryEmbedding = pgvector.toSql(qEmb) as string;

      // Define type for SQL result

      console.time("questionRankings");
      // STEP 1: Get question IDs with vector distances (lightweight ranking query)
      const questionAndResponseRankings = await ctx.db.$queryRaw<
        (RankingWithoutRelevance & { question_content_vector: string })[]
      >`
          WITH base_questions AS (
            -- Apply all basic filters first to narrow down the dataset
            SELECT DISTINCT q.*
            FROM "Question" q
            WHERE q."orgId" = ${ctx.org.id}
              AND q.category = ANY(${input.category}::"QuestionCategory"[])
              AND (${input.status}::"QuestionStatusType" IS NULL OR q.status = ${input.status}::"QuestionStatusType")
              AND (${input.startDate}::timestamp IS NULL OR q."updatedAt" >= ${input.startDate}::timestamp)
              AND (${input.endDate}::timestamp IS NULL OR q."updatedAt" <= ${input.endDate}::timestamp)
          ),
          tag_filtered_questions AS (
            -- Apply tag filter
            SELECT DISTINCT q.*
            FROM base_questions q
            ${
              input.tagIds && input.tagIds.length > 0
                ? Prisma.sql`
              JOIN "_QuestionToTag" qt ON qt."A" = q.id
              WHERE qt."B" = ANY(${input.tagIds}::text[])
            `
                : Prisma.sql`WHERE 1=1`
            }
          ),
          fund_ddq_filtered_questions AS (
            -- Apply fund and DDQ filters
            SELECT DISTINCT q.*
            FROM tag_filtered_questions q
            ${
              (input.fundIds && input.fundIds.length > 0) ||
              (input.ddq && input.ddq.length > 0)
                ? Prisma.sql`
              LEFT JOIN "Response" resp ON resp.id = q."responseId"
              LEFT JOIN "DocumentResponses" rd ON rd."responseId" = resp.id
              LEFT JOIN "Document" doc ON doc.id = rd."documentId"
              ${
                input.fundIds && input.fundIds.length > 0
                  ? Prisma.sql`
                LEFT JOIN "_DocumentToFund" df ON df."A" = doc.id
              `
                  : Prisma.empty
              }
              WHERE 1=1
              ${
                input.fundIds && input.fundIds.length > 0
                  ? Prisma.sql`
                AND df."B" = ANY(${input.fundIds}::text[])
              `
                  : Prisma.empty
              }
              ${
                input.ddq && input.ddq.length > 0
                  ? Prisma.sql`
                AND doc.id = ANY(${input.ddq}::text[])
              `
                  : Prisma.empty
              }
            `
                : Prisma.sql`WHERE 1=1`
            }
          ),
          latest_question_content AS (
            -- Get latest QuestionContent ONLY for filtered questions
            SELECT DISTINCT ON (qc."questionId") 
              qc."questionId",
              qc.vector::text as question_content_vector,
              qc.vector <=> ${queryEmbedding}::vector as vector_distance
            FROM "QuestionContent" qc
            JOIN fund_ddq_filtered_questions fq ON fq.id = qc."questionId"
            LEFT JOIN "Response" r ON r.id = fq."responseId"
            WHERE qc."orgId" = ${ctx.org.id}
              AND qc.vector IS NOT NULL
              AND qc.content->>'text' IS NOT NULL
              AND LENGTH(TRIM(qc.content->>'text')) > 0
              ${
                input.responseStatus && input.responseStatus.length > 0
                  ? Prisma.sql`
                AND r."status" = ANY(${input.responseStatus}::"ResponseStatus"[])
              `
                  : Prisma.empty
              }
            ORDER BY qc."questionId", qc."updatedAt" DESC
          ),
          latest_response_content AS (
            -- Get latest ResponseContent ONLY for filtered questions
            SELECT 
              fq.id as "questionId",
              latest_rc.vector::text as question_content_vector,
              latest_rc.vector <=> ${queryEmbedding}::vector as vector_distance,
              latest_rc.response_text
            FROM (
              SELECT DISTINCT ON (rc."responseId")
                rc."responseId",
                rc.vector,
                rc.content->>'text' as response_text
              FROM "ResponseContent" rc
              JOIN "Response" r ON r.id = rc."responseId"
              JOIN fund_ddq_filtered_questions fq ON fq."responseId" = r.id
              WHERE rc."orgId" = ${ctx.org.id}
                AND rc.vector IS NOT NULL  
                AND rc.content->>'text' IS NOT NULL
                AND LENGTH(TRIM(rc.content->>'text')) > 0
                ${
                  input.responseStatus && input.responseStatus.length > 0
                    ? Prisma.sql`
                  AND r."status" = ANY(${input.responseStatus}::"ResponseStatus"[])
                `
                    : Prisma.empty
                }
              ORDER BY rc."responseId", rc."updatedAt" DESC
            ) latest_rc
            JOIN "Response" r ON r.id = latest_rc."responseId"
            JOIN fund_ddq_filtered_questions fq ON fq."responseId" = r.id
          ),
          top_questions AS (
            -- Get top 5 closest questions by question vector distance
            SELECT 
              fq.id as "questionId",
              question_content_vector,
              lqc.vector_distance as question_distance,
              NULL::double precision as response_distance,
              NULL::text as response_text
            FROM fund_ddq_filtered_questions fq
            JOIN latest_question_content lqc ON lqc."questionId" = fq.id
            ORDER BY lqc.vector_distance ASC
            LIMIT ${QUESTION_SQL_QUERY_LIMIT}
          ),
          top_responses AS (
            -- Get top 50 closest answers by response vector distance
            SELECT 
              fq.id as "questionId",
              question_content_vector,
              NULL::double precision as question_distance,
              lrc.vector_distance as response_distance,
              lrc.response_text
            FROM fund_ddq_filtered_questions fq
            JOIN latest_response_content lrc ON lrc."questionId" = fq.id
            ORDER BY lrc.vector_distance ASC
            LIMIT ${RESPONSE_SQL_QUERY_LIMIT}
          ),
          combined_search AS (
            -- Combine and deduplicate: keep only the best distance for each question
            SELECT 
              "questionId",
              MIN(question_distance) as question_distance,
              MIN(response_distance) as response_distance,
              (array_agg(question_content_vector))[1] as question_content_vector,
              -- Get the response_text from the record with the best response distance
              (ARRAY_AGG(response_text ORDER BY response_distance ASC NULLS LAST) FILTER (WHERE response_text IS NOT NULL))[1] as response_text
            FROM (
              SELECT * FROM top_questions
              UNION ALL
              SELECT * FROM top_responses
            ) combined
            GROUP BY "questionId"
          )
          SELECT 
            "questionId",
            question_distance as best_question_distance,
            response_distance as best_response_distance,
            question_content_vector,
            response_text
          FROM combined_search
          ORDER BY question_distance ASC
        `;
      console.timeEnd("questionRankings");
      console.time("voyageReranking");

      console.log(
        "Initial questionIds count:",
        questionAndResponseRankings.length,
        "Unique count:",
        new Set(questionAndResponseRankings.map((r) => r.questionId)).size,
      );

      const questionContentVectorsMap = new Map<string, number[]>(
        questionAndResponseRankings.map(
          ({ questionId, question_content_vector }) => [
            questionId,
            pgvector.fromSql(question_content_vector),
          ],
        ),
      );

      // STEP 2: Group questions and rerank
      const rerankedRankings = await rerankQuestions(
        input.search,
        questionAndResponseRankings,
      );

      console.time("mergeRankingsWithFullData");

      // Extract question IDs from reranked results for the Prisma query
      const questionIds = rerankedRankings.map((r) => r.questionId);

      console.time("Get full question/response");
      // STEP 3: Get full question/response data using Prisma (only for reranked top results!)
      const questions =
        questionIds.length > 0
          ? await ctx.db.question.findMany({
              where: {
                id: { in: questionIds },
                orgId: ctx.org.id,
              },
              include: GetQuestionsInclude,
            })
          : [];

      console.timeEnd("Get full question/response");

      // STEP 4: Merge reranked rankings with full data and maintain reranked order
      const questionsWithVectorData: QuestionWithVectorData[] =
        rerankedRankings.reduce((acc, ranking) => {
          const question = questions.find((q) => q.id === ranking.questionId);
          const vector = questionContentVectorsMap.get(ranking.questionId);

          if (!question) return acc;

          const data: QuestionWithVectorData = {
            ...question,
            vector: pgvector.toSql(vector),
            best_question_distance: ranking.best_question_distance,
            best_response_distance: ranking.best_response_distance,
            response_text: ranking.response_text,
            relevance_score: ranking.relevance_score ?? 0, // Always provide a number
          };

          acc.push(data);
          return acc;
        }, [] as QuestionWithVectorData[]);

      const result = {
        questions: questionsWithVectorData.map((q) => {
          // Calculate the best (lowest) distance from question and response distances
          const bestQuestionDistance = q.best_question_distance;
          const bestResponseDistance = q.best_response_distance;
          const vectorDistance = Math.min(
            bestQuestionDistance ?? Infinity,
            bestResponseDistance ?? Infinity,
          );
          const finalVectorDistance =
            vectorDistance === Infinity ? 2.0 : vectorDistance;

          const questionContent = q.questionContents[0]?.content as JsonObject;
          const questionText = (questionContent?.text as string) ?? "";

          return {
            ...q,
            ...{ group: q.group },
            searchResult: {
              id: q.id,
              type: "question" as const,
              content: q.questionContents[0]?.content ?? {},
              vector_distance: finalVectorDistance,
              question_distance: bestQuestionDistance,
              response_distance: bestResponseDistance,
              search_text: q.response_text ?? "", // Use response text for reranking
              question_context: questionText, // Keep question text in context
              answer_context: q.response_text ?? "", // Use response text for answer context
              question_id: q.id,
              response_id: q.responseId,
              bm25_score: 0,
              combined_score: Math.max(0, 1 - finalVectorDistance), // Convert distance to similarity score
              relevance_score: q.relevance_score, // Always include VoyageAI rerank score (0 if not reranked)
            },
          };
        }),
        groupedQuestions: [],
        pagination: {
          total: questionsWithVectorData.length,
          pageSize: input.pageSize,
          page: input.page,
          totalPages: Math.ceil(
            questionsWithVectorData.length / (input.pageSize ?? 10),
          ),
        },
      };
      console.timeEnd("mergeRankingsWithFullData");
      return result;
    }

    const skip = input.pageSize ? input.page * input.pageSize : undefined;
    // const skip = 0;
    const take = input.pageSize ?? undefined;

    const whereClause = {
      orgId: ctx.org.id ?? "",
      category: { in: input.category },
      status: input.status ?? undefined,
      updatedAt: {
        gte: input.startDate,
        lte: input.endDate,
      },
      tags:
        input.tagIds && input.tagIds.length > 0
          ? { some: { id: { in: input.tagIds } } }
          : undefined,

      response: {
        responseContents: !input.ddq
          ? {
              every: {
                content: {
                  path: ["text"],
                  not: "",
                },
              },
            }
          : undefined,
        status:
          input.responseStatus && input.responseStatus.length > 0
            ? {
                in: input.responseStatus,
              }
            : undefined,
        documents:
          input.fundIds && input.fundIds.length > 0
            ? {
                some: {
                  document: {
                    funds: { some: { id: { in: input.fundIds } } },
                  },
                },
              }
            : undefined,
        AND: [
          {
            documents: {
              every: {
                documentId:
                  input.ddq && input.ddq.length > 0
                    ? {
                        in: input.ddq,
                      }
                    : {
                        not: undefined,
                      },
              },
            },
          },
          {
            NOT: {
              documents: {
                none: {},
              },
            },
          },
        ],
      },
    };

    // Counts
    const count = await ctx.db.question.count({
      where: {
        ...whereClause,
      },
    });

    const questions = await ctx.db.question.findMany({
      where: {
        ...whereClause,
      },
      include: GetQuestionsInclude,
      skip,
      take,
      orderBy: {
        index: "asc",
      },
    });

    return {
      questions: questions.map(
        (r) => ({ ...r, searchResult: null }) as QuestionWithSearchType,
      ),
      groupedQuestions: [],
      pagination: {
        total: count,
        pageSize: input.pageSize,
        page: input.page,
        totalPages: Math.ceil(count / (input.pageSize ?? 10)),
      },
    };
  });

// Step 2b: Rerank questions
const rerankQuestions = async (
  query: string,
  questionAndResponseRankings: RankingWithoutRelevance[],
) => {
  let rerankedRankings: (RankingWithoutRelevance & {
    relevance_score: number;
  })[] = [];
  console.time("voyageReranking");
  try {
    const reranker = new VoyageAIReranker();

    // Filter out items with empty response text for reranking
    type ValidRankingResponse = Omit<
      RankingWithoutRelevance,
      "response_text"
    > & {
      response_text: string;
    };

    const validRankings: ValidRankingResponse[] = questionAndResponseRankings
      .filter((ranking) => {
        return ranking.response_text && ranking.response_text.trim().length > 0;
      })
      .map((ranking) => {
        return {
          ...ranking,
          response_text: ranking.response_text?.trim() ?? "",
        };
      });

    console.log(
      `Valid rankings for reranking: ${validRankings.length} out of ${questionAndResponseRankings.length}`,
    );

    if (validRankings.length > 0) {
      // Rerank with top_k=5 as requested, but don't exceed available items
      const topK = Math.min(RESPONSE_TOP_K, validRankings.length);

      console.log(
        `Attempting to rerank ${validRankings.length} items with top_k=${topK}`,
      );

      const rerankedItems = await reranker.rerankItems(
        query,
        validRankings,
        (ranking: ValidRankingResponse) => ranking.response_text,
        {
          model: "rerank-2",
          top_k: topK,
        },
      );

      // Create a map of relevance scores from reranked items
      const relevanceMap = new Map<string, number>();
      rerankedItems.forEach((item) => {
        relevanceMap.set(item.questionId, item.relevance_score);
      });

      // Update relevance scores for items that were reranked
      rerankedRankings = questionAndResponseRankings
        .slice(0, QUESTION_TOP_K) // Limit to top K questions
        .map((item) => ({
          ...item,
          relevance_score: relevanceMap.get(item.questionId) ?? 0,
        }));

      const finalRankSet = new Set(rerankedRankings.map((r) => r.questionId));

      // Add reranked items that aren't already in the top k questions
      for (const reranked of rerankedItems) {
        if (!finalRankSet.has(reranked.questionId)) {
          console.log(
            `Adding reranked item ${reranked.questionId} with score ${reranked.relevance_score}`,
          );
          finalRankSet.add(reranked.questionId);
          rerankedRankings.push({
            questionId: reranked.questionId,
            best_question_distance: reranked.best_question_distance,
            best_response_distance: reranked.best_response_distance,
            response_text: reranked.response_text,
            relevance_score: reranked.relevance_score,
          });
        }
      }

      console.log(
        `Reranked ${validRankings.length} items, got top ${rerankedItems.length} results`,
      );
    } else {
      console.log(
        "No valid response texts for reranking, keeping original order",
      );
      // Still assign default relevance scores
      rerankedRankings = questionAndResponseRankings
        .slice(0, QUESTION_TOP_K)
        .map((r) => ({
          ...r,
          relevance_score: 0,
        }));
    }
  } catch (error) {
    console.error(
      "VoyageAI reranking failed, falling back to vector search order:",
      error,
    );
    console.error(
      "Error details:",
      error instanceof Error ? error.message : String(error),
    );
    // Continue with original vector search results if reranking fails
    rerankedRankings = questionAndResponseRankings
      .slice(0, QUESTION_TOP_K)
      .map((r) => ({
        ...r,
        relevance_score: 0,
      }));
  }
  console.timeEnd("voyageReranking");

  return rerankedRankings;
};

// Step 2a: Group questions
const groupQuestions = async (questionContentVectorsMap: Map<string, any>) => {
  try {
    const payload = Array.from(questionContentVectorsMap.entries()).map(
      ([questionId, vector]) => {
        return {
          question_id: questionId,
          question_vector: vector ?? [],
        };
      },
    );

    if (payload.length === 0) {
      return new Map<string, number | undefined>();
    }

    if (payload.length === 1) {
      return new Map<string, number | undefined>([
        [payload[0]!.question_id, undefined],
      ]);
    }

    const response = await fetch(
      `${getPyrpcApiUrl()}/pyrpc/generate-answer/cluster-similar-question`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questions: payload,
        }),
      },
    );
    const groups: QuestionGroupResponse[] = await response.json();

    const map = new Map<string, number | undefined>();
    groups.forEach((group) => {
      map.set(group.question_id, group.group);
    });

    return map;
  } catch (error) {
    console.error("Failed to group questions:", error);
    return new Map<string, number | undefined>();
  }
};

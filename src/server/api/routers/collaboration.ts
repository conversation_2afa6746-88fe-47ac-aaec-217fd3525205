import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { CollaborationManager } from "~/server/api/managers/collaborationManager";
import { TRPCError } from "@trpc/server";

export const collaborationRouter = createTRPCRouter({
  assignQuestion: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });
      return await CollaborationManager.assignTo({
        db: ctx.db,
        questionId: input.questionId,
        userId: input.userId,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
      });
    }),
  assignQuestionWithApproval: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
        userId: z.string(),
        responseId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });
      return await CollaborationManager.assignToWithApproval({
        db: ctx.db,
        createdById: ctx.auth.id,
        questionId: input.questionId,
        assignToUserId: input.userId,
        responseId: input.responseId,
        orgId: ctx.org.id,
      });
    }),
  unassignQuestion: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });
      return await CollaborationManager.unassign({
        db: ctx.db,
        questionId: input.questionId,
        userId: input.userId,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
      });
    }),
  searchUsers: protectedProcedure
    .input(
      z.object({
        query: z.string().optional(),
        questionId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });

      return await CollaborationManager.searchUsers({
        db: ctx.db,
        questionId: input.questionId,
        orgId: ctx.org.id,
        userId: ctx.auth.id,
        query: input.query,
      });
    }),
  getAssignedUsersByQuestion: protectedProcedure
    .input(z.object({ questionId: z.string() }))
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });
      return await CollaborationManager.getAssignedUsersByQuestion({
        db: ctx.db,
        questionId: input.questionId,
        orgId: ctx.org.id,
      });
    }),
  getAssignedUsersByDocument: protectedProcedure
    .input(z.object({ documentId: z.string() }))
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) throw new TRPCError({ code: "UNAUTHORIZED" });
      return await CollaborationManager.getAssignedUsersByDocument({
        db: ctx.db,
        documentId: input.documentId,
        orgId: ctx.org.id,
      });
    }),
});

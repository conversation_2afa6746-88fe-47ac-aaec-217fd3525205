import { z } from "zod";
import {
  getBatchQuestions,
  clusterAllQuestions,
} from "~/lib/cluster-questions";
import { protectedProcedure } from "~/server/api/trpc";

export const groupAllQuestions = protectedProcedure
  .input(z.object({}))
  .mutation(async ({ ctx }) => {
    if (!ctx.org.id) {
      return {
        message: "Organization not found",
        success: false,
      };
    }

    const batchQuestions = await getBatchQuestions({
      db: ctx.db,
      orgId: ctx.org.id,
    });

    try {
      const result = await clusterAllQuestions({
        db: ctx.db,
        orgId: ctx.org.id,
        questions: batchQuestions,
      });
      console.log(`Found ${result.length} groups`);
    } catch (err) {
      console.error("Failed to group questions:", err);
    }

    return {
      message: "Clustering completed",
      success: true,
    };
  });

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { UserActivityManager } from "../managers/userActivityManager";
import { TRPCError } from "@trpc/server";

export const activityRouter = createTRPCRouter({
  userFeedbackProvided: protectedProcedure
    .input(
      z.object({
        responseId: z.string().optional(),
        questionId: z.string().optional(),
        documentId: z.string().optional(),
        feedbackType: z.string(),
        memo: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }
      if (!ctx.auth.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User id is required",
        });
      }

      return UserActivityManager.userFeedbackProvided({
        db: ctx.db,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
        responseId: input.responseId,
        questionId: input.questionId,
        documentId: input.documentId,
        feedbackType: input.feedbackType,
        memo: input.memo,
        metadata: input.metadata,
      });
    }),

  collaboratorAdded: protectedProcedure
    .input(
      z.object({
        responseId: z.string().optional(),
        questionId: z.string().optional(),
        documentId: z.string().optional(),
        collaboratorId: z.string(),
        collaboratorName: z.string(),
        memo: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }
      if (!ctx.auth.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User id is required",
        });
      }

      return UserActivityManager.collaboratorAdded({
        db: ctx.db,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
        responseId: input.responseId,
        questionId: input.questionId,
        documentId: input.documentId,
        collaboratorId: input.collaboratorId,
        collaboratorName: input.collaboratorName,
        memo: input.memo,
        metadata: input.metadata,
      });
    }),

  responsesEdited: protectedProcedure
    .input(
      z.object({
        responseId: z.string().optional(),
        questionId: z.string().optional(),
        documentId: z.string().optional(),
        editSummary: z.string().optional(),
        memo: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }
      if (!ctx.auth.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User id is required",
        });
      }

      return UserActivityManager.responsesEdited({
        db: ctx.db,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
        responseId: input.responseId,
        questionId: input.questionId,
        documentId: input.documentId,
        editSummary: input.editSummary,
        memo: input.memo,
        metadata: input.metadata,
      });
    }),

  responseStatusUpdated: protectedProcedure
    .input(
      z.object({
        responseId: z.string(),
        questionId: z.string(),
        documentId: z.string().optional(),
        oldStatus: z.string(),
        newStatus: z.string(),
        memo: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }
      if (!ctx.auth.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User id is required",
        });
      }

      return UserActivityManager.responseStatusUpdated({
        db: ctx.db,
        orgId: ctx.org.id,
        createdById: ctx.auth.id,
        responseId: input.responseId,
        questionId: input.questionId,
        documentId: input.documentId,
        oldStatus: input.oldStatus,
        newStatus: input.newStatus,
        memo: input.memo,
        metadata: input.metadata,
      });
    }),

  getActivityByUser: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
        limit: z.number().min(1).max(100).optional().default(50),
        offset: z.number().min(0).optional().default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return UserActivityManager.getActivityByUser({
        db: ctx.db,
        orgId: ctx.org.id,
        userId: input.userId,
        limit: input.limit,
        offset: input.offset,
      });
    }),

  getActivityByOrg: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).optional().default(50),
        offset: z.number().min(0).optional().default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return UserActivityManager.getActivityByOrg({
        db: ctx.db,
        orgId: ctx.org.id,
        limit: input.limit,
        offset: input.offset,
      });
    }),

  getActivityForQuestion: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
        limit: z.number().min(1).max(100).optional().default(50),
        offset: z.number().min(0).optional().default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return UserActivityManager.getActivityForQuestion({
        db: ctx.db,
        orgId: ctx.org.id,
        questionId: input.questionId,
        limit: input.limit,
        offset: input.offset,
      });
    }),
});

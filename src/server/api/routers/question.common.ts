import { type SearchResult } from "~/server/utils/search";

import type { Prisma } from "@prisma/client";

export const GetQuestionsInclude = {
  questionContents: true,
  response: {
    include: {
      documents: {
        include: {
          document: {
            include: {
              funds: true,
              tags: true,
            },
            omit: {
              jsonContents: true,
              htmlContents: true,
              plainTextContents: true,
            },
          },
        },
      },
      responseContents: {
        include: {
          createdBy: true,
        },
      },
      tags: true,
      documentSection: {
        select: {
          id: true,
          title: true,
        },
      },
    },
  },
  tags: true,
};

export type GetQuestionsType = Prisma.QuestionGetPayload<{
  include: typeof GetQuestionsInclude;
}>;

export type QuestionWithSearchType = GetQuestionsType & {
  searchResult: SearchResult | null;
};

export type QuestionWithVectorData = GetQuestionsType & {
  best_question_distance: number | null;
  best_response_distance: number | null;
  response_text: string | null;
  vector: number[];
  relevance_score: number;
};

export type GroupQuestionType = {
  id: string;
  name: string;
  questionIds: string[];
};

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { AnalyticsManager } from "../managers/analyticsManager";

export const analyticsRouter = createTRPCRouter({
  getDocumentStatsByStatus: protectedProcedure.query(async ({ ctx }) => {
    return await AnalyticsManager.getDocumentStatsByStatus({
      db: ctx.db,
      orgId: ctx.org.id ?? "",
    });
  }),
});

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { CategoryManager } from "../managers/categoryManager";
import { CategoryStatus } from "@prisma/client";

export const categoryRouter = createTRPCRouter({
  getCategoryTreeWithDocuments: protectedProcedure
    .input(
      z.object({
        filterMode: z.enum(["MISSING_ONLY", "ALL", "ACTIVE_ONLY"]).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return await CategoryManager.getCategoryTree({
        db: ctx.db,
        orgId: ctx.org.id ?? "",
        filterMode: input.filterMode,
      });
    }),

  getAllCategories: protectedProcedure.query(async ({ ctx }) => {
    return await CategoryManager.getAllCategories({
      db: ctx.db,
      orgId: ctx.org.id ?? "",
    });
  }),

  createCategory: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, { message: "Name is required" }),
        description: z.string().optional(),
        staleAfterDays: z.number().optional(),
        parentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const category = await CategoryManager.createCategory({
        db: ctx.db,
        data: {
          name: input.name,
          description: input.description ?? null,
          staleAfterDays: input.staleAfterDays ?? null,
          orgId: ctx.org.id ?? "",
          parentId: input.parentId ?? null,
          parentSeedId: null,
          seedId: null,
          status: CategoryStatus.ACTIVE,
        },
      });
      return category;
    }),

  updateCategory: protectedProcedure
    .input(
      z.object({
        id: z.string().min(1),
        name: z.string().min(1, { message: "Name is required" }),
        description: z.string().optional(),
        staleAfterDays: z.number().optional(),
        status: z
          .enum([CategoryStatus.ACTIVE, CategoryStatus.INACTIVE])
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Update the category itself
      const updatedCategory = await ctx.db.category.update({
        where: { id: input.id, orgId: ctx.org.id ?? "" },
        data: {
          name: input.name,
          description: input.description ?? null,
          staleAfterDays: input.staleAfterDays ?? null,
          status: input.status ?? CategoryStatus.ACTIVE,
        },
      });
      return updatedCategory;
    }),

  deleteCategory: protectedProcedure
    .input(
      z.object({
        id: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if category has children
      const categoryWithChildren = await ctx.db.category.findFirst({
        where: {
          id: input.id,
          orgId: ctx.org.id ?? "",
        },
        include: {
          children: true,
        },
      });

      if (!categoryWithChildren) {
        throw new Error("Category not found");
      }

      if (categoryWithChildren.children.length > 0) {
        throw new Error("Cannot delete category that has children");
      }

      // Delete the category
      await CategoryManager.deleteCategory({
        db: ctx.db,
        id: input.id,
        orgId: ctx.org.id ?? "",
      });

      return { success: true };
    }),

  removeDocumentFromCategory: protectedProcedure
    .input(
      z.object({
        documentId: z.string().min(1),
        categoryIds: z.array(z.string()).min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      console.log("removeDocumentFromCategory called with:", input);

      await Promise.all(
        input.categoryIds.map(async (categoryId) => {
          await ctx.db.category.update({
            where: {
              id: categoryId,
              orgId: ctx.org.id ?? "",
            },
            data: {
              documents: {
                disconnect: { id: input.documentId },
              },
            },
          });
        }),
      );

      return { success: true };
    }),

  exportCategories: protectedProcedure.mutation(async ({ ctx }) => {
    return await CategoryManager.exportCategoriesToExcel({
      db: ctx.db,
      orgId: ctx.org.id ?? "",
    });
  }),
  countStatus: protectedProcedure.input(z.object({
    categoryId: z.string().optional(),
  })).query(async ({ ctx, input }) => {
    return await CategoryManager.countStatus({
      db: ctx.db,
      orgId: ctx.org.id ?? "",
      categoryId: input.categoryId,
    });
  }),
});

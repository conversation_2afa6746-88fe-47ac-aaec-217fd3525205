import { PrismaClientType } from "~/server/db";
import * as Sentry from "@sentry/node";
import { getSharepointFileIdFromUrl } from "~/lib/integrations/azure/utils";
import { GetDocumentsInclude } from "../routers/document";
import { getFilenameFromLocalUrl } from "~/server/utils/getFilenameFromLocalUrl";

export class DocumentManager {
  /**
   * Get a document by its sharepoint url
   */
  static async getDocumentBySharepointUrl({
    db,
    orgId,
    url,
  }: {
    orgId: string;
    db: PrismaClientType;
    url: string;
  }) {
    const org = await db.org.findFirstOrThrow({
      where: { id: orgId },
      include: { azureAccessToken: true, AzureDrive: true },
    });

    if (!org.azureAccessToken) {
      Sentry.captureMessage("AccessToken not found", {
        level: "error",
        extra: {
          url,
        },
      });
      throw new Error("AccessToken not found");
    }

    try {
      const id = await getSharepointFileIdFromUrl(
        org.azureAccessToken,
        org.AzureDrive[0]?.azureId ?? "",
        url,
        db,
      );
      return await db.document.findFirstOrThrow({
        where: { azureItemId: id },
        include: GetDocumentsInclude,
      });
    } catch (error) {
      Sentry.captureMessage("Error getting Sharepoint file ID from URL", {
        level: "error",
        extra: {
          url,
        },
      });
      Sentry.captureException(error);
      throw error;
    }
  }

  static async getDocumentByLocalUrl({
    db,
    orgId,
    localUrl,
  }: {
    orgId: string;
    db: PrismaClientType;
    localUrl: string;
  }) {
    const filename = getFilenameFromLocalUrl(localUrl);
    try {
      return await db.document.findFirstOrThrow({
        where: { name: { contains: filename, mode: "insensitive" }, orgId },
        include: GetDocumentsInclude,
      });
    } catch (error) {
      Sentry.captureMessage("Error getting document by local URL", {
        level: "error",
        extra: { localUrl, filename },
      });
      Sentry.captureException(error);
      throw error;
    }
  }
}

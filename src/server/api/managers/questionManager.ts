import {
  <PERSON><PERSON><PERSON>,
  Question<PERSON>ategor<PERSON>,
  QuestionStatusType,
  ResponseStatus,
} from "@prisma/client";
import { PrismaClientType } from "~/server/db";
import type { GetQuestionsType } from "../routers/question.common";
import { GetQuestionsInclude } from "../routers/question.common";
import { AggregatedFeedback, FeedbackManager } from "./feedbackManager";

export interface DDQuestion {
  id: string;
  text: string;
  insufficientData: boolean;
  selected: boolean;
  isLoading: boolean;
  response: GetQuestionsType["response"];
  status: QuestionStatusType;
}

export interface DDQuestionWithFeedback extends DDQuestion {
  feedback: AggregatedFeedback | null;
}
export interface DDQuestionWithIndexAndFeedback extends DDQuestionWithFeedback {
  index: number;
}

export type DDQSectionWithQuestions = Record<
  string,
  {
    title: string;
    sectionId: string;
    index: number;
    questions: DDQuestionWithIndexAndFeedback[];
  }
>;
export class QuestionManager {
  static normalizeQuestion = (question: GetQuestionsType): DDQuestion => {
    return {
      id: question.id,
      text: (question.questionContents[0]?.content as { text: string }).text,
      insufficientData: (
        question.questionContents[0]?.content as { insufficientData: boolean }
      )?.insufficientData,
      selected: false,
      isLoading: false,
      response: question.response,
      status: question.status,
    };
  };

  static normalizeQuestions = (questions: GetQuestionsType[]): DDQuestion[] => {
    return questions.map((question) => this.normalizeQuestion(question));
  };

  private static whereClauseAllQuestionsAssignedToUser = (
    orgId: string,
    userId: string,
  ): Prisma.QuestionWhereInput => {
    return {
      orgId,
      category: {
        in: [
          QuestionCategory.OTHER,
          QuestionCategory.DATA_ASSURANCE,
          QuestionCategory.STANDARDS_AND_FRAMEWORKS,
          QuestionCategory.INVESTMENT_PROCESS,
        ],
      },
      response: {
        NOT: {
          documents: {
            none: {},
          },
        },
        status: {
          in: [ResponseStatus.PENDING_APPROVAL],
        },
      },
      AssignedQuestions: {
        some: {
          assignToUserId: userId,
          requiresApproval: true,
        },
      },
    };
  };

  private static whereClauseAllQuestionsForDocument = (
    orgId: string,
    documentId: string,
    questionStatusTypes?: QuestionStatusType[],
    responseStatuses?: ResponseStatus[],
  ): Prisma.QuestionWhereInput => {
    return {
      orgId,
      category: {
        in: [
          QuestionCategory.OTHER,
          QuestionCategory.DATA_ASSURANCE,
          QuestionCategory.STANDARDS_AND_FRAMEWORKS,
          QuestionCategory.INVESTMENT_PROCESS,
        ],
      },
      status: {
        in: questionStatusTypes?.length
          ? questionStatusTypes
          : [QuestionStatusType.NEW, QuestionStatusType.ANSWERED],
      },
      response: {
        AND: [
          {
            documents: {
              every: {
                documentId,
              },
            },
          },
          {
            NOT: {
              documents: {
                none: {},
              },
            },
          },
          {
            status: {
              in: responseStatuses?.length
                ? responseStatuses
                : [
                    ResponseStatus.DRAFT,
                    ResponseStatus.PENDING_APPROVAL,
                    ResponseStatus.APPROVED,
                    ResponseStatus.REJECTED,
                  ],
            },
          },
        ],
      },
    };
  };
  private static whereClauseSingleQuestion = (
    orgId: string,
    questionId: string,
  ): Prisma.QuestionWhereInput => {
    return {
      orgId,
      id: questionId,
      response: {
        AND: [
          {
            NOT: {
              documents: {
                none: {},
              },
            },
          },
        ],
      },
    };
  };

  static async getQuestion({
    db,
    orgId,
    questionId,
  }: {
    db: PrismaClientType;
    orgId: string;
    questionId: string;
  }): Promise<GetQuestionsType | null> {
    const whereClause: Prisma.QuestionWhereInput =
      this.whereClauseSingleQuestion(orgId, questionId);
    const question = await db.question.findFirst({
      where: whereClause,
      include: GetQuestionsInclude,
    });
    if (!question) {
      return null;
    }
    return question;
  }

  static async getQuestionWithFeedback({
    db,
    orgId,
    questionId,
    userId,
  }: {
    db: PrismaClientType;
    orgId: string;
    questionId: string;
    userId: string;
  }): Promise<DDQuestionWithFeedback | null> {
    const question = await this.getQuestion({
      db,
      orgId,
      questionId,
    });
    if (!question) {
      return null;
    }
    const feedback = await FeedbackManager.getAggregatedFeedback({
      db,
      questionId: question.id,
      orgId,
      userId,
    });

    return {
      ...this.normalizeQuestion(question),
      feedback,
    };
  }
  static async getQuestions({
    db,
    orgId,
    documentId,
    limit,
    cursor,
    questionStatusTypes,
    responseStatuses,
  }: {
    db: PrismaClientType;
    orgId: string;
    documentId: string;
    limit: number;
    cursor?: string; // react query needs string
    questionStatusTypes?: QuestionStatusType[];
    responseStatuses?: ResponseStatus[];
  }): Promise<{
    items: DDQuestion[];
    nextCursor?: string;
  }> {
    const whereClause: Prisma.QuestionWhereInput =
      this.whereClauseAllQuestionsForDocument(
        orgId,
        documentId,
        questionStatusTypes,
        responseStatuses,
      );
    const cursorInt = cursor ? parseInt(cursor) : 0;
    const questions = await db.question.findMany({
      where: whereClause,
      include: GetQuestionsInclude,
      take: limit + 1,
      skip: cursorInt,
      orderBy: {
        index: "asc",
      },
    });

    const hasMore = questions.length > limit;
    const items: GetQuestionsType[] = hasMore
      ? questions.slice(0, -1)
      : questions;
    const nextCursor = hasMore ? (cursorInt + limit).toString() : undefined;

    return {
      items: this.normalizeQuestions(items),
      nextCursor,
    };
  }

  static async getQuestionsWithFeedback({
    db,
    orgId,
    documentId,
    limit,
    cursor,
    userId,
    questionStatusTypes,
    responseStatuses,
  }: {
    db: PrismaClientType;
    orgId: string;
    documentId: string;
    limit: number;
    cursor?: string;
    userId: string;
    questionStatusTypes?: QuestionStatusType[];
    responseStatuses?: ResponseStatus[];
  }): Promise<{
    items: DDQuestionWithFeedback[];
    nextCursor?: string;
  }> {
    const questions = await this.getQuestions({
      db,
      orgId,
      documentId,
      limit,
      cursor,
      questionStatusTypes,
      responseStatuses,
    });

    const feedbackBatch = await FeedbackManager.getAggregatedFeedbackBatch({
      db,
      questionIds: questions.items.map((question) => question.id),
      orgId,
      userId,
    });
    const items: DDQuestionWithFeedback[] = questions.items.map((question) => ({
      ...question,
      feedback: feedbackBatch.aggregatedByQuestionId[question.id] ?? null,
    }));
    return { items, nextCursor: questions.nextCursor };
  }

  static async getAssignedQuestionsInfinite({
    db,
    orgId,
    userId,
    limit,
    cursor,
  }: {
    db: PrismaClientType;
    orgId: string;
    userId: string;
    limit: number;
    cursor?: string;
  }): Promise<{
    items: GetQuestionsType[];
    nextCursor?: string;
  }> {
    const whereClause: Prisma.QuestionWhereInput =
      this.whereClauseAllQuestionsAssignedToUser(orgId, userId);
    const cursorInt = cursor ? parseInt(cursor) : 0;

    const questions = await db.question.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      take: limit + 1,
      skip: cursorInt,
      include: GetQuestionsInclude,
    });

    const hasMore = questions.length > limit;
    const items: GetQuestionsType[] = hasMore
      ? questions.slice(0, -1)
      : questions;
    const nextCursor = hasMore ? (cursorInt + limit).toString() : undefined;

    return {
      items,
      nextCursor,
    };
  }

  static async getAssignedQuestionsPaginated({
    db,
    orgId,
    userId,
    page,
    pageSize,
  }: {
    db: PrismaClientType;
    orgId: string;
    userId: string;
    page: number;
    pageSize: number;
  }): Promise<{
    items: GetQuestionsType[];
    pagination: {
      total: number;
      pageSize: number;
      page: number;
      totalPages: number;
    };
  }> {
    const whereClause: Prisma.QuestionWhereInput =
      this.whereClauseAllQuestionsAssignedToUser(orgId, userId);

    // Get total count
    const count = await db.question.count({
      where: whereClause,
    });

    // Calculate skip value
    const skip = (page - 1) * pageSize;

    // Get questions for current page
    const questions = await db.question.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      take: pageSize,
      skip,
      include: GetQuestionsInclude,
    });

    return {
      items: questions,
      pagination: {
        total: count,
        pageSize,
        page,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }
}

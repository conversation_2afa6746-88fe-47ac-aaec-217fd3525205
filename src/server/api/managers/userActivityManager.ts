import { UserActivityType } from "@prisma/client";
import { type PrismaClientType } from "~/server/db";
import { Prisma } from "@prisma/client";

type DatabaseClient = PrismaClientType | Prisma.TransactionClient;

export class UserActivityManager {
  static async userFeedbackProvided({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    feedbackType,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    feedbackType: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.RESPONSE_FEEDBACK_CREATED,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `User provided ${feedbackType} feedback`,
        metadata: {
          feedbackType,
          ...metadata,
        },
      },
    });
  }

  static async collaboratorAdded({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    collaboratorId,
    collaboratorName,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    collaboratorId: string;
    collaboratorName: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.QUESTION_ASSIGNED,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Collaborator ${collaboratorName} added to the task`,
        metadata: {
          collaboratorId,
          collaboratorName,
          ...metadata,
        },
      },
    });
  }

  static async collaboratorRemoved({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    collaboratorId,
    collaboratorName,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    collaboratorId: string;
    collaboratorName: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.QUESTION_UNASSIGNED,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Collaborator ${collaboratorName} removed from the task`,
        metadata: {
          collaboratorId,
          collaboratorName,
          action: "removed",
          ...metadata,
        },
      },
    });
  }

  static async responsesEdited({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    editSummary,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    editSummary?: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.RESPONSE_UPDATED,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Response edited${editSummary ? `: ${editSummary}` : ""}`,
        metadata: {
          editSummary,
          ...metadata,
        },
      },
    });
  }

  static async responseStatusUpdated({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    oldStatus,
    newStatus,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId: string;
    questionId: string;
    documentId?: string;
    oldStatus: string;
    newStatus: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    let activityType: UserActivityType;

    // Determine the appropriate activity type based on the new status
    switch (newStatus.toUpperCase()) {
      case "APPROVED":
        activityType = UserActivityType.RESPONSE_APPROVED;
        break;
      case "DRAFT":
        activityType = UserActivityType.RESPONSE_DRAFTED;
        break;
      default:
        activityType = UserActivityType.RESPONSE_UPDATED;
    }

    return db.userActivity.create({
      data: {
        type: activityType,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo:
          memo || `Response status changed from ${oldStatus} to ${newStatus}`,
        metadata: {
          oldStatus,
          newStatus,
          ...metadata,
        },
      },
    });
  }

  static async responseCreated({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId: string;
    questionId: string;
    documentId?: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    const activityType = UserActivityType.RESPONSE_CREATED;

    return db.userActivity.create({
      data: {
        type: activityType,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Response created`,
        metadata: {
          ...metadata,
        },
      },
    });
  }

  static async getActivityByUser({
    db,
    orgId,
    userId,
    limit = 50,
    offset = 0,
  }: {
    db: DatabaseClient;
    orgId: string;
    userId: string;
    limit?: number;
    offset?: number;
  }) {
    return db.userActivity.findMany({
      where: {
        orgId,
        createdById: userId,
      },
      include: {
        response: {
          include: {
            responseContents: true,
          },
        },
        question: true,
        document: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
    });
  }

  static async getActivityByOrg({
    db,
    orgId,
    limit = 50,
    offset = 0,
  }: {
    db: DatabaseClient;
    orgId: string;
    limit?: number;
    offset?: number;
  }) {
    return db.userActivity.findMany({
      where: {
        orgId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        response: {
          include: {
            responseContents: true,
          },
        },
        question: true,
        document: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
    });
  }

  static async getActivityForQuestion({
    db,
    orgId,
    questionId,
    limit = 50,
    offset = 0,
  }: {
    db: DatabaseClient;
    orgId: string;
    questionId: string;
    limit?: number;
    offset?: number;
  }) {
    return db.userActivity.findMany({
      where: {
        orgId,
        questionId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        response: {
          include: {
            responseContents: true,
          },
        },
        question: true,
        document: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
    });
  }

  static async approvalRequested({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    approverId,
    approverName,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    approverId: string;
    approverName: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.RESPONSE_ASSIGNED_FOR_APPROVAL,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Response sent for approval to ${approverName}`,
        metadata: {
          approverId,
          approverName,
          ...metadata,
        },
      },
    });
  }

  static async approvalCancelled({
    db,
    orgId,
    createdById,
    responseId,
    questionId,
    documentId,
    approverId,
    approverName,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    responseId?: string;
    questionId?: string;
    documentId?: string;
    approverId: string;
    approverName: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.RESPONSE_UNASSIGNED_FOR_APPROVAL,
        orgId,
        createdById,
        responseId,
        questionId,
        documentId,
        memo: memo || `Approval request cancelled for ${approverName}`,
        metadata: {
          approverId,
          approverName,
          action: "cancelled",
          ...metadata,
        },
      },
    });
  }

  static async regenerateResponse({
    db,
    orgId,
    createdById,
    questionId,
    documentId,
    memo,
    metadata,
  }: {
    db: DatabaseClient;
    orgId: string;
    createdById: string;
    questionId?: string;
    documentId?: string;
    memo?: string;
    metadata?: Record<string, any>;
  }) {
    return db.userActivity.create({
      data: {
        type: UserActivityType.RE_GENERATE_ANSWER,
        orgId,
        createdById,
        questionId,
        documentId,
        memo: memo || `Re-generate Answer`,
        metadata: {
          ...metadata,
        },
      },
    });
  }
}

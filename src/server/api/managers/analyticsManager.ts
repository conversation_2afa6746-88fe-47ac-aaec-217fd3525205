import { PrismaClientType } from "~/server/db";
import * as Sentry from "@sentry/node";
import { getSharepointFileIdFromUrl } from "~/lib/integrations/azure/utils";
import { GetDocumentsInclude } from "../routers/document";
import { getFilenameFromLocalUrl } from "~/server/utils/getFilenameFromLocalUrl";

export class AnalyticsManager {
  /**
   * Get a document by its sharepoint url
   */
  static async getDocumentStatsByStatus({
    db,
    orgId,
  }: {
    orgId: string;
    db: PrismaClientType;
  }) {
    const totalCount = await db.document.count({
      where: { orgId },
    });

    // Group documents by status
    const documentsByStatus = await db.document.groupBy({
      by: ["status"],
      where: { orgId },
      _count: {
        status: true,
      },
    });

    const byStatus = documentsByStatus.map(({ status, _count }) => ({
      status,
      count: _count.status,
    }));

    return {
      totalCount,
      byStatus,
    };
  }
}

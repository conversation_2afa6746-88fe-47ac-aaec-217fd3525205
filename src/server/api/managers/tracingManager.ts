import { faker } from "@faker-js/faker";
import { randomBytes } from "crypto";
import { LangfuseTraceClient, LangfuseSpanClient } from "langfuse";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Langfuse } from "langfuse-langchain";
import { env } from "~/env";

export interface TracingContext {
  sessionId?: string;
  traceId?: string;
  parentSpanId?: string | null | undefined;
}

export class Tracing {
  private static instance: Tracing;
  private client?: Langfuse;

  private constructor() {
    if (!env.LANGFUSE_PUBLIC_KEY || !env.LANGFUSE_HOST) {
      return;
    }

    this.client = new Langfuse({
      publicKey: env.LANGFUSE_PUBLIC_KEY,
      baseUrl: env.LANGFUSE_HOST,
    });
  }

  public static getInstance(): Tracing {
    if (!Tracing.instance) {
      Tracing.instance = new Tracing();
    }
    return Tracing.instance;
  }

  public score({
    value,
    traceId,
    comment,
    dataType,
    type,
    name,
  }: {
    value: number;
    traceId: string;
    comment: string;
    dataType: "NUMERIC" | "BOOLEAN" | "CATEGORICAL";
    type?: "USER_FEEDBACK" | "LLM_EVALUATION";
    name: string;
  }) {
    if (!this.client) {
      return;
    }
    
    return this.client?.score({
      name: name,
      value: value,
      traceId: traceId,
      comment: comment,
      dataType: dataType,
    });
  }

  public end() {
    return this.client?.flush();
  }

  public generateTracingId() {
    return randomBytes(16).toString("hex");
  }

  public generateSessionId() {
    return faker.string.uuid();
  }

  public generateSpanId() {
    return randomBytes(8).toString("hex");
  }

  public trace({
    name,
    input,
    id,
    userId,
    tracingContext,
  }: {
    name: string;
    input?: unknown;
    id?: string;
    userId?: string;
    tracingContext?: TracingContext;
  }) {
    if (!this.client) {
      return undefined;
    }

    let trace;

    if (tracingContext?.traceId) {
      // If we have a valid parent trace, create a span as a child
      trace = this.client.span({
        name,
        input,
        traceId: tracingContext?.traceId,
        parentObservationId: tracingContext?.parentSpanId,
        id: this.generateSpanId(),
      });
    } else {
      // Otherwise create a new root trace
      trace = this.client.trace({
        name,
        sessionId: tracingContext?.sessionId ?? this.generateSessionId(),
        input,
        userId,
        id: id ?? this.generateTracingId(),
      });
    }

    return trace;
  }

  public getHandlerForTrace({
    trace,
    sessionId,
  }: {
    trace?: LangfuseTraceClient | LangfuseSpanClient;
    sessionId?: string;
  }) {
    if (!this.client) {
      return null;
    }

    if (!trace) {
      const rootTrace = this.client.trace();
      return new CallbackHandler({ root: rootTrace, sessionId });
    }
    return new CallbackHandler({ root: trace, sessionId });
  }

  public shutdown() {
    return this.client?.shutdown();
  }
}

export const tracing = Tracing.getInstance();

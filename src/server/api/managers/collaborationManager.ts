import { type PrismaClientType } from "~/server/db";
import type { AssignedQuestion, Question, User } from "@prisma/client";
import { UserActivityManager } from "./userActivityManager";
import { Prisma, ResponseStatus, UserActivityType } from "@prisma/client";
import { ResponseManager } from "./responseManager";
import { knockUtils } from "~/lib/integrations/knock/utils";
import { env } from "~/env";
import { JsonObject } from "@prisma/client/runtime/library";
import { QuestionContentType } from "~/lib/types";

export interface AssignedQuestionWithDetails extends AssignedQuestion {
  question: Question;
  assignToUser: User;
  createdBy: User;
}

export type UserWithAssignedBit = {
  id: string;
  name: string;
  email: string;
  image: string;
  isAssigned: boolean;
  isAssignedForApproval: boolean;
};

export class CollaborationManager {
  /**
   * Assign a question to a specific user within an organization
   */
  static async assignTo({
    db,
    questionId,
    userId,
    orgId,
    createdById,
  }: {
    db: PrismaClientType;
    questionId: string;
    userId: string;
    orgId: string;
    createdById: string;
  }): Promise<AssignedQuestion> {
    return db.$transaction(async (tx: Prisma.TransactionClient) => {
      // Check if the assignment already exists
      const existingAssignment = await tx.assignedQuestion.findFirst({
        where: {
          questionId,
          assignToUserId: userId,
          orgId,
        },
        include: {
          assignToUser: true,
          createdBy: true,
        },
      });

      // assign again if it already exists
      if (existingAssignment) {
        return existingAssignment;
      }

      console.log("questionId:", questionId);

      // Create the assignment
      const assignment = await tx.assignedQuestion.create({
        data: {
          questionId,
          assignToUserId: userId,
          orgId,
          createdById,
        },
        include: {
          assignToUser: true,
          createdBy: true,
        },
      });

      // Log the collaboration activity
      await UserActivityManager.collaboratorAdded({
        db: tx,
        orgId,
        createdById,
        questionId,
        collaboratorId: userId,
        collaboratorName:
          assignment.assignToUser.name ||
          assignment.assignToUser.email ||
          userId,
        memo: `Question assigned to ${assignment.assignToUser.name || assignment.assignToUser.email || userId}`,
        metadata: {
          assignmentId: assignment.id,
          questionId,
          assignToUserId: userId,
        },
      });

      return assignment;
    });
  }

  static async assignToWithApproval({
    db,
    questionId,
    assignToUserId,
    orgId,
    createdById,
    responseId,
  }: {
    db: PrismaClientType;
    questionId: string;
    assignToUserId: string;
    orgId: string;
    createdById: string;
    responseId: string;
  }): Promise<AssignedQuestion> {
    const result = await db.$transaction(
      async (tx: Prisma.TransactionClient) => {
        // Check if the assignment already exists
        let assignment = await tx.assignedQuestion.findFirst({
          where: {
            questionId,
            assignToUserId,
            orgId,
          },
          include: {
            assignToUser: true,
            createdBy: true,
          },
        });

        // Create the assignment if it doesn't exist
        if (assignment) {
          assignment = await tx.assignedQuestion.update({
            where: {
              id: assignment.id,
            },
            data: {
              requiresApproval: true,
            },
            include: {
              assignToUser: true,
              createdBy: true,
            },
          });
        } else {
          assignment = await tx.assignedQuestion.create({
            data: {
              questionId,
              assignToUserId,
              orgId,
              createdById,
              requiresApproval: true,
            },
            include: {
              assignToUser: true,
              createdBy: true,
            },
          });
        }

        // Log the approval request activity
        await UserActivityManager.approvalRequested({
          db: tx,
          orgId,
          createdById,
          responseId,
          questionId,
          approverId: assignToUserId,
          approverName:
            assignment.assignToUser.name ||
            assignment.assignToUser.email ||
            assignToUserId,
          memo: `Question assigned to ${assignment.assignToUser.name || assignment.assignToUser.email || assignToUserId}`,
          metadata: {
            assignmentId: assignment.id,
            questionId,
            assignToUserId,
          },
        });

        return assignment;
      },
    );

    await ResponseManager.updateResponseStatus({
      db,
      id: responseId,
      status: ResponseStatus.PENDING_APPROVAL,
      orgId,
      createdById,
    });

    const document = await db.document.findFirst({
      select: {
        name: true,
      },
      where: {
        responses: {
          some: {
            responseId: responseId,
          },
        },
        orgId,
      },
    });

    const question = await db.question.findFirst({
      select: {
        questionContents: {
          select: {
            content: true,
          },
        },
      },
      where: {
        id: questionId,
        orgId,
      },
    });

    const questionContent = question?.questionContents[0]?.content;
    const questionContentText = questionContent
      ? (questionContent as QuestionContentType).text
      : "";

    // TODO: this probably needs to be more robust in case knock is down - but ok for now
    await knockUtils.notification({
      orgId,
      data: {
        medium: "email",
        type: "tag",
        title: "Pending approval on Virgil.",
        message: `Your review has been requested on Virgil. Click on the link below to view the items pending your approval.`,
        batch_content: `Question "${questionContentText}" of document "${document?.name}"`,
        link: `${process.env.VERCEL_PROJECT_PRODUCTION_URL ?? process.env.NGROK_DOMAIN}/dashboard/response?tab=pending-approval`,
        link_label: "Click to Review in Virgil",
        batch_id: "email|tag",
      },
      recipients: [
        {
          id: result.assignToUser.id,
          name: result.assignToUser.name ?? "",
          email: result.assignToUser.email ?? "",
        },
      ],
      actor: {
        id: createdById,
        name: result.createdBy.name ?? "",
        email: result.createdBy.email,
      },
    });

    return result;
  }

  /**
   * Remove an assignment (unassign a question from a user)
   */
  static async unassign({
    db,
    questionId,
    userId,
    orgId,
    createdById,
  }: {
    db: PrismaClientType;
    questionId: string;
    userId: string;
    orgId: string;
    createdById: string;
  }): Promise<void> {
    return db.$transaction(async (tx: Prisma.TransactionClient) => {
      // Get user details for activity tracking
      const assignToUser = await tx.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true },
      });

      const deleted = await tx.assignedQuestion.deleteMany({
        where: {
          questionId,
          assignToUserId: userId,
          orgId,
        },
      });

      if (deleted.count === 0) {
        throw new Error("Assignment not found");
      }

      // Log the unassignment activity
      await UserActivityManager.collaboratorRemoved({
        db: tx,
        orgId,
        createdById,
        questionId,
        collaboratorId: userId,
        collaboratorName: assignToUser?.name || assignToUser?.email || userId,
        memo: `Question unassigned from ${assignToUser?.name || assignToUser?.email || userId}`,
        metadata: {
          questionId,
          assignToUserId: userId,
        },
      });

      const count = await tx.assignedQuestion.count({
        where: {
          questionId,
          orgId,
        },
      });

      if (count === 0) {
        const response = await tx.response.findFirst({
          where: {
            questions: {
              some: {
                id: questionId,
              },
            },
          },
        });
        if (response) {
          await ResponseManager.updateResponseStatusTxFn({
            tx,
            id: response?.id,
            status: ResponseStatus.DRAFT,
            orgId,
            createdById,
          });
        }
      }
    });
  }

  static async searchUsers({
    db,
    questionId,
    orgId,
    userId,
    query,
  }: {
    db: PrismaClientType;
    questionId: string;
    orgId: string;
    userId: string;
    query: string | undefined;
  }): Promise<UserWithAssignedBit[]> {
    const users = await db.user.findMany({
      where: {
        name: { contains: query ?? "", mode: "insensitive" },
        orgs: { some: { orgId } },
      },
      include: {
        AssignedQuestions: {
          where: {
            questionId: questionId,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
      take: 10,
    });

    const transformedUsers = users.map((user) => ({
      id: user.id,
      name: user.name ?? "",
      email: user.email ?? "",
      image: user.image ?? "",
      isAssigned: user.AssignedQuestions.length > 0,
      isAssignedForApproval: user.AssignedQuestions.some(
        (question) => question.requiresApproval,
      ),
    }));

    return transformedUsers.sort((a, b) => {
      // First sort by assignment status (assigned users first)
      if (a.isAssigned && !b.isAssigned) return -1;
      if (!a.isAssigned && b.isAssigned) return 1;

      // Then sort by name alphabetically
      return a.name.localeCompare(b.name);
    });
  }

  static async getAssignedUsersByQuestion({
    db,
    questionId,
    orgId,
    requiresApproval = undefined,
  }: {
    db: PrismaClientType;
    questionId: string;
    orgId: string;
    requiresApproval?: boolean;
  }): Promise<UserWithAssignedBit[]> {
    const users = await db.user.findMany({
      where: {
        AssignedQuestions: {
          some: {
            questionId,
            orgId,
            requiresApproval,
          },
        },
      },
      include: {
        AssignedQuestions: {
          where: {
            questionId,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return users.map((user) => ({
      id: user.id,
      name: user.name ?? "",
      email: user.email ?? "",
      image: user.image ?? "",
      isAssigned: user.AssignedQuestions.length > 0,
      isAssignedForApproval: user.AssignedQuestions.some(
        (question) => question.requiresApproval,
      ),
    }));
  }

  static async getAssignedUsersByDocument({
    db,
    documentId,
    orgId,
  }: {
    db: PrismaClientType;
    documentId: string;
    orgId: string;
  }): Promise<UserWithAssignedBit[]> {
    const users = await db.user.findMany({
      where: {
        AssignedQuestions: {
          some: {
            question: {
              response: {
                documents: {
                  some: {
                    documentId,
                  },
                },
              },
            },
          },
        },
      },
      include: {
        AssignedQuestions: true,
      },
      orderBy: {
        name: "asc",
      },
      distinct: ["id"],
    });

    return users.map((user) => ({
      id: user.id,
      name: user.name ?? "",
      email: user.email ?? "",
      image: user.image ?? "",
      isAssigned: user.AssignedQuestions.length > 0,
      isAssignedForApproval: user.AssignedQuestions.some(
        (question) => question.requiresApproval,
      ),
    }));
  }

  static async sendApprovalNotification({
    db,
    questionId,
    orgId,
    actorUserId,
  }: {
    db: PrismaClientType;
    questionId: string;
    orgId: string;
    actorUserId: string;
  }) {
    return db.$transaction(async (tx: Prisma.TransactionClient) => {
      return await CollaborationManager.sendApprovalNotificationTxFn({
        db: tx,
        questionId,
        orgId,
        actorUserId,
      });
    });
  }

  static async sendApprovalNotificationTxFn({
    db,
    questionId,
    orgId,
    actorUserId,
  }: {
    db: Prisma.TransactionClient;
    questionId: string;
    orgId: string;
    actorUserId: string;
  }) {
    // recipient of the notification is the AssignedQuestion.createdBy user
    const assignedQuestion = await db.assignedQuestion.findFirst({
      where: {
        questionId,
        orgId,
      },
      include: {
        createdBy: true,
      },
    });

    const question = await db.question.findFirst({
      where: {
        id: questionId,
        orgId,
      },
      include: {
        questionContents: true,
        response: {
          include: {
            documents: {
              include: {
                document: true,
              },
            },
          },
        },
      },
    });

    if (!assignedQuestion || !question?.questionContents?.length) {
      throw new Error("Assigned question not found");
    }

    // const questionContent = question?.questionContents[0]?.content;
    // const questionContentText = questionContent
    //   ? (questionContent as QuestionContentType).text
    //   : "";

    // const document = question.response?.documents[0]?.document;
    // if (!document) {
    //   throw new Error("Document not found");
    // }

    // await knockUtils.notification({
    //   orgId,
    //   data: {
    //     medium: "email",
    //     type: "approve",
    //     title: "Question was approved on Virgil.",
    //     message: `Your question has been approved on Virgil. Click on the link below to view the question.`,
    //     batch_content: `Question "${questionContentText}" of document "${document?.name}"`,
    //     link: `${process.env.VERCEL_PROJECT_PRODUCTION_URL ?? process.env.NGROK_DOMAIN}/dashboard/response?tab=all`,
    //     link_label: "Click to view in Virgil",
    //     batch_id: "approve|email",
    //   },
    //   recipients: [
    //     {
    //       id: assignedQuestion.createdBy.id,
    //       name: assignedQuestion.createdBy.name ?? "",
    //       email: assignedQuestion.createdBy.email ?? "",
    //     },
    //   ],
    //   actor: {
    //     id: actorUserId,
    //     name: assignedQuestion.createdBy.name ?? "",
    //     email: assignedQuestion.createdBy.email ?? "",
    //   },
    // });
  }
}

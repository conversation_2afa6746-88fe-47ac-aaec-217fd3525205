import { getFilenameFromLocalUrl } from "~/server/utils/getFilenameFromLocalUrl";

describe("DocumentManager", () => {
  describe("getFilenameFromLocalUrl", () => {
    it("should extract filename from Windows path with drive letter", () => {
      const windowsPath =
        "C:\\Users\\<USER>\\OneDrive - Virgil AI\\Empty DDQ demos\\Word\\PIFSS Unified DDQ - Open End - OH DCS 2025_BLANK.docx";
      const expected = "PIFSS Unified DDQ - Open End - OH DCS 2025_BLANK.docx";

      const result = getFilenameFromLocalUrl(windowsPath);

      expect(result).toBe(expected);
    });

    it("should extract filename from Unix path", () => {
      const unixPath = "/users/name.docx";
      const expected = "name.docx";

      const result = getFilenameFromLocalUrl(unixPath);

      expect(result).toBe(expected);
    });

    it("should extract filename from Windows path with forward slashes", () => {
      const windowsPath = "C:/Users/<USER>/Documents/file.pdf";
      const expected = "file.pdf";

      const result = getFilenameFromLocalUrl(windowsPath);

      expect(result).toBe(expected);
    });

    it("should extract filename from relative path", () => {
      const relativePath = "documents/report.docx";
      const expected = "report.docx";

      const result = getFilenameFromLocalUrl(relativePath);

      expect(result).toBe(expected);
    });

    it("should extract filename from path with spaces", () => {
      const pathWithSpaces = "C:\\My Documents\\Important File.docx";
      const expected = "Important File.docx";

      const result = getFilenameFromLocalUrl(pathWithSpaces);

      expect(result).toBe(expected);
    });

    it("should extract filename from path with special characters", () => {
      const pathWithSpecialChars =
        "/home/<USER>/documents/file-with-dashes_and_underscores.txt";
      const expected = "file-with-dashes_and_underscores.txt";

      const result = getFilenameFromLocalUrl(pathWithSpecialChars);

      expect(result).toBe(expected);
    });

    it("should handle empty string", () => {
      const emptyPath = "";
      const expected = "";

      const result = getFilenameFromLocalUrl(emptyPath);

      expect(result).toBe(expected);
    });

    it("should handle path ending with slash", () => {
      const pathEndingWithSlash = "/path/to/directory/";
      const expected = "";

      const result = getFilenameFromLocalUrl(pathEndingWithSlash);

      expect(result).toBe(expected);
    });

    it("should handle just filename", () => {
      const justFilename = "document.pdf";
      const expected = "document.pdf";

      const result = getFilenameFromLocalUrl(justFilename);

      expect(result).toBe(expected);
    });
  });
});

import pgvector from "pgvector";
import { VoyageAIEmbedding } from "~/lib/rag-response/utils/voyageai-embedding-openai-compat";
import { type PrismaClientType } from "../db";

type SearchResultType = "question" | "response";

export type SearchResult = {
  type: SearchResultType;
  id: string;
  question_id: string;
  response_id: string;
  content: {
    text?: string;
    question_context?: string;
    answer_context?: string;
    [key: string]: any;
  } | null;
  vector_distance: number;
  bm25_score: number;
  combined_score: number;
  question_context: string;
  answer_context: string;
  response_distance?: number;
  question_distance?: number;
  relevance_score?: number;
  reasoning: string | null;
}

export async function responseLibrarySemanticSearch({
  db,
  query,
  orgId,
  currentDocumentId,
  limit = 5,
  returnEmptyResponses = false,
  secret,
}: {
  db: PrismaClientType;
  query: string;
  orgId: string;
  currentDocumentId: string;
  limit?: number;
  returnEmptyResponses?: boolean;
  secret?: Record<string, string>;
}): Promise<SearchResult[]> {
  const embedding = new VoyageAIEmbedding(secret);
  const qEmb: number[] = await embedding.embedQuery(query);
  const queryEmbedding = pgvector.toSql(qEmb);
  // Preprocess the query for text search
  const searchTerms = query
    .toLowerCase()
    .replace(/[^\w\s]/g, " ") // Replace special characters with spaces
    .split(/\s+/) // Split on whitespace
    .filter((term) => term.length > 2) // Remove very short terms
    .map((term) => term.replace(/'/g, "''")) // Escape single quotes
    .join(" & "); // Combine with AND operator

  // Preprocess query for exact matching
  const cleanQuery = query
    .toLowerCase() // Normalize case
    .replace(/[\n\r]/g, " ") // Replace newlines with spaces
    .replace(/\s+/g, " ") // Replace multiple spaces with single space
    .trim(); // Remove leading/trailing whitespace

  console.log("Original query:", query);
  console.log("Cleaned query:", cleanQuery);

  // Function to clean text in the same way as the query
  const cleanText = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[\n\r]/g, " ")
      .replace(/\s+/g, " ")
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, " ")
      .trim();
  };

  const MAX_RETRIES = 5;
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      const results = await db.$queryRaw`
    WITH combined_results AS (
      -- Search QuestionContent
      SELECT 
        'question' as type,
        qc.id,
        qc.content,
        qc.vector <=> ${queryEmbedding}::vector as vector_distance,
        COALESCE(qc.content->>'text', '') as search_text,
        COALESCE(qc.content->>'question_context', '') as question_context,
        COALESCE(qc.content->>'answer_context', '') as answer_context,
        CASE 
          WHEN LOWER(TRIM(qc.content->>'text')) = LOWER(TRIM(${cleanQuery})) THEN 1.0
          WHEN LOWER(TRIM(qc.content->>'text')) LIKE LOWER(TRIM(${`%${cleanQuery}%`})) THEN 0.8
          WHEN to_tsvector('english', qc.content->>'text') @@ to_tsquery('english', ${searchTerms}) THEN 0.6
          ELSE ts_rank_cd(
            to_tsvector('english', qc.content->>'text'),
            to_tsquery('english', ${searchTerms})
          )
        END as bm25_score,
        CASE 
          WHEN LOWER(TRIM(qc.content->>'text')) = LOWER(TRIM(${cleanQuery})) THEN 1.0
          WHEN LOWER(TRIM(qc.content->>'text')) LIKE LOWER(TRIM(${`%${cleanQuery}%`})) THEN 0.8
          WHEN to_tsvector('english', qc.content->>'text') @@ to_tsquery('english', ${searchTerms}) THEN 0.6
          ELSE 0
        END as exact_match_score,
        qc."questionId" as question_id,
        q."responseId" as response_id
      FROM "QuestionContent" qc
      LEFT JOIN "Question" q ON q.id = qc."questionId"
      LEFT JOIN "Response" r ON r.id = q."responseId"
      LEFT JOIN "DocumentResponses" dr ON dr."responseId" = r.id
      LEFT JOIN "ResponseContent" rc ON rc."responseId" = r.id
      WHERE qc."orgId" = ${orgId}
        AND qc.vector IS NOT NULL
        AND qc.content->>'text' IS NOT NULL
        AND (dr."documentId" IS NULL OR dr."documentId" != ${currentDocumentId})
        AND (${returnEmptyResponses} OR LENGTH(TRIM(qc.content->>'text')) > 0)
        AND (rc.content->>'text' IS NOT NULL AND LENGTH(TRIM(rc.content->>'text')) > 0)
        
      
      UNION ALL
      
      -- Search ResponseContent
      SELECT 
        'response' as type,
        rc.id,
        rc.content,
        rc.vector <=> ${queryEmbedding}::vector as vector_distance,
        COALESCE(rc.content->>'text', '') as search_text,
        COALESCE(rc.content->>'question_context', '') as question_context,
        COALESCE(rc.content->>'answer_context', '') as answer_context,
        CASE 
          WHEN LOWER(TRIM(rc.content->>'text')) = LOWER(TRIM(${cleanQuery})) THEN 1.0
          WHEN LOWER(TRIM(rc.content->>'text')) LIKE LOWER(TRIM(${`%${cleanQuery}%`})) THEN 0.8
          WHEN to_tsvector('english', rc.content->>'text') @@ to_tsquery('english', ${searchTerms}) THEN 0.6
          ELSE ts_rank_cd(
            to_tsvector('english', rc.content->>'text'),
            to_tsquery('english', ${searchTerms})
          )
        END as bm25_score,
        CASE 
          WHEN LOWER(TRIM(rc.content->>'text')) = LOWER(TRIM(${cleanQuery})) THEN 1.0
          WHEN LOWER(TRIM(rc.content->>'text')) LIKE LOWER(TRIM(${`%${cleanQuery}%`})) THEN 0.8
          WHEN to_tsvector('english', rc.content->>'text') @@ to_tsquery('english', ${searchTerms}) THEN 0.6
          ELSE 0
        END as exact_match_score,
        NULL as question_id,
        rc."responseId" as response_id
      FROM "ResponseContent" rc
      LEFT JOIN "Response" r ON r.id = rc."responseId"
      LEFT JOIN "DocumentResponses" dr ON dr."responseId" = r.id
      WHERE rc."orgId" = ${orgId}
        AND rc.vector IS NOT NULL
        AND rc.content->>'text' IS NOT NULL
        AND (dr."documentId" IS NULL OR dr."documentId" != ${currentDocumentId})
        AND (${returnEmptyResponses} OR LENGTH(TRIM(rc.content->>'text')) > 0)
    )
    SELECT 
      *,
      -- Combine scores: lower vector_distance is better, higher bm25_score is better, exact_match_score is 1 or 0
      (1 - vector_distance) * 0.5 + bm25_score * 0.3 + exact_match_score * 0.2 as combined_score
    FROM combined_results
    ORDER BY combined_score DESC
    LIMIT ${limit}
  `;

      return results as SearchResult[];
    } catch (error) {
      console.error(
        "Failed to search response library. Retrying...",
        error,
        retries,
      );
      retries++;
    }
  }

  throw new Error("Failed to search response library");
}

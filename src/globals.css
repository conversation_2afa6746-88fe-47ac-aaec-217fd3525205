@import "tailwindcss";
@import "tw-animate-css";
/*
  ---break---
*/
@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans:
    "<PERSON><PERSON>", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --text-xs: 0.625rem; /* 10px */
  --text-sm: 0.75rem; /* 12px */
  --text-base: 0.875rem; /* 14px */
  --text-lg: 1rem; /* 16px */
  --text-xl: 1.125rem; /* 18px */
  --text-2xl: 1.25rem; /* 20px */
  --text-3xl: 1.5rem; /* 24px */
  --text-4xl: 1.75rem; /* 28px */
  --text-5xl: 2rem; /* 32px */

  /* line height */
  --text-xs--line-height: 1rem; /* 16px */
  --text-sm--line-height: 1.25rem; /* 20px */
  --text-base--line-height: 1.5rem; /* 24px */
  --text-lg--line-height: 1.625rem; /* 26px */
  --text-xl--line-height: 1.75rem; /* 28px */
  --text-2xl--line-height: 2rem; /* 32px */
  --text-3xl--line-height: 2.25rem; /* 36px */
  --text-4xl--line-height: 2.5rem; /* 40px */
  --text-5xl--line-height: 2.5rem; /* 40px */

  /* Status Colors - matching Material-UI theme */
  --color-success: var(--primary);
  --color-warning: #ffab00;
  --color-error: #ff5630;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
/*
  ---break---
*/
@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-active: rgba(94, 135, 121, 0.16);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-text-secondary: var(--text-text-secondary);
}
/*
  ---break---
*/
:root {
  color-scheme: light only;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 28px;
  --spacing-8: 32px;
  --radius: 0.625rem;
  --background: #f9fafb;
  --foreground: #1c252e;
  --card: #f9fafb;
  --card-foreground: #0a0a0a;
  --popover: #ffffff;
  --popover-foreground: #1c252e;
  --primary: #5e8779;
  --primary-foreground: #ffffff;
  --secondary: #11363f;
  --secondary-foreground: #ffffff;
  --muted: #f5f5f5;
  --muted-foreground: #637381;
  --accent: #f4f6f8;
  --accent-foreground: #1c252e;
  --destructive: #ff5630;
  --border: #dfe3e8;
  --input: #dfe3e8;
  --ring: #5e8779;
  --chart-1: #5e8779;
  --chart-2: #11363f;
  --chart-3: #22c55e;
  --chart-4: #ffab00;
  --chart-5: #ff5630;
  --chart-6: #ff0000;
  --chart-7: #000000;
  --sidebar: #fafafa;
  --sidebar-foreground: #171717;
  --sidebar-primary: #5e8779;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f4f6f8;
  --sidebar-accent-foreground: #171717;
  --sidebar-border: #dfe3e8;
  --sidebar-ring: #5e8779;
  --text-text-secondary: #6b7280;
  --colors-component-selected: rgba(94, 135, 121, 0.12);

  /* Border and shadow variables */
  --border-width-border: 1px;
  --base-input: #e5e5e5;
  --custom-background-dark-input-30: #fff;
  --shadow-xs-offset-x: 0px;
  --shadow-xs-offset-y: 0px;
  --shadow-xs-blur-radius: 2px;
  --shadow-xs-spread-radius: 0px;
  --shadow-xs-color: rgba(0, 0, 0, 0.05);
  --shadow-color: rgba(0, 0, 0, 0.1);

  --border-radius-icon: 0.25rem; /* 4px */
}
/*
  ---break---
*/
.dark {
  --background: #141a21;
  --foreground: #ffffff;
  --card: #1c252e;
  --card-foreground: #ffffff;
  --popover: #1c252e;
  --popover-foreground: #ffffff;
  --primary: #7eb5a6;
  --primary-foreground: #1c252e;
  --secondary: #3d7380;
  --secondary-foreground: #ffffff;
  --muted: #454f5b;
  --muted-foreground: #919eab;
  --accent: #454f5b;
  --accent-foreground: #ffffff;
  --destructive: #ffac82;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: #7eb5a6;
  --chart-1: #7eb5a6;
  --chart-2: #3d7380;
  --chart-3: #77ed8b;
  --chart-4: #ffd666;
  --chart-5: #ffac82;
  --sidebar: #1c252e;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #7eb5a6;
  --sidebar-primary-foreground: #1c252e;
  --sidebar-accent: #454f5b;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: #7eb5a6;

  /* Border and shadow variables for dark mode */
  --border-width-border: 1px;
  --base-input: rgba(255, 255, 255, 0.15);
  --custom-background-dark-input-30: #1c252e;
  --shadow-xs-offset-x: 0px;
  --shadow-xs-offset-y: 0px;
  --shadow-xs-blur-radius: 2px;
  --shadow-xs-spread-radius: 0px;
  --shadow-xs-color: rgba(0, 0, 0, 0.2);
}
/*
  ---break---
*/
@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/** **************************************
* Fonts: Roboto
*************************************** */
@import "@fontsource/roboto/300.css";
@import "@fontsource/roboto/400.css";
@import "@fontsource/roboto/500.css";
@import "@fontsource/roboto/700.css";
@import "@fontsource/roboto/900.css";

/** **************************************
* Plugins
*************************************** */
/* scrollbar */
@import "./components/scrollbar/styles.css";

/* image */
@import "./components/image/styles.css";

/* lightbox */
@import "./components/lightbox/styles.css";

/* chart */
@import "./components/chart/styles.css";

@import "github-markdown-css/github-markdown.css";

/** **************************************
* Baseline
*************************************** */
html {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
body,
#root,
#root__layout {
  display: flex;
  flex: 1 1 auto;
  min-height: 100%;
  flex-direction: column;
}
img {
  max-width: 100%;
  vertical-align: middle;
}
ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
input[type="number"] {
  -moz-appearance: textfield;
  appearance: none;
}
input[type="number"]::-webkit-outer-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
input[type="number"]::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}

/* Clerk component styles */
.cl-rootBox {
  max-width: 100%;
}
.cl-cardBox {
  max-width: 100%;
}
.cl-footer {
  display: none;
}

.markdown-body {
  box-sizing: border-box;
  width: 100%;
  background-color: inherit !important;
  color: inherit !important;
  font-size: inherit !important;
  cursor: pointer;
}

.markdown-body ul {
  list-style-type: disc;
}

/* Markdown table styling */
.markdown-body table {
  border-spacing: 0 !important;
  border-collapse: collapse !important;
  /* border-color: inherit !important; */
  /* border-style: solid !important;
  border-width: 0.5px !important; */

  display: block !important;
  margin: 0 auto !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow: auto !important;
  background-color: inherit !important;
  color: inherit !important;
  /* border-radius: 0.5rem !important; */
}

.markdown-body thead {
  border-radius: 0.5rem !important;
}

.markdown-body th {
  border-color: inherit !important;
  border-style: solid !important;
  border-width: 0.5px !important;
  padding: 0.5rem;
  background-color: #404f65 !important;
  color: white !important;
}
.markdown-body tbody,
.markdown-body td,
.markdown-body tfoot,
.markdown-body tr {
  border-color: inherit !important;
  border-style: solid !important;
  border-width: 0.5px !important;
  padding: 0.5rem;
  background-color: inherit !important;
  color: inherit !important;
}
.markdown-body.add-in.glowing * {
  background: linear-gradient(
    to right,
    #7953cd 20%,
    #00affa 30%,
    #0190cd 70%,
    #764ada 80%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  background-size: 500% auto;
  animation: textShine 3s ease-in-out infinite alternate;
}

@keyframes textShine {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.markdown-body.add-in.fade * {
  animation: fadeInOut 1.7s ease-in-out infinite;
}

@keyframes fadeInOut {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

/* add in styles */
.markdown-body.add-in {
  font-size: 10px !important;
  background-color: transparent !important;
  max-width: 100%;
  overflow-x: auto;
}

.markdown-body.add-in .table-wrapper {
  width: 100%;
  overflow-x: scroll;
  margin: 1rem 0;
  padding-right: 10px;
}

.markdown-body.add-in table {
  min-width: 100%;
}
.markdown-body.add-in tbody,
.markdown-body.add-in td,
.markdown-body.add-in tfoot,
.markdown-body.add-in tr {
  padding: 0.3rem;
  font-size: 10px !important;
}

.markdown-body.add-in h6 {
  font-size: 10px !important;
  color: var(--palette-text-primary) !important;
}

.markdown-body.add-in h3 {
  color: #404f65 !important;
}

.add-in-table table {
  border-collapse: collapse;
  width: 100%;
}
.add-in-table th,
.add-in-table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}
.add-in-table th {
  background-color: #4a90e2;
  color: white;
  font-weight: bold;
}
.add-in-table td:first-child {
  font-weight: bold;
}

.add-in-table .generated {
  background: color-mix(in oklab, var(--ring) 50%, transparent);
}

.markdown-body ol {
  list-style-type: decimal;
}
.markdown-body ul {
  list-style-type: disc;
}

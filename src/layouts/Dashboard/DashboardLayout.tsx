"use client";

import { AppSidebar } from "~/v2/components/composit/AppSidebar/AppSidebar";
import { SidebarProvider } from "~/v2/components/ui/Sidebar";
import { NavProvider } from "~/v2/contexts/NavContext";
import { DashboardLayoutProvider } from "./DashboardLayoutContext";

export type DashboardLayoutProps = {
  children: React.ReactNode;
  data?: {
    nav?: any;
  };
};

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={false}>
      <AppSidebar />
      <div className="flex flex-1 overflow-x-hidden h-screen bg-gray-50">
        <div className="flex-1 flex flex-col overflow-x-hidden">
          <DashboardLayoutProvider>
            <NavProvider>
              <main className="flex flex-col flex-1 w-full h-full">
                {children}
              </main>
            </NavProvider>
          </DashboardLayoutProvider>
        </div>
      </div>
    </SidebarProvider>
  );
}

"use client";

import { useEffect } from "react";
import { Typography } from "~/v2/components";
import { useNavContext } from "~/v2/contexts/NavContext";
import { useTabsWithQueryParam } from "~/hooks/useTabsWithQueryParam";
import { useDashboardLayout } from "./DashboardLayoutContext";

type Tab = {
  value: string;
  label: string;
};

type DashboardHeaderProps = {
  children?: React.ReactNode;
  title: string;
  subtitle?: string;
  tabs?: Tab[];
  actions?: React.ReactNode;
}

export function DashboardHeader({
  children,
  title,
  subtitle,
  tabs,
  actions,
}: DashboardHeaderProps) {

  const { Tabs, currentTab } = useTabsWithQueryParam({ tabs });
  const { setCurrentTab } = useNavContext();

  const { headerElevated } = useDashboardLayout();

  // Update NavContext when currentTab changes
  useEffect(() => {
    if (currentTab) {
      setCurrentTab(currentTab);
    }
  }, [currentTab, setCurrentTab]);

  return (
    <div className={`flex flex-col items-start bg-white p-4 pb-2 z-10 relative transition-shadow duration-200 ${headerElevated ? 'shadow-[0_12px_10px_-10px_gray]' : ''}`}>
      <div className="flex items-start gap-2 w-full">
        <div className="flex flex-col gap-2 mb-4">
          <Typography variant="h3">{title}</Typography>
          {subtitle && <Typography variant="body" className="text-muted-foreground">{subtitle}</Typography>}
        </div>
        <div className="flex-1" />
        {actions}
      </div>
      
      {tabs && Tabs}
      
      {children}
    </div>
  )
}

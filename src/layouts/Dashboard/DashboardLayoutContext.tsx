import React, { createContext, useContext, useState, ReactNode } from 'react';

interface DashboardLayoutContextType {
  headerElevated: boolean;
  setHeaderElevated: (elevated: boolean) => void;
}

const DashboardLayoutContext = createContext<DashboardLayoutContextType | undefined>(undefined);

interface DashboardLayoutProviderProps {
  children: ReactNode;
}

export const DashboardLayoutProvider: React.FC<DashboardLayoutProviderProps> = ({ children }) => {
  const [headerElevated, setHeaderElevated] = useState<boolean>(false);

  const value: DashboardLayoutContextType = {
    headerElevated,
    setHeaderElevated,
  };

  return (
    <DashboardLayoutContext.Provider value={value}>
      {children}
    </DashboardLayoutContext.Provider>
  );
};

export const useDashboardLayout = (): DashboardLayoutContextType => {
  const context = useContext(DashboardLayoutContext);
  if (context === undefined) {
    throw new Error('useDashboardLayout must be used within a DashboardLayoutProvider');
  }
  return context;
};

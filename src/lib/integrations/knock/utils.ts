import { env } from "~/env";
import { Knock } from "@knocklabs/node";
import z from "zod";

export interface Recipient {
  id: string;
  name: string;
  email: string;
}

export interface Tenant {
  id: string;
  name: string;
}

export enum Workflow {
  Notification = "notification",
}

export const NotificationMessage = z.object({
  medium: z.enum(["email", "slack", "team"]),
  type: z.enum(["tag", "approve"]),
  title: z.string(),
  message: z.string(),
  link: z.string(),
  batch_id: z.string().optional().nullish(),
  batch_content: z.string().optional().nullish(),
  link_label: z.string(),
});

export type NotificationMessage = z.infer<typeof NotificationMessage>;

export class KnockUtils {
  private static instance: KnockUtils;
  private knock?: Knock;

  private constructor() {
    if (!env.KNOCK_API_KEY) {
      console.error(
        "KNOCK_API_KEY is not set and KnockUtils will not be initialized",
      );
      return;
    }

    this.knock = new Knock({ apiKey: env.KNOCK_API_KEY });
  }

  public static getInstance(): KnockUtils {
    if (!KnockUtils.instance) {
      KnockUtils.instance = new KnockUtils();
    }
    return KnockUtils.instance;
  }

  notification(options: {
    orgId: string;
    data: NotificationMessage;
    recipients: Recipient[];
    actor?: Recipient;
  }) {
    return this.trigger({
      workflow: Workflow.Notification,
      orgId: options.orgId,
      data: options.data,
      actor: options.actor,
      recipients: options.recipients,
    });
  }

  async upsertUser(options: Recipient) {
    if (!this.knock) {
      return;
    }

    const user = await this.knock.users.update(options.id, {
      email: options.email,
      name: options.name,
      timezone: "America/New_York",
    });

    return user;
  }

  async upsertTenant(options: Tenant) {
    if (!this.knock) {
      return;
    }

    const tenant = await this.knock.tenants.set(options.id, {
      name: options.name,
    });

    return tenant;
  }

  async trigger(options: {
    workflow: Workflow;
    orgId: string;
    data: NotificationMessage;
    actor?: Recipient;
    recipients: Recipient[];
  }) {
    if (!this.knock) {
      return;
    }

    const unique_batch_id = [
      ...[options.data.batch_id],
      ...options.recipients.map((r) => r.email),
    ].sort();

    const data = { ...options.data, batch_id: unique_batch_id.join("|") };

    console.log(
      `>>> Send Knock notification with`,
      `Recipients:`,
      options.recipients,
      `Actor:`,
      options.actor,
      `Data:`,
      data,
    );

    return await this.knock.workflows.trigger(options.workflow, {
      data: data,
      actor: options.actor
        ? {
            id: options.actor.id,
            name: options.actor.name,
            email: options.actor.email,
          }
        : undefined,
      recipients: options.recipients.map((recipient) => ({
        id: recipient.id,
        name: recipient.name,
        email: recipient.email,
      })),
      tenant: options.orgId,
    });
  }
}

export const knockUtils = KnockUtils.getInstance();

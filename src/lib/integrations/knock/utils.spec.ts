import { Command } from "commander";
import { db } from "~/server/db";
import { knockUtils } from "./utils";

async function main() {
  const program = new Command();

  program
    .name("knock")
    .description("Send notification to user")
    .option(
      "-u, --user-email <address>",
      "user email to immitate, if not provided, will use the default testing user",
    )
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const { user, org } = await db.userOrg.findFirstOrThrow(
    true
      ? {
          where: {
            user: {
              email: options.userEmail ?? "<EMAIL>",
            },
          },
          select: {
            orgId: true,
            org: {
              select: {
                id: true,
                name: true,
              },
            },
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        }
      : void 0,
  );

  const userRes = await knockUtils.upsertUser({
    id: user.id,
    name: user.name ?? "",
    email: user.email,
  });

  console.log(userRes);

  const tenantRes = await knockUtils.upsertTenant({
    id: org.id,
    name: org.name ?? "",
  });

  console.log(tenantRes);

  const notificationRes = await knockUtils.notification({
    orgId: org.id,
    data: {
      medium: "email",
      type: "tag",
      title: "Pending approval on Virgil",
      message: `Your review has been requested on Virgil. Click on the link below to view the items pending your approval.`,
      link: "https://virgil-dev.asymmetry.ai/dashboard/data-room",
      link_label: "Click to Review in Virgil",
      batch_content: "Question 'Fund Size' of document 'Gallatin Equity V - Q3 2023 Quarterly Report.pdf'",
      batch_id: "tag|email",
    },
    recipients: [{ id: user.id, name: user.name ?? "", email: user.email }],
    actor: {
      id: user.id,
      name: user.name ?? "",
      email: user.email,
    },
  });

  const notificationRes2 = await knockUtils.notification({
    orgId: org.id,
    data: {
      medium: "email",
      type: "tag",
      title: "Pending approval on Virgil",
      message: `Your review has been requested on Virgil. Click on the link below to view the items pending your approval.`,
      link: "https://virgil-dev.asymmetry.ai/dashboard/data-room",
      link_label: "Click to Review in Virgil",
      batch_content: "Question 'Capital Deployed' of document 'Gallatin Equity V - Q3 2023 Quarterly Report.pdf'",
      batch_id: "tag|email",
    },
    recipients: [{ id: user.id, name: user.name ?? "", email: user.email }],
    actor: {
      id: user.id,
      name: user.name ?? "",
      email: user.email,
    },
  });

  console.log(notificationRes, notificationRes2);
}

main().catch(console.error);
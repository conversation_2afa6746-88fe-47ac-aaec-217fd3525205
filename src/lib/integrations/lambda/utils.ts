import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { env } from "~/env";

type InvokeHttpOpts = {
  funcName: string;
  path: string; // e.g. "/pyrpc/generate-answer/classifier-document-category"
  payload?: unknown; // your JSON body
  method?: "POST" | "GET" | "PUT" | "DELETE" | "PATCH" | "OPTIONS" | "HEAD";
  region?: string;
};

function decodePayload(payload?: Uint8Array | number[] | string | null) {
  if (!payload) return null;

  const bytes =
    payload instanceof Uint8Array
      ? payload
      : Uint8Array.from(payload as number[]);

  let text = new TextDecoder("utf-8").decode(bytes);

  // Remove NULs that can appear as padding between chunks
  text = text.replace(/\u0000/g, "").trim();

  // Try clean JSON first
  try {
    return JSON.parse(text);
  } catch {
    // Fallback: handle accidental concatenated JSON like {}{}
    const parts = text
      .split(/}\s*(?=\s*{)/) // split on boundary between } and next {
      .map((p, i, a) => (i < a.length - 1 ? p + "}" : p));

    if (parts.length) {
      const meta = JSON.parse(parts[0]!);
      const body = JSON.parse(parts[1]!);

      return { statusCode: meta.statusCode, headers: meta.headers, body };
    }

    return text;
  }
}

export async function invokeLambda(
  functionName: string,
  payload: Record<string, unknown>,
  path?: string,
) {
  const lambdaParams = {
    region: env.AWS_LAMBDA_REGION,
    credentials: process.env.AWS_LAMBDA_ACCESS_KEY
      ? {
          accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
          secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
        }
      : undefined,
  };

  const lambdaClient = new LambdaClient(lambdaParams);
  const funcName = `${functionName}-${env.LAMBDA_ENV}`;

  if (path) {
    return await invokeLambdaHttp({
      funcName,
      path,
      payload,
    });
  }

  const invokeParams: InvokeCommandInput = {
    FunctionName: funcName,
    InvocationType: "RequestResponse",
    Payload: JSON.stringify(payload),
  };

  try {
    console.log(`>>> ${funcName} Lambda invoked`);

    const command = new InvokeCommand(invokeParams);
    const response = await lambdaClient.send(command);

    const buffer = Buffer.from(response.Payload ?? "").toString();

    console.log(`<<< ${funcName} Lambda invoked`, buffer);

    const jsonResponse = JSON.parse(buffer);

    if (jsonResponse?.statusCode === 200) {
      return jsonResponse?.body;
    }

    throw new Error(jsonResponse?.body);
  } catch (error) {
    console.error(
      `Error invoking ${functionName}-${env.LAMBDA_ENV} Lambda`,
      error,
    );

    return null;
  }
}

export async function invokeLambdaHttp({
  funcName,
  path,
  payload = {},
  method = "POST",
  region,
}: InvokeHttpOpts) {
  const lambdaClient = new LambdaClient({ region });

  // Build an API Gateway HTTP API v2-compatible event
  const event = {
    version: "2.0",
    routeKey: `${method} ${path}`,
    rawPath: path,
    rawQueryString: "",
    headers: {
      "content-type": "application/json",
    },
    requestContext: {
      http: {
        method,
        path,
        protocol: "HTTP/1.1",
        sourceIp: "0.0.0.0",
        userAgent: "aws-sdk-js-v3",
      },
    },
    isBase64Encoded: false,
    body: JSON.stringify(payload ?? {}),
  };

  const invokeParams: InvokeCommandInput = {
    FunctionName: funcName,
    InvocationType: "RequestResponse",
    Payload: new TextEncoder().encode(JSON.stringify(event)),
  };

  console.log(`>>> Invoking Lambda ${funcName} ${method} ${path}`);

  const cmd = new InvokeCommand(invokeParams);
  const res = await lambdaClient.send(cmd);

  // Handle function errors (e.g., unhandled exceptions in Lambda)
  if (res.FunctionError) {
    const errPayload = res.Payload ? new TextDecoder().decode(res.Payload) : "";
    throw new Error(`Lambda error (${res.FunctionError}): ${errPayload}`);
  }

  // Decode the Lambda response payload
  const decoded: any = decodePayload(res.Payload);

  // If your Lambda returns a proxy response { statusCode, headers, body, isBase64Encoded }
  if (decoded && typeof decoded === "object" && "body" in decoded) {
    const proxy = decoded as {
      statusCode: number;
      headers?: Record<string, string>;
      body?: string;
      isBase64Encoded?: boolean;
    };

    let body: unknown = proxy.body;
    if (typeof proxy.body === "string") {
      try {
        body = JSON.parse(proxy.body);
      } catch {
        // body is plain text; keep as-is
      }
    }

    console.log(`<<< ${funcName} Lambda invoked`, body);

    return body;
  }

  // Otherwise return the function’s direct result
  return decoded;
}

import { Sha256 } from "@aws-crypto/sha256-js";
import { AssumeRoleCommand, Credentials, STSClient } from "@aws-sdk/client-sts";
import { HttpRequest } from "@aws-sdk/protocol-http";
import { SignatureV4 } from "@aws-sdk/signature-v4";
import { env } from "~/env";
import { getPyrpcApiUrl } from "~/utils/url";

async function getAWSLambdaSessionToken() {
  const stsClient = new STSClient({
    region: env.AWS_LAMBDA_REGION,
    credentials: process.env.AWS_LAMBDA_ACCESS_KEY
      ? {
          accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
          secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
        }
      : undefined,
  });

  const input = {
    RoleArn: process.env.AWS_LAMBDA_EXECUTION_ROLE_ARN,
    RoleSessionName: "chat-session", // required
    DurationSeconds: 3600,
  };
  const command = new AssumeRoleCommand(input);
  const response = await stsClient.send(command);

  return response.Credentials;
}

async function getAWSLambdaSignedRequest(
  lambdaUrl: string,
  credentials: Credentials,
  payload: string,
) {
  const url = new URL(lambdaUrl);

  // set up the HTTP request
  const request = new HttpRequest({
    hostname: url.hostname,
    path: url.pathname,
    body: payload,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      host: url.hostname,
    },
  });

  const signer = new SignatureV4({
    credentials: {
      accessKeyId: credentials.AccessKeyId ?? "",
      secretAccessKey: credentials.SecretAccessKey ?? "",
      sessionToken: credentials.SessionToken ?? "",
    },
    service: "lambda",
    region: env.AWS_LAMBDA_REGION,
    sha256: Sha256,
  });

  const { headers, body, method } = await signer.sign(request);

  return { headers, body, method };
}

export async function invokeRemotePyrpc(
  path: string,
  payload: Record<string, unknown>,
): Promise<Response> {
  // Run in local
  if (
    process.env.NODE_ENV !== "production" &&
    (!process.env.EXECUTE_LAMBDAS_ON_LOCAL_DEV ||
      process.env.EXECUTE_LAMBDAS_ON_LOCAL_DEV === "false")
  ) {
    console.log(`Calling ${path}`);

    const response = await fetch(`${getPyrpcApiUrl()}/${path}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (
      !response.ok ||
      response.status !== 200 ||
      !response.body
    ) {
      throw new Error(`HTTP error! status: ${response.status}, URL: ${path}`);
    }

    return response;
  }

  // Run in Vercel
  if (
    process.env.CHUNKING_API_URL &&
    process.env.AWS_LAMBDA_ACCESS_KEY &&
    (process.env.EXECUTE_LAMBDAS_ON_LOCAL_DEV === "true" ||
      process.env.NODE_ENV === "production")
  ) {
    console.log("Invoking AWS Lambda Function Using Signed Request");

    const credentials = await getAWSLambdaSessionToken();
    console.log("AWS Lambda credentials", credentials);

    if (!credentials) {
      throw new Error("No AWS Lambda credentials found");
    }

    const lambdaUrl = `${process.env.CHUNKING_API_URL}/${path}`;
    const { headers, body, method } = await getAWSLambdaSignedRequest(
      lambdaUrl,
      credentials,
      JSON.stringify(payload),
    );

    const response = await fetch(lambdaUrl, {
      method: method,
      headers: headers,
      body: body,
    });

    console.log("AWS Lambda response", response);

    return response;
  }

  // Run in Lambda Function
  if (
    process.env.CHUNKING_API_URL &&
    !process.env.AWS_LAMBDA_ACCESS_KEY &&
    (process.env.EXECUTE_LAMBDAS_ON_LOCAL_DEV === "true" ||
      process.env.NODE_ENV === "production")
  ) {
    console.log("Invoke Pyrpc API Lambda function from Lambda Function" );

    const lambdaUrl = `${process.env.CHUNKING_API_URL}/${path}`;
    const response = await fetch(lambdaUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    console.log("AWS Lambda response", response);

    return response;
  }

  throw new Error("No AWS Lambda credentials found");
}

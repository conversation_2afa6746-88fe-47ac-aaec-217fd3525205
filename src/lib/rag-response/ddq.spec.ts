import { Command } from "commander";
import xlsx from "xlsx";
import { stringify } from "csv-stringify";
import { createWriteStream } from "fs";
import * as fs from "fs/promises";
import * as yaml from "js-yaml";
import path from "path";
import { promisify } from "util";
import { FinancialEmbedding } from "~/lib/rag-response/utils/financial-embedding-openai-compat";
import { db } from "~/server/db";
import { generateResponseRAGFromDocumentQuestions, QuestionInput } from ".";
import { ModelParameters, QuestionContentType } from "../types";

const stringifyAsync: any = promisify(stringify);

import { Question, QuestionContent, QuestionType } from "@prisma/client";
import { ResponseStyle } from "../types";

function redirectLog() {
  const log = console.log;
  console.log = () => {};
  const restore = () => {
    console.log = log;
  };
  return { log, restore };
}

const { log, restore } = redirectLog();

async function processReadmeFile(filePath: string): Promise<
  {
    No: number;
    Question: string;
    "Ground Truth Answer": string | undefined;
    "Ground Truth Citations": string;
  }[]
> {
  // Read the Excel file
  const workbook = xlsx.readFile(filePath);

  // eslint-disable-next-line @typescript-eslint/dot-notation
  const sheet = workbook.Sheets["DDQ"];

  if (!sheet) {
    return [];
  }

  // Convert sheet to JSON
  const jsonData = xlsx.utils.sheet_to_json(sheet!);

  const questionAnswers = jsonData as any;

  return questionAnswers;
}

function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dotProduct = vecA.reduce((acc, val, i) => acc + val * vecB[i]!, 0);
  const normA = Math.sqrt(vecA.reduce((acc, val) => acc + val * val, 0));
  const normB = Math.sqrt(vecB.reduce((acc, val) => acc + val * val, 0));
  return dotProduct / (normA * normB);
}

async function compareAnswers(
  generated: string | undefined,
  groundTruth: string | undefined,
): Promise<number> {
  generated = String(generated);
  groundTruth = String(groundTruth);
  const embeddings = new FinancialEmbedding();

  const [genEmbed, truthEmbed] = await Promise.all([
    embeddings.embedQuery(generated),
    embeddings.embedQuery(groundTruth),
  ]);

  return cosineSimilarity(genEmbed, truthEmbed);
}

async function getUserOrg(email: string) {
  return await db.userOrg.findFirstOrThrow(
    true
      ? {
          where: {
            user: {
              email: email,
            },
          },
        }
      : void 0,
  );
}

async function getFunds(orgId: string) {
  return await db.document.findFirst({
    where: {
      orgId: orgId,
    },
    select: {
      funds: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });
}

async function getTags(orgId: string) {
  return await db.document.findFirst({
    where: {
      orgId: orgId,
    },
    select: {
      tags: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });
}

async function getQuestions(
  orgId: string,
): Promise<(Question & { questionContents: QuestionContent[] })[]> {
  return await db.question.findMany({
    where: {
      orgId: orgId,
    },
    include: {
      questionContents: true,
    },
  });
}

function chunkQuestions(
  arr: (Question & { questionContents: QuestionContent[] })[],
  chunkSize: number,
) {
  return Array.from({ length: Math.ceil(arr.length / chunkSize) }, (_, i) =>
    arr.slice(i * chunkSize, i * chunkSize + chunkSize),
  );
}

async function main() {
  const program = new Command();

  program
    .name("ddq")
    .description("Process documents from URI")
    .option("-v, --verbose", "enable verbose output")
    .requiredOption("-i, --input-path <path>", "Path to README.xlsx file")
    .requiredOption("-o, --output-path <path>", "path to save ouput results")
    .requiredOption("-m, --metrics-path <path>", "path to save metrics")
    .requiredOption("--model <model>", "LLM model to use")
    .option("--temperature <number>", "Sampling temperature", parseFloat)
    .option("--top-p <number>", "Top-p sampling", parseFloat)
    .option(
      "-u, --user-email <address>",
      "user email to immitate, if not provided, will use the first user in the database",
    )
    .option(
      "--thinking-mode <boolean>",
      "Enable thinking mode",
      (val) => val === "true",
    )
    .option(
      "--agentic-mode-langgraph <boolean>",
      "Enable agentic mode",
      (val) => val === "true",
    )
    .option(
      "--citation-verification-mode <boolean>",
      "Enable citation verification",
      (val) => val === "true",
    )
    .option(
      "--answer-reflexion-mode <boolean>",
      "Enable answer reflexion",
      (val) => val === "true",
    )
    .option("--batch-size <number>", "Number of question to process")
    .option("--document-id <string>", "Document id to process")
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const documentId = options.documentId;
  const readmePath = options.inputPath;

  const { userId, orgId } = await getUserOrg(options.userEmail);
  const questions: (Question & { questionContents: QuestionContent[] })[] =
    await getQuestions(orgId);

  const funds = await getFunds(orgId);
  const tags = await getTags(orgId);
  const numberOfQuestionsToProcess = options.batchSize ?? 2;

  console.log(`Found ${questions.length} questions`);

  const batches = chunkQuestions(questions, numberOfQuestionsToProcess);

  const modelParameters: ModelParameters = {
    model: options.model,
    temperature: options.temperature,
    topP: options.topP,
    agentic_mode_langgraph: options.agenticModeLanggraph,
    thinking_mode: options.thinkingMode,
    citation_verification_mode: options.citationVerificationMode,
    answer_reflexion_mode: options.answerReflexionMode,
    responseStyle: ResponseStyle.NORMAL,
  };

  const results: any[] = [];
  let count = 0;

  const questionsAnswers = await processReadmeFile(readmePath);

  for (const batch of batches) {
    const batchPayload: QuestionInput[] = batch.map((q) => {
      const questionText =
        (q.questionContents[0]?.content as QuestionContentType).text ?? "";

      return {
        questionId: q.id,
        customPrompt: "",
        existingAnswer: "",
        questionType: q.type ?? QuestionType.FREE_TEXT,
        answerTemplate: q.answerTemplate ?? "",
        question: questionText,
      };
    });

    const responses = await generateResponseRAGFromDocumentQuestions({
      db,
      orgId,
      documentId,
      questions: batchPayload,
      tagIds: tags?.tags.map((t) => t.id) ?? [],
      fundIds: funds?.funds.map((f) => f.id) ?? [],
      modelParameters,
    });

    if (responses) {
      count++;
      for (const response of responses) {
        const question = batch.find((q) => q.id === response.questionId);

        const citationsYaml: string = yaml.dump(response.citations, {
          indent: 2,
          lineWidth: -1, // No line wrapping
          noRefs: true, // Don't output YAML references
        });

        const questionText =
          (question?.questionContents[0]?.content as QuestionContentType)
            .text ?? "";

        const questionAnswer = questionsAnswers.find((qa) => qa.No === count);

        const similarity = questionAnswer?.["Ground Truth Answer"]
          ? await compareAnswers(
              response.answer,
              questionAnswer["Ground Truth Answer"],
            )
          : 1 / 0;

        results.push({
          No: count,
          "Question Type": question?.type,
          Question: questionText,
          "Answer Template": question?.answerTemplate,
          Response: response.answer,
          Citations: citationsYaml,
          "Similarity (Cosine Cube)": similarity ** 3,
        });
      }
    }
  }

  restore();
  const csvData = await stringifyAsync(results, {
    header: true,
    columns: {
      No: "No",
      "Question Type": "Question Type",
      Question: "Question",
      "Answer Template": "Answer Template",
      Response: "Response",
      Citations: "Citations",
      "Similarity (Cosine Cube)": "Similarity (Cosine Cube)",
      "Ground Truth Answer": "Ground Truth Answer",
      "Ground Truth Citations": "Ground Truth Citations",
    },
    cast: {
      object: (value: unknown) => JSON.stringify(value),
    },
  });

  await fs.mkdir(path.dirname(options.outputPath), { recursive: true });

  const writeStream = createWriteStream(options.outputPath);
  writeStream.write(csvData);
  writeStream.end();

  // TODO: implement metrics for question-anwser document
  await Promise.all([
    new Promise((resolve, reject) => {
      writeStream.on("finish", () => resolve(undefined));
      writeStream.on("error", reject);
    }),

    fs.writeFile(
      options.metricsPath,
      JSON.stringify(
        {
          "Similarity (Cosine Cube)": results.reduce(
            (acc, response, index) => ({
              ...acc,
              [(index + 1).toString().padStart(3, "0")]:
                response["Similarity (Cosine Cube)"],
            }),
            {},
          ),
          summary: {
            mean:
              results.reduce(
                (acc, r) => acc + r["Similarity (Cosine Cube)"],
                0,
              ) / results.length,
            ...(() => {
              const sorted = [...results].sort(
                (a, b) =>
                  a["Similarity (Cosine Cube)"] - b["Similarity (Cosine Cube)"],
              );
              const midIndex = Math.floor(sorted.length * 0.5);
              const quarterIndex = Math.floor(sorted.length * 0.25);
              return {
                p50: sorted[midIndex]!["Similarity (Cosine Cube)"],
                p25: sorted[quarterIndex]!["Similarity (Cosine Cube)"],
              };
            })(),
          },
        },
        null,
        2,
      ),
    ),
  ]);
}

// Execute if run directly (not imported as module)
// if (require.main === module) {
main().catch(console.error);
// }

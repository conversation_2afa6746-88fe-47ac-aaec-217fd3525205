import { Command } from "commander";
import { db } from "~/server/db";

import { clusterAllQuestions, getBatchQuestions } from "../cluster-questions";

async function getUserOrg(email: string) {
  return await db.userOrg.findFirstOrThrow(
    true
      ? {
          where: {
            user: {
              email: email,
            },
          },
        }
      : void 0,
  );
}

async function main() {
  const program = new Command();

  program
    .name("questions-clustering")
    .description("Cluster questions")
    .option(
      "-u, --user-email <address>",
      "user email to immitate, if not provided, will use the first user in the database",
    )
    .option("--batch-size <number>", "Number of question to process")
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const { orgId } = await getUserOrg(options.userEmail);

  const batchQuestions = await getBatchQuestions({
    db: db,
    orgId: orgId,
  });

  try {
    const result = await clusterAllQuestions({
      db: db,
      orgId: orgId,
      questions: batchQuestions,
    });
    const map = new Map<string, number>();
    result.forEach((item) => {
      map.set(item.question_id, item.group);
    });

    const mergedresult = batchQuestions.map((item) => {
      return {
        question_id: item.question_id,
        question_text: item.question_text,
        group: map.get(item.question_id),
      };
    }).sort((a, b) => (a.group ?? -1) - (b.group ?? -1));

    mergedresult.forEach((item) => {
      if(item.group !== undefined && item.group > -1) {
        console.log(item.question_id, item.question_text, item.group);
      }
    });

    
    console.log(`Found ${result.length} questions`);
  } catch (err) {
    console.error("Failed to group questions:", err);
  }
}

// Execute if run directly (not imported as module)
// if (require.main === module) {
main().catch(console.error);
// }

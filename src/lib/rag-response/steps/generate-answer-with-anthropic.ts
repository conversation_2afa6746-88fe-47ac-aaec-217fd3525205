import { ChatModel, ModelParameters, ResponseStyle } from "~/lib/types";
import { newAnthropicLLM, newGeminiLLM, newLlm } from "./llm-constructor";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "node_modules/@langchain/core/dist/prompts/chat";
import { tracing } from "~/server/api/managers/tracingManager";
import { DocumentChunkType, UserInput } from "./types";
import { z } from "zod";
import { Anthropic } from "@anthropic-ai/sdk";
import { HumanMessage } from "@langchain/core/messages";
import { createChatPrompt } from "llm-exe";
import { jsonrepair } from "jsonrepair";
import {
  compressContext,
  prioritizeChunks,
  estimateTokenCount,
  getModelContextLimit,
} from "~/lib/utils/context-compression";
import { createContextCompressor } from "~/lib/utils/advanced-context-compression";

const outputSchema = z.object({
  input_type: z
    .enum(["query", "instructions", "casual", "unclear"])
    .describe("The type of user input"),
  output: z
    .string()
    .describe("The answer to the user question or instruction."),
});

type outputSchema = z.infer<typeof outputSchema>;

const responseStyleMap: Map<ResponseStyle, string> = new Map<
  ResponseStyle,
  string
>([
  [ResponseStyle.NORMAL, ""],
  [
    ResponseStyle.CONCISE,
    "Answer in a concise manner, without any additional commentary. Limit your response to a single paragraph.",
  ],
  [
    ResponseStyle.DETAILED,
    "Answer in a detailed manner, with all the information from the excerpts.",
  ],
  [
    ResponseStyle.OFFICIAL,
    "Answer in a formal manner, with all the information from the excerpts.",
  ],
]);

const chatPrompt = `
<general_information>
{{persona_prompt_string}}
Today's date is {{today}}
</general_information>

<task>
You are given a user input, a chat history, and a list of document excerpts.

Your first task is to classify the users' input.
If the users' input is part of a casual conversation, and does not contain a specific request for information or an instruction to perform a task, you must respond with a casual answer and nothing else. Do not show the classification in your response.
You MUST NOT indicate that you are performing any kind of classification. 

If the users' input references a previous message in the chat history, you must take it into account when answering the question.

If the users' input is a work related question or an instruction, your second task is to answer the user question based on the provided document excerpts. 
You must answer your query **only using the retrieved content**, and format your answer for use in investor reports.
</task>

<output_formatting_rules>
Use **Markdown** formatting wherever possible, including tables.  
Ensure that Markdown tables include a **newline between the header and each table row**.

Use **bold** formatting for **important words** and **phrases**.

Use **italics** for **emphasis**.

Use **underline** for **important links**.

Only use **bold** for headings.
Do not use any heading tags in your markdown output.
Do not use #, ##, ###, ####, #####, or ###### anywhere in your markdown output.

YOU ARE NOT ALLOWED TO USE ANY MARKDOWN HEADING TAGS IN YOUR OUTPUT.

Do not use dividers in your markdown output. A divider is a line that separates sections of your output.
</output_formatting_rules>

<content_rules>
If the content includes **names or personnel**, be **verbose** and include **full details**.

Avoid using phrases such as:
- “Based on the information provided”
- “Based on the document excerpts”
</content_rules>

---

When your query involves **time series or itemized lists**, follow these rules:

---

### 1. ✅ Known Exhaustive (List or Time Series)

If the source **explicitly defines a total count or time range** (e.g., “Arclight Capital manages 4 funds” or “Quarterly revenue from Q1 2022 to Q4 2023”), you must:
- Display **all expected entries**.
- Use **“Unnamed item”** or **“No data available”** where information is missing.
- **Do not fabricate names or attributes** not present in the source.
#### 📄 List Example:
> “Arclight Capital manages 4 funds.”  
> Only 2 are named: ArcGrowth and ArcIncome. AUM is only provided for ArcGrowth.

| Fund Name     | AUM               |
|---------------|-------------------|
| ArcGrowth     | $100M             |
| ArcIncome     | No data available |
| Unnamed fund  | No data available |
| Unnamed fund  | No data available |

_Note: Arclight Capital manages 4 funds. Only 2 are named in the retrieved content._

#### 🕒 Time Series Example:

> “Quarterly revenue from Q1 2022 to Q4 2023:”

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2022   | $12M           |
| Q2 2022   | No data available |
| Q3 2022   | $14M           |
| Q4 2022   | $13.5M         |
| Q1 2023   | $16M           |
| Q2 2023   | No data available |
| Q3 2023   | $17.2M         |
| Q4 2023   | $18M           |

---

### 2. ❌ Unknown Exhaustiveness

If the source **mentions individual items** (e.g., funds or time points) but does **not say how many exist in total**, treat the list as **ambiguously incomplete**.

- Show only retrieved items.
- Do not infer or fabricate missing names or totals.
- Use **“No data available”** only for attributes that were expected but missing.
- Clarify that coverage is **unknown**.

#### 📄 List Example:

> “Calder Equity is managed by Calder Investments. Calder Growth is managed by Calder Investments.”

| Fund Name       | AUM               |
|------------------|-------------------|
| Calder Equity    | No data available |
| Calder Growth    | No data available |

_Note: It is unclear whether Calder Investments manages additional funds or what their AUMs are._

#### 🕒 Time Series Example:

> “Revenue was reported for Q1 and Q3 2023.”

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2023   | $11M           |
| Q3 2023   | $13M           |

_Note: Coverage is unclear — other quarters may exist but are not shown._

---

### 3. ⚠️ Explicitly Non-Exhaustive

If the source language **implies the list is illustrative and not complete**, treat it as **intentionally partial**.

You may determine non-exhaustiveness if the source uses any of the following:
- “such as”
- “including”
- “among others”
- “for example”
- “e.g.”
- “select funds”
- “sample list”
- “not limited to”

You may also infer partiality based on context — even if such phrases are not present — when it is clear that the source is **providing examples, not a complete list**.

- Show only what is named.
- Clearly state that the list is **non-exhaustive**.

#### 📄 List Example:

> “Northwave Capital manages funds such as Northwave Balanced and Northwave ESG.”

| Fund Name         | AUM     |
|--------------------|---------|
| Northwave Balanced | $95M    |
| Northwave ESG      | $88M    |

_Note: This is a non-exhaustive list — other funds may exist._

#### 🕒 Time Series Example:

> “Performance highlights include Q2 and Q4 2023.”

| Quarter   | Revenue        |
|-----------|----------------|
| Q2 2023   | $14.1M         |
| Q4 2023   | $15.5M         |

_Note: Only select quarters are shown. Full range not provided._

{{response_style}}

<user_input>
Here is the user input:
{{input}}
</user_input>

<chat_history>
{{chat_history}}
</chat_history>

The document excerpts are provided as follow:
{{excerpts}}
`;

export async function* generateAnswerWithAnthropic({
  input,
  context,
  data,
  modelParameters,
  chatMessageId,
}: {
  context: { institutionName: string; sessionId?: string };
  data: DocumentChunkType[];
  modelParameters: ModelParameters;
  chatMessageId: string;
  input: UserInput;
}) {
  const DELIM = "__@__";
  const persona_prompt_string = `You are a financial analyst, and you work for ${context.institutionName}`;

  // Get model context limit
  const modelContextLimit = getModelContextLimit(modelParameters.model);
  console.log(
    `Model ${modelParameters.model} context limit: ${modelContextLimit} tokens`,
  );

  // Use advanced context compression
  const contextCompressor = createContextCompressor(modelParameters.model, {
    safetyMargin: 0.75, // Use 75% of context window for safety
    chunkOverlap: 200,
    minChunkSize: 100,
  });

  // Compress chunks using advanced strategies
  const compressionResult = await contextCompressor.compressContext(
    data,
    input.message,
    input.messageHistory.map((m) => JSON.stringify(m.content)).join("\n\n"),
    {
      strategy: "hybrid",
      maxChunks: Math.ceil((modelContextLimit * 0.6) / 1000), // Use 60% for chunks
      preserveRelevance: 0.7,
    },
  );

  console.log(
    `Advanced compression: ${compressionResult.compressionRatio.toFixed(2)}x (${compressionResult.totalTokens} tokens) using ${compressionResult.strategy} strategy`,
  );

  // Process compressed chunks
  const contentRecords = compressionResult.compressedChunks.map(
    (d) => JSON.parse(d.pageContent).content,
  );
  const processedRecords = contentRecords.map((c) => c.split(DELIM).join(": "));
  const cleanedRecords = processedRecords.map((c) => c.replace(/\n/g, " "));
  const finalData = cleanedRecords.join("\n\n");

  // Prepare context components
  const systemPrompt = chatPrompt;
  const userInput = input.message;
  const chatHistory = input.messageHistory
    .map((m) => JSON.stringify(m.content))
    .join("\n\n");
  const excerpts = finalData;

  // Final context compression to ensure we fit within limits
  const compressedContext = compressContext(
    systemPrompt,
    userInput,
    chatHistory,
    excerpts,
    modelParameters.model,
    {
      preserveSystemPrompt: true,
      preserveUserInput: true,
      preserveChatHistory: true,
      preserveExcerpts: true,
      compressionStrategy: "priority",
    },
  );

  console.log(
    `Final context compression: ${compressedContext.compressionRatio.toFixed(2)}x (${compressedContext.totalTokens} tokens)`,
  );

  const anthropic = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });

  const p = createChatPrompt<{
    input: string;
    excerpts: string;
    persona_prompt_string: string;
    response_style: string;
    chat_history: string;
    today: string;
  }>(compressedContext.systemPrompt);

  const prompt = p.format({
    input: compressedContext.userInput,
    excerpts: compressedContext.excerpts,
    persona_prompt_string: persona_prompt_string,
    response_style: responseStyleMap.get(modelParameters.responseStyle) ?? "",
    chat_history: compressedContext.chatHistory,
    today: new Date().toLocaleDateString(),
  });

  console.time("stream");
  const response = anthropic.beta.messages.stream({
    model: modelParameters.model,
    messages: [
      {
        role: "user",
        content: JSON.stringify(prompt[0]?.content),
      },
    ],
    max_tokens: 64000,

    temperature: modelParameters.temperature,
    tools: [
      {
        name: "structured_output",
        description:
          "Generate structured responses for DDQ questions using the provided schema.",
        input_schema: {
          type: "object",
          properties: {
            input_type: {
              type: "string",
              enum: ["query", "instructions", "casual", "unclear"],
            },
            output: { type: "string" },
          },
        },
      },
    ],
    tool_choice: {
      type: "tool",
      name: "structured_output",
    },
    betas: ["fine-grained-tool-streaming-2025-05-14", "context-1m-2025-08-07"],
  });

  let output = "";
  let previousOutput = "";
  for await (const chunk of response) {
    const data = JSON.parse(JSON.stringify(chunk) as string);
    if (data.type === "content_block_delta") {
      if (data.delta.partial_json.length > 0) {
        output += data.delta.partial_json;

        try {
          const parsed = JSON.parse(jsonrepair(output)) as outputSchema;
          const input_type = parsed.input_type;

          // Yield only the delta (new content since last iteration)
          if (parsed.output) {
            const delta = parsed.output.slice(previousOutput.length);
            if (delta.length > 0) {
              yield { delta, input_type };
              previousOutput = parsed.output;
            }
          }
        } catch (error) {
          console.error("error", error);
        }
      }
    }
  }
  console.timeEnd("stream");
}

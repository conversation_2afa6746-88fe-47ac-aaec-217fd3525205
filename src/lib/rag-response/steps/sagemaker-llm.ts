import { SageMakerRuntimeClient, InvokeEndpointCommand } from "@aws-sdk/client-sagemaker-runtime";
import { env } from "~/env";

export interface SageMakerLLMConfig {
  endpointName: string;
  region: string;
  temperature?: number;
  topP?: number;
  maxTokens?: number;
}

export class SageMakerLLM {
  private client: SageMakerRuntimeClient;
  private config: SageMakerLLMConfig;

  constructor(config?: Partial<SageMakerLLMConfig>, secret?: Record<string, string>) {
    this.config = {
      endpointName: config?.endpointName ?? secret?.SAGEMAKER_ENDPOINT_NAME ?? env.SAGEMAKER_ENDPOINT_NAME ?? "virgil-qwen3-endpoint",
      region: config?.region ?? secret?.SAGEMAKER_REGION ?? env.SAGEMAKER_REGION ?? "ap-southeast-1",
      temperature: config?.temperature ?? 0.7,
      topP: config?.topP ?? 0.9,
      maxTokens: config?.maxTokens ?? 2048,
    };

    this.client = new SageMakerRuntimeClient({
      region: this.config.region,
      credentials: {
        accessKeyId: secret?.AWS_S3_ACCESS_KEY ?? env.AWS_S3_ACCESS_KEY,
        secretAccessKey: secret?.AWS_S3_SECRET_ACCESS_KEY ?? env.AWS_S3_SECRET_ACCESS_KEY,
      },
    });
  }

  async generateCompletion(prompt: string, options?: {
    temperature?: number;
    topP?: number;
    maxTokens?: number;
  }): Promise<string> {
    const payload = {
      inputs: prompt,
      parameters: {
        temperature: options?.temperature ?? this.config.temperature,
        top_p: options?.topP ?? this.config.topP,
        max_new_tokens: options?.maxTokens ?? this.config.maxTokens,
        do_sample: true,
        stop: ["<|endoftext|>", "<|im_end|>"],
      },
    };

    try {
      const command = new InvokeEndpointCommand({
        EndpointName: this.config.endpointName,
        ContentType: "application/json",
        Body: JSON.stringify(payload),
      });

      const response = await this.client.send(command);
      
      if (!response.Body) {
        throw new Error("No response body from SageMaker endpoint");
      }

      const result = JSON.parse(new TextDecoder().decode(response.Body));
      
      let generatedText: string;
      if (Array.isArray(result) && result.length > 0) {
        generatedText = result[0]?.generated_text ?? "";
      } else if (typeof result === "object") {
        generatedText = result.generated_text ?? result.outputs ?? "";
      } else {
        generatedText = String(result);
      }

      // Remove the original prompt from the response if it's included
      if (generatedText.startsWith(prompt)) {
        generatedText = generatedText.slice(prompt.length).trim();
      }

      return generatedText;
    } catch (error) {
      console.error("SageMaker endpoint error:", error);
      throw new Error(`SageMaker inference failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async generateChatCompletion(messages: Array<{ role: string; content: string }>, options?: {
    temperature?: number;
    topP?: number;
    maxTokens?: number;
  }): Promise<string> {
    const prompt = this.messagesToPrompt(messages);
    return this.generateCompletion(prompt, options);
  }

  private messagesToPrompt(messages: Array<{ role: string; content: string }>): string {
    const promptParts: string[] = [];

    for (const message of messages) {
      const { role, content } = message;

      if (role === "system") {
        promptParts.push(`<|im_start|>system\n${content}<|im_end|>`);
      } else if (role === "user") {
        promptParts.push(`<|im_start|>user\n${content}<|im_end|>`);
      } else if (role === "assistant") {
        promptParts.push(`<|im_start|>assistant\n${content}<|im_end|>`);
      }
    }

    promptParts.push("<|im_start|>assistant\n");
    return promptParts.join("\n");
  }
}

export function newSageMakerLLM(secret?: Record<string, string>, config?: Partial<SageMakerLLMConfig>) {
  return new SageMakerLLM(config, secret);
}
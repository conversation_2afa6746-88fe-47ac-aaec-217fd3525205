import OpenAI from "openai";
import { EvaluateMessages } from "~/lib/types";
import { env } from "~/env";

export class RewardEvaluation {
  private apiKey?: string;
  private baseUrl: string;
  private model: string;
  private openai: OpenAI | undefined;

  private static instance: RewardEvaluation;

  public static getInstance(): RewardEvaluation {
    if (!RewardEvaluation.instance) {
      RewardEvaluation.instance = new RewardEvaluation();
    }
    return RewardEvaluation.instance;
  }

  constructor(secret?: Record<string, string>) {
    this.apiKey = env.NVIDIA_API_KEY;
    this.baseUrl = "https://integrate.api.nvidia.com/v1";
    this.model =
      secret?.NVIDIA_MODEL ??
      env.NVIDIA_MODEL ??
      "nvidia/llama-3.1-nemotron-70b-reward";

    if (!this.apiKey) {
      console.error(
        "NVIDIA API key is required. Set NVIDIA_API_KEY environment variable or pass NVIDIA_API_KEY in secret parameter.",
      );
      return;
    }

    this.openai = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
    });
  }


  sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  async evaluate(messageHistory: EvaluateMessages): Promise<number> {
    if (!this.openai) {
      return 0;
    }

    if (!messageHistory?.length) {
      return 0;
    }

    const messages: { role: "user" | "assistant"; content: string }[] = [];

    // manipulate messages with pair user & assistant
    for (let i = 0; i < messageHistory.length; i++) {
      if (
        i + 1 < messageHistory.length - 1 &&
        messageHistory[i]?.role === "user" &&
        messageHistory[i + 1]?.role === "assistant"
      ) {
        messages.push({
          role: "user",
          content: messageHistory[i]?.content ?? "",
        });
        messages.push({
          role: "assistant",
          content: messageHistory[i + 1]?.content ?? "",
        });
      }
    }
    const completion = await this.openai.chat.completions.create({
      model: this.model,
      messages,
    });

    const content = completion.choices?.[0]?.message?.content ?? "";
    const reward = Number(content) || Number(content.split("reward:")[1]) || 0;

    return this.sigmoid(reward);
  }
}

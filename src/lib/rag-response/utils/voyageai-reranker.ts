export interface RerankResult {
  relevance_score: number;
  index: number;
}

export interface RerankResponse {
  data: RerankResult[];
}

export interface RerankRequest {
  query: string;
  documents: string[];
  model?: string;
  top_k?: number;
}

export class VoyageAIReranker {
  private apiKey?: string;
  private baseUrl: string;

  constructor(secret?: Record<string, string>) {

    this.apiKey = secret?.VOYAGEAI_API_KEY ?? process.env.VOYAGEAI_API_KEY;
    this.baseUrl = "https://api.voyageai.com/v1";
    
    if (!this.apiKey) {
      throw new Error("VoyageAI API key is required. Set VOYAGEAI_API_KEY environment variable or pass VOYAGEAI_API_KEY in secret parameter.");
    }
  }

  async rerank(
    query: string, 
    documents: string[], 
    options: {
      model?: string;
      top_k?: number;
    } = {}
  ): Promise<RerankResponse> {
    const { model = "rerank-2", top_k = 5 } = options;

    if (documents.length === 0) {
      return { data: [] };
    }

    // Filter out empty documents
    const validDocuments = documents.filter(doc => doc && doc.trim().length > 0);
    
    if (validDocuments.length === 0) {
      return { data: [] };
    }

    const requestBody: RerankRequest = {
      query,
      documents: validDocuments,
      model,
      top_k: Math.min(top_k, validDocuments.length), // Don't request more than available
    };

    try {
      console.log(`VoyageAI rerank request: ${validDocuments.length} documents, top_k=${requestBody.top_k}`);
      
      const response = await fetch(`${this.baseUrl}/rerank`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`VoyageAI API error response: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`VoyageAI rerank API error: ${response.status} ${response.statusText}\n${errorText}`);
      }

      const responeJson = (await response.json()) as RerankResponse;
      console.log(`VoyageAI rerank response: ${responeJson?.data?.length || 0} results`);
      
      console.log("VoyageAI rerank results:", responeJson);
      
      // Validate the response structure
      if (!responeJson || !Array.isArray(responeJson.data)) {
        console.error("Invalid VoyageAI rerank response structure:", responeJson);
        throw new Error(`Invalid VoyageAI rerank response: expected results array, got ${typeof responeJson?.data}`);
      }
      
      return responeJson;
    } catch (error) {
      console.error("VoyageAI reranker error:", error);
      throw error;
    }
  }

  /**
   * Rerank a list of items using their text content
   * Returns the items in reranked order with relevance scores
   */
  async rerankItems<T>(
    query: string,
    items: T[],
    textExtractor: (item: T) => string,
    options: {
      model?: string;
      top_k?: number;
    } = {}
  ): Promise<Array<T & { relevance_score: number }>> {
    if (items.length === 0) {
      return [];
    }

    // Extract text from items
    const documents = items.map(textExtractor);
    
    // Get reranking results
    const rerankResult = await this.rerank(query, documents, options);
    
    // Validate rerank result structure
    if (!rerankResult || !Array.isArray(rerankResult.data)) {
      console.error("Invalid rerank result structure:", rerankResult);
      throw new Error(`Invalid rerank result: expected results array, got ${typeof rerankResult?.data}`);
    }
    
    // Map results back to original items with scores
    const rerankedItems = rerankResult.data.map(result => {
      const originalItem = items[result.index];
      if (!originalItem) {
        throw new Error(`Invalid index ${result.index} from rerank results`);
      }
      return {
        ...originalItem,
        relevance_score: result.relevance_score,
      } as T & { relevance_score: number };
    });

    return rerankedItems;
  }
}

// Singleton instance for easy use
let _instance: VoyageAIReranker | null = null;

export function getVoyageAIReranker(): VoyageAIReranker {
  if (!_instance) {
    _instance = new VoyageAIReranker();
  }
  return _instance;
}

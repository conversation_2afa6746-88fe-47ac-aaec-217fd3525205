import { AnswerGenerationType } from "@prisma/client";
import { JsonObject } from "@prisma/client/runtime/library";
import { generateAnswersByQuestionIds } from "~/lib/ddq";
import { INSUFFICIENT_DATA_ERROR, ModelParameters } from "~/lib/types";
import {
  markDocumentStatusGeneratingAnswers,
  markDocumentStatusReady,
} from "~/lib/vectorizer";
import { TracingContext } from "~/server/api/managers/tracingManager";
import {
  ExistingAnswerType,
  GetDocumentWithEmptyAnswersInclude,
  GetDocumentWithEmptyAnswersType,
  overwriteExistingResponseContent,
} from "~/server/api/routers/document";
import { PrismaClientType } from "~/server/db";
import { GeneratedResponse } from "..";

export const addExistingAnswersToResponseContent = async (
  db: PrismaClientType,
  documentId: string,
  existingAnswers: ExistingAnswerType[],
) => {
  for (const answer of existingAnswers) {
    console.log("Updating answer", JSON.stringify(answer, null, 2));

    try {
      // Find the response for answer.questionId
      const response = await db.response.findFirst({
        where: {
          questions: {
            some: { id: answer?.questionId ?? "" },
          },
        },
      });

      if (!response) {
        console.error(`No response found for question ${answer?.questionId}`);
        continue;
      }

      // First find the ResponseContent for this response
      const responseContent = await db.responseContent.findFirst({
        where: {
          responseId: response?.id ?? "",
          // Make sure we're updating the current document
          response: {
            documents: {
              some: { documentId: documentId },
            },
          },
        },
      });

      if (!responseContent) {
        console.error(`No ResponseContent found for response ${response?.id}`);
        continue;
      }

      const documentNameForResponseContent = await db.document.findFirst({
        where: {
          responses: {
            some: {
              response: {
                responseContents: {
                  some: {
                    id: responseContent.id,
                  },
                },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
        },
      });

      console.log(
        "documentNameForResponseContent",
        documentNameForResponseContent,
      );

      // Update the ResponseContent with the existing answers from the response library
      const updatedResponseContent = await db.responseContent.update({
        where: {
          id: responseContent.id,
          response: {
            documents: {
              some: {
                documentId: documentId,
              },
            },
          },
        },
        data: {
          content: {
            text: "",
            existingResponses: answer?.existingAnswers.map((a) => ({
              responseId: a.response_id ?? "",
              questionId: a.question_id ?? "",
              vector_distance: a.vector_distance,
              bm25_score: a.bm25_score,
            })),
            reason: answer?.reasoning,
          },
          answerGenerationType: AnswerGenerationType.EXTRACTED,
        },
      });

      console.log(
        "updatedResponseContent",
        JSON.stringify(updatedResponseContent, null, 2),
      );
    } catch (error) {
      console.error(
        `Failed to add existing answers to response (${answer?.questionId}) content: ${error}`,
      );
    }
  }
};

export async function* generateAnswersForDocument(input: {
  onlyEmptyAnswers: boolean;
  onlyErroredResponses: boolean;
  responseLibraryMatching: boolean;
  modelParameters: ModelParameters;
  db: PrismaClientType;
  orgId: string;
  doc: { id: string; name: string };
  tracingContext?: TracingContext;
  userId: string;
  secret?: Record<string, string>;
}) {
  console.time(`Generating answers for document ${input.doc.name}`);

  await markDocumentStatusGeneratingAnswers({
    db: input.db,
    documentId: input.doc.id,
    orgId: input.orgId,
  });

  const documentWithQuestionsAndEmptyAnswers: GetDocumentWithEmptyAnswersType =
    await input.db.document.findFirstOrThrow({
      where: {
        id: input.doc.id,
        orgId: input.orgId,
      },
      include: {
        responses: {
          where: {
            documentId: input.doc.id,
          },

          include: GetDocumentWithEmptyAnswersInclude,
        },
      },
    });

  console.log(
    "documentWithQuestionsAndEmptyAnswers",
    documentWithQuestionsAndEmptyAnswers.responses.map(
      (r) => (r.response.responseContents[0]?.content as JsonObject).text,
    ),
  );

  console.log("input.onlyEmptyAnswers", input.onlyEmptyAnswers);
  console.log("input.onlyErroredResponses", input.onlyErroredResponses);

  const questionIds = documentWithQuestionsAndEmptyAnswers.responses
    .filter((response) => {
      return input.onlyErroredResponses
        ? response.response.responseContents.some(
            (content) =>
              (content.content as JsonObject).text === INSUFFICIENT_DATA_ERROR,
          )
        : true;
    })
    .filter((response) => {
      return input.onlyEmptyAnswers
        ? (response.response.responseContents[0]?.content as JsonObject)
            .text === ""
        : true;
    })
    .map((response) =>
      response.response.questions.map((question) => question.id),
    )
    .flat();

  console.log(
    "questionIds",
    questionIds,
    questionIds.length,
    documentWithQuestionsAndEmptyAnswers.responses
      .filter((r) => questionIds.includes(r.response.questions[0]?.id ?? ""))
      .map((response) =>
        response.response.responseContents.map(
          (content) => (content.content as JsonObject).text,
        ),
      ),
  );

  // Mark all responses as EXTRACTED
  await input.db.responseContent.updateMany({
    where: {
      response: {
        questions: {
          some: {
            id: {
              in: questionIds,
            },
          },
        },
      },
    },
    data: {
      answerGenerationType: AnswerGenerationType.EXTRACTED,
    },
  });

  yield {
    status: "searching_responses",
    documentId: input.doc.id,
    totalResponses: questionIds.length,
    totalResponsesGenerated: 0,
    responses: [],
  };

  const BATCH_SIZE = 20;
  const responses = documentWithQuestionsAndEmptyAnswers.responses;
  const existingAnswers: ExistingAnswerType[] = [];

  const totalDocumentQuestions = questionIds.length;
  const totalExistingAnswers = existingAnswers.filter(
    (answer) => answer !== null,
  ).length;

  console.log(
    "totalDocumentQuestions",
    totalDocumentQuestions,
    "totalExistingAnswers",
    totalExistingAnswers,
  );

  const questionsToProcess = questionIds.filter((questionId) => {
    return input.responseLibraryMatching
      ? !existingAnswers.some((answer) => answer?.questionId === questionId)
      : true;
  });

  console.log("questionsToProcess", questionsToProcess);

  if (questionsToProcess.length === 0) {
    console.log("No questions to process. Marking document as ready");
    await markDocumentStatusReady({
      db: input.db,
      documentId: input.doc.id,
      orgId: input.orgId,
    });

    console.log("Document marked as ready", input.doc.name);

    return;
  }

  yield {
    status: "searching_responses_completed",
    documentId: input.doc.id,
    totalResponses: questionsToProcess.length,
    totalExistingAnswers: totalExistingAnswers,
    questionsToProcess: questionsToProcess.length,
  };

  if (input.responseLibraryMatching) {
    console.log("Adding existing answers to response content");
    await addExistingAnswersToResponseContent(
      input.db,
      input.doc.id,
      existingAnswers,
    );
  }

  const answersIterator = generateAnswersByQuestionIds({
    db: input.db,
    orgId: input.orgId,
    documentId: input.doc.id,
    questionIdsWithCustomPrompt: questionsToProcess.map((q) => ({
      questionId: q,
      customPrompt: "",
    })),
    userId: input.userId,
    tracingContext: input.tracingContext,
    modelParameters: input.modelParameters,
    secret: input.secret,
  });

  const answers: GeneratedResponse[] = [];
  for await (const {
    status,
    totalResponses,
    totalResponsesGenerated,
    responses,
  } of answersIterator) {
    console.log(
      "generateAnswers: Generated new answers",
      status,
      totalResponses,
      totalResponsesGenerated,
      responses?.length,
    );
    if (status === "error") {
      yield {
        status: "error",
        documentId: input.doc.id,
        totalResponses: totalResponses ?? 0,
      };
    }

    if (status === "generating_answers") {
      yield {
        status: "generating_answers",
        documentId: input.doc.id,
        totalResponses: totalResponses ?? 0,
        totalResponsesGenerated: totalResponsesGenerated ?? 0,
        responses: responses ?? [],
      };

      console.log(
        "List of documentWithQuestionsAndEmptyAnswers.responses questionIds",
        documentWithQuestionsAndEmptyAnswers.responses.map((r) =>
          r.response.questions.map((q) => q.id),
        ),
        documentWithQuestionsAndEmptyAnswers.responses.length,
      );

      console.log(
        "List of responses questionId",
        responses.map((r) => r?.questionId),
        responses.length,
      );

      console.log(
        "Matching responses to documentWithQuestionsAndEmptyAnswers.responses",
        documentWithQuestionsAndEmptyAnswers.responses.map((r) =>
          r.response.questions
            .map((q) => q.id)
            .some((qId) =>
              responses.map((r) => r?.questionId ?? "").includes(qId),
            ),
        ),
      );

      const answersAndResponses = responses.map((response) => ({
        answer: response?.answer ?? "",
        reason: response?.reason ?? "",
        citations: response?.citations ?? [],
        insufficientData: response?.insufficientData ?? false,
        response: documentWithQuestionsAndEmptyAnswers.responses.find((r) =>
          r.response.questions.find((q) => q.id === response?.questionId),
        ),
      }));

      if (answersAndResponses.length > 0) {
        // console.log(
        //   "Overwriting existing response contents",
        //   answersAndResponses,
        // );

        console.time(
          `Overwriting existing response contents for ${input.doc.name}. Total responses generated: ${totalResponsesGenerated}`,
        );
        const updatedResponseContents = await overwriteExistingResponseContent(
          input.db,
          answersAndResponses,
          input.userId,
          input.orgId,
        );

        // console.log("updatedResponseContents", updatedResponseContents);
        console.timeEnd(
          `Overwriting existing response contents for ${input.doc.name}. Total responses generated: ${totalResponsesGenerated}`,
        );
      }

      // Check to see if there are any unanswered questions left
      const extractedAnswers = await input.db.responseContent.count({
        where: {
          response: {
            documents: {
              some: {
                documentId: input.doc.id,
              },
            },
          },
          answerGenerationType: AnswerGenerationType.EXTRACTED,
        },
      });

      console.log("Responses left to generate:", extractedAnswers);
      // If there are no extracted answers, mark the document as ready

      if (extractedAnswers === 0) {
        console.log("Marking document as ready", input.doc.name);

        await markDocumentStatusReady({
          db: input.db,
          documentId: input.doc.id,
          orgId: input.orgId,
        });
      }
    }

    if (status === "completed") {
      const validResponses = responses.filter(
        (r): r is GeneratedResponse => r !== null,
      );
      yield {
        status: "completed",
        documentId: input.doc.id,
        totalResponses: totalResponses ?? 0,
        totalResponsesGenerated: totalResponsesGenerated ?? 0,
        responses: validResponses,
      };
      answers.push(...validResponses);
    }
  }

  console.timeEnd(`Generating answers for document ${input.doc.name}`);
}

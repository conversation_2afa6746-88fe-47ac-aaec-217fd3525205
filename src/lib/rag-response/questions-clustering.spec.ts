import { Command } from "commander";
import * as pgvector from "pgvector";
import { db } from "~/server/db";

import { stringify } from "csv-stringify";
import { promisify } from "util";
import { clusterQuestions } from "../cluster-questions";
const stringifyAsync: any = promisify(stringify);

async function getUserOrg(email: string) {
  return await db.userOrg.findFirstOrThrow(
    true
      ? {
          where: {
            user: {
              email: email,
            },
          },
        }
      : void 0,
  );
}

async function main() {
  const program = new Command();

  program
    .name("questions-clustering")
    .description("Cluster questions")
    .option(
      "-u, --user-email <address>",
      "user email to immitate, if not provided, will use the first user in the database",
    )
    .option("--batch-size <number>", "Number of question to process")
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const { orgId } = await getUserOrg(options.userEmail);

  const questions = await db.$queryRaw<{ id: string; vector: string }[]>`
    SELECT 
      q.id as id, 
      qc.vector::text as vector
    FROM "Question" q
    INNER JOIN "QuestionContent" qc ON qc."questionId" = q."id"
    WHERE q."orgId" = ${orgId} and qc.vector IS not NULL
    LIMIT 2
  `;

  const result = await clusterQuestions({
    db,
    orgId,
    questions: questions.map((question) => ({
      question_id: question.id,
      question_vector: pgvector.fromSql(question.vector),
    })),
  });
};

// Execute if run directly (not imported as module)
// if (require.main === module) {
main().catch(console.error);
// }

import { ChatPromptTemplate } from "@langchain/core/prompts";
import { Prisma } from "@prisma/client";
import { type JsonObject } from "@prisma/client/runtime/library";
import { z } from "zod";
import { type PrismaClientType } from "~/server/db";
import { tracing, TracingContext } from "../server/api/managers/tracingManager";
import { invokeLambda } from "./integrations/lambda/utils";
import { invokeRemotePyrpc } from "./integrations/pyrpc-remote/utils";
import {
  documentClassifierPrompt,
  documentFundClassifierPrompt,
  taggingPrompt,
} from "./prompts";
import { newGeminiLLM } from "./rag-response/steps/llm-constructor";

const BATCH_SIZE = 3;

export const structuredTaggedResponseOutputSchema = z.object({
  tagged_responses: z
    .object({
      question_id: z
        .string()
        .describe(`The ID of a question, as passed in the input.`),
      tag_id: z
        .string()
        .describe(
          `The ID of a tag that the question is tagged with, as passed in the input.`,
        ),
      tag_name_and_id: z
        .string()
        .describe(
          `The name and ID of the tag that the question is tagged with, as passed in the input.`,
        ),
      reason: z
        .string()
        .describe(
          `A short paragraph detailing the reason why this specific tag was assigned to this question, taking into account the question content and the tag summary.`,
        ),
    })
    .array()
    .describe("An array of question and tag pairs"),
});

type TaggedResponse = {
  question_id: string;
  tag_id: string;
  tag_name_and_id: string;
  reason: string;
};

export const structuredTaggedDocumentResponseOutputSchema = z
  .object({
    document_tags: z
      .object({
        tag_id: z
          .string()
          .describe(
            `The ID of a tag that the document is tagged with, as passed in the input.`,
          ),
        reason: z
          .string()
          .describe(
            `A short paragraph detailing the reason why this specific tag was assigned to this document, taking into account the document content and the tag summary.`,
          ),
      })
      .array(),
  })
  .describe("An array of tags and reasons");

export const structuredFundTaggedDocumentResponseOutputSchema = z
  .object({
    document_funds: z
      .object({
        fund_id: z
          .string()
          .describe(
            `The ID of a fund that the document is tagged with, as passed in the input.`,
          ),
        reason: z
          .string()
          .describe(
            `A short paragraph detailing the reason why this specific fund was assigned to this document, taking into account the document content and the fund description.`,
          ),
      })
      .array(),
  })
  .describe("An array of funds and reasons");

export async function questionClassifier(input: {
  db: PrismaClientType;
  documentId?: string;
  orgId: string;
  secret?: Record<string, string>;
  tracingContext?: TracingContext;
}) {
  const rootSpan = tracing.trace({
    name: "questionClassifier",
    tracingContext: input.tracingContext,
    input: {
      documentId: input.documentId,
    },
  });
  const llm = newGeminiLLM(input.secret);

  const llmWithTool = llm.withStructuredOutput(
    structuredTaggedResponseOutputSchema,
    {
      name: "tagged_responses",
    },
  );

  const taggingChain =
    ChatPromptTemplate.fromTemplate(taggingPrompt).pipe(llmWithTool);

  const questions = await input.db.question.findMany({
    where: {
      orgId: input.orgId,
      response: {
        documents: {
          some: {
            documentId: input.documentId,
          },
        },
      },
    },
    include: {
      questionContents: true,
    },
  });

  if (questions.length === 0) {
    console.log("No questions found for document", input.documentId);
    return true;
  }

  const tags = await input.db.tag.findMany({
    where: {
      orgId: input.orgId,
    },
  });

  const formattedTags = tags.map((t) => ({
    tag_id: t.id,
    tag_name: t.name,
    tag_summary: t.summary,
  }));

  const formattedQuestions = questions.map((q) => ({
    question_id: q.id,
    question_content: (q.questionContents[0]?.content as JsonObject).text,
  }));

  const numberOfQuestionsToProcess = 100;
  let questionIndex = 0;
  const taggedResponses: TaggedResponse[] = [];

  // divide the questions array into smaller arrays of numberOfQuestionsToProcess
  const questionChunks = formattedQuestions.reduce<JsonObject[][]>(
    (acc, curr, index) => {
      const chunkIndex = Math.floor(index / numberOfQuestionsToProcess);
      if (!acc[chunkIndex]) {
        acc[chunkIndex] = [];
      }
      acc[chunkIndex].push(curr);
      return acc;
    },
    [],
  );

  console.log("Number of question chunks", questionChunks.length);

  await Promise.all(
    questionChunks.map(async (questionChunk) => {
      try {
        const callback = tracing.getHandlerForTrace({ trace: rootSpan });
        const r = await taggingChain.invoke(
          {
            tags: formattedTags,
            questions: questionChunk,
          },
          { callbacks: callback ? [callback] : [] },
        );

        taggedResponses.push(...r.tagged_responses);
        questionIndex++;
      } catch (error) {
        console.error(
          `Error tagging questions ${questionIndex} - ${questionIndex + numberOfQuestionsToProcess}`,
          error,
        );
        throw error;
      }
    }),
  );

  rootSpan?.update({
    output: taggedResponses,
  });

  // console.log(
  //   taggedResponses.map((t) => ({
  //     question: questions.find((q) => q.id === t.question_id)
  //       ?.questionContents[0]?.content as JsonObject,
  //     tag: tags.find((tag) => tag.id === t.tag_id),
  //     tag_name_and_id: t.tag_name_and_id,
  //     equals: t.tag_name_and_id.includes(
  //       tags.find((tag) => tag.id === t.tag_id)?.name ?? "",
  //     ),
  //     reason: t.reason,
  //   })),
  //   taggedResponses.length,
  // );

  try {
    await input.db.$transaction(
      async (tx) => {
        for (const question of questions) {
          const taggedQuestion = taggedResponses.find(
            (t) => t.question_id === question.id,
          );

          if (!taggedQuestion?.tag_id) {
            console.log(
              `Question ${question.id} not found in tagged responses`,
            );
            continue;
          }

          await tx.question.update({
            where: { id: question.id },
            data: {
              tags: {
                connect: {
                  id: taggedQuestion.tag_id,
                },
              },
            },
          });
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
        timeout: 1000000,
      },
    );

    // The only @unique constraint is the id so we can't use upsert()
    // Need to delete all and recreate
    await input.db.tagEntityConnection.deleteMany({
      where: {
        questionId: {
          in: questions.map((q) => q.id),
        },
      },
    });

    await input.db.tagEntityConnection.createMany({
      data: taggedResponses
        .filter((tr) => questions.find((q) => q.id === tr.question_id))
        .map((t) => ({
          tagId: t.tag_id,
          questionId: t.question_id,
          orgId: input.orgId,
          connectionReason: t.reason,
          documentId: input.documentId,
        })),
    });

    return true;
  } catch (error) {
    console.error("Error updating questions and tags", error);
    return false;
  }
}

export async function documentCategoryClassifier(input: {
  db: PrismaClientType;
  documentId?: string;
  orgId: string;
  secret?: Record<string, string>;
  tracingContext?: TracingContext;
}) {
  console.time(`Category Classification ${input.documentId}`);

  const tracingContext = input.tracingContext ?? {
    sessionId: tracing.generateSessionId(),
    traceId: tracing.generateTracingId(),
  };
  const rootSpan = tracing.trace({
    name: "documentCategoryClassifier",
    input: {
      documentId: input.documentId,
    },
    tracingContext,
  });

  const nestedTracingContext: TracingContext = {
    ...tracingContext,
    parentSpanId: rootSpan?.id,
  };

  const doc = await input.db.document.findUniqueOrThrow({
    where: {
      id: input.documentId,
    },
    include: {
      chunks: true,
    },
  });

  const documentInput = {
    name: doc.name,
    id: input.documentId,
  };

  const dbCategories = await input.db.category.findMany({
    where: {
      orgId: input.orgId,
      parentId: {
        not: null,
      },
    },
  });

  const chunkContents = doc.chunks.map((c) => c.content);
  // const generateChunksPayload = {
  //   id: documentInput.id,
  // secret: input.secret,
  // };
  // if (process.env.NODE_ENV === "production") {
  //   const r = await invokeLambda(
  //     "getUnstructureFileContents",
  //     generateChunksPayload,
  //   );
  //   const [elements, partialResult] = r;
  //   chunkContents = doc.chunks.map(c => c.content);
  // } else {
  //   const unstructuredDocumentPath = `pyrpc/document/raw-chunk`;

  //   try {
  //     const rawDocumentResponse = await invokeRemotePyrpc(
  //       unstructuredDocumentPath,
  //       generateChunksPayload,
  //     );
  //     const [elements, partialResult] = await rawDocumentResponse.json();
  //     chunkContents = elements;
  //   } catch (err) {
  //     console.error(err);
  //     return false;
  //   }
  // }

  const groupCategories = dbCategories.reduce(
    (acc, category) => {
      if (!category.parentId) {
        return acc;
      }

      if (!acc) {
        acc = {};
      }

      const group = category.parentId;

      if (!acc[group]) {
        acc[group] = [];
      }

      acc[group].push({
        id: category.id,
        name: category.name,
        description: category.description,
      });

      return acc;
    },
    {} as Record<
      string,
      { id: string; name: string; description: string | null }[]
    >,
  );

  // Process sequentially in batches of BATCH_SIZE to avoid rate limiting
  const groupEntries = Object.entries(groupCategories);
  const results = [];

  for (let i = 0; i < groupEntries.length; i += BATCH_SIZE) {
    if (i >= 1) break;

    const batch = groupEntries.slice(i, i + BATCH_SIZE);

    // Run requests in parallel within each batch
    const batchPromises = batch.map(([group, categories], index) => {
      if (index >= 1) return;
      return documentCategoryClassifierByGroup({
        db: input.db,
        documentId: input.documentId,
        orgId: input.orgId,
        secret: input.secret,
        tracingContext: nestedTracingContext,
        documentInput: documentInput,
        categoryInputs: categories,
        chunkContents: chunkContents,
      });
    });

    try {
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  const classifiedCategories = results.reduce((acc, result) => {
    if (!result) {
      return acc;
    }

    if (!result.categories?.length) {
      return acc;
    }

    return [...acc, ...result.categories];
  }, [] as any[]);

  console.log(
    `Found ${classifiedCategories.length} categories for document ${input.documentId}`,
    classifiedCategories,
  );

  try {
    await input.db.document.update({
      where: {
        id: input.documentId,
      },
      data: {
        Categories: {
          set: [],
        },
      },
    });

    await input.db.categoryEntityConnection.deleteMany({
      where: {
        documentId: input.documentId,
      },
    });
  } catch (error) {
    console.error("Error deleting existing category entity connections", error);
    return false;
  }

  try {
    await input.db.document.update({
      where: { id: input.documentId },
      data: {
        Categories: {
          connect: classifiedCategories.map((c: { id: string }) => ({
            id: c.id,
          })),
        },
      },
    });

    await input.db.categoryEntityConnection.createMany({
      data: classifiedCategories.map((t: { id: string; reason: string }) => ({
        categoryId: t.id,
        documentId: input.documentId,
        reason: t.reason,
      })),
    });

    rootSpan?.update({
      output: {
        documentId: input.documentId,
        documentName: doc.name,
        categories: classifiedCategories,
      },
    });
  } catch (err) {
    console.error("Error updating document categories", err);
  }
  console.timeEnd(`Category Classification ${input.documentId}`);
  return classifiedCategories;
}

async function documentCategoryClassifierByGroup(input: {
  db: PrismaClientType;
  documentId?: string;
  orgId: string;
  secret?: Record<string, string>;
  tracingContext?: TracingContext;
  documentInput: {
    id: string | undefined;
    name: string;
  };
  categoryInputs: {
    id: string;
    name: string;
    description: string | null;
  }[];
  chunkContents: string[];
}) {
  let classifierResult;
  const classifierPayload = {
    document: {
      id: input.documentInput.id,
      name: input.documentInput.name,
      chunks: input.chunkContents,
    },
    categories: input.categoryInputs,
    secret: input.secret,
    tracingContext: input.tracingContext,
  };

  if (process.env.NODE_ENV === "production") {
    const r = await invokeLambda(
      "pyrpc",
      classifierPayload,
      "/pyrpc/generate-answer/classifier-document-category",
    );
    classifierResult = r;
  } else {
    const classifierPath = `pyrpc/generate-answer/classifier-document-category`;
    try {
      const classifierResponse = await invokeRemotePyrpc(
        classifierPath,
        classifierPayload,
      );
      classifierResult = await classifierResponse.json();
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  if (!classifierResult) {
    console.error("No classifier result");
    return false;
  }

  return classifierResult;
}

export async function documentClassifier(input: {
  db: PrismaClientType;
  documentId?: string;
  orgId: string;
  secret?: Record<string, string>;
  tracingContext?: TracingContext;
}) {
  const rootSpan = tracing.trace({
    name: "documentClassifier",
    tracingContext: input.tracingContext,
    input: {
      documentId: input.documentId,
    },
  });
  console.time(`Tagging document ${input.documentId}`);

  const llm = newGeminiLLM(input.secret);

  const llmWithTool = llm.withStructuredOutput(
    structuredTaggedDocumentResponseOutputSchema,
    {
      name: "tagged_document",
    },
  );

  const classifyingChain = ChatPromptTemplate.fromTemplate(
    documentClassifierPrompt,
  ).pipe(llmWithTool);

  const tags = await input.db.tag.findMany({
    where: {
      orgId: input.orgId,
    },
  });

  const formattedTags = tags.map((t) => ({
    tag_id: t.id,
    tag_name: t.name,
    tag_summary: t.summary,
  }));

  const doc = await input.db.document.findUniqueOrThrow({
    where: {
      id: input.documentId,
    },
    select: {
      chunks: true,
    },
  });

  const callback = tracing.getHandlerForTrace({ trace: rootSpan });
  const r = await classifyingChain.invoke(
    {
      tags: formattedTags,
      document: doc.chunks
        .map((d) => `${d.content} ${JSON.stringify(d.metadata)}`)
        .join("\n\n"),
    },
    { callbacks: callback ? [callback] : [] },
  );

  try {
    //Remove existing tags first
    try {
      await input.db.tagEntityConnection.deleteMany({
        where: {
          documentId: input.documentId,
        },
      });

      await input.db.document.update({
        where: { id: input.documentId },
        data: {
          tags: {
            set: [],
          },
        },
      });
    } catch (error) {
      console.error("Error deleting existing tag entity connections", error);
      return false;
    }

    try {
      if (r.document_tags.length > 0) {
        await input.db.document.update({
          where: { id: input.documentId },
          data: {
            tags: {
              connect: r.document_tags.map((t) => ({
                id: t.tag_id,
              })),
            },
          },
        });
      }
    } catch (error) {
      console.error("Error updating document tags", error);
    }

    rootSpan?.update({
      output: r,
    });

    // console.log(
    //   "Tagging document",
    //   input.documentId,
    //   r.document_tags,
    //   r.document_tags.length,
    // );

    try {
      await input.db.tagEntityConnection.createMany({
        data: r.document_tags.map((t) => ({
          tagId: t.tag_id,
          documentId: input.documentId,
          orgId: input.orgId,
          connectionReason: t.reason,
        })),
      });
    } catch (error) {
      console.error("Error creating tag entity connections", error);
    }

    console.timeEnd(`Tagging document ${input.documentId}`);

    return true;
  } catch (error) {
    console.error("Error updating document and tags", error);
    return false;
  }
}

export async function documentFundClassifier(input: {
  db: PrismaClientType;
  documentId?: string;
  orgId: string;
  secret?: Record<string, string>;
  tracingContext?: TracingContext;
}) {
  const rootSpan = tracing.trace({
    name: "documentFundClassifier",
    tracingContext: input.tracingContext,
    input: {
      documentId: input.documentId,
    },
  });
  console.time(`Tagging document funds ${input.documentId}`);

  const llm = newGeminiLLM(input.secret);

  const llmWithTool = llm.withStructuredOutput(
    structuredFundTaggedDocumentResponseOutputSchema,
    {
      name: "tagged_document",
    },
  );

  const classifyingChain = ChatPromptTemplate.fromTemplate(
    documentFundClassifierPrompt,
  ).pipe(llmWithTool);

  const funds = await input.db.fund.findMany({
    where: {
      orgId: input.orgId,
    },
  });

  if (funds.length === 0) {
    console.log("No funds found for organization", input.orgId);
    return true;
  }

  const formattedFunds = funds.map((f) => ({
    fund_id: f.id,
    fund_name: f.name,
    fund_description: f.description,
  }));

  const doc = await input.db.document.findUniqueOrThrow({
    where: {
      id: input.documentId,
    },
    select: {
      name: true,
      chunks: true,
    },
  });

  console.log("Number of chunks", doc.name, doc.chunks.length);

  if (doc.chunks.length === 0) {
    console.error(
      "No chunks found for document, classifying might be incomplete",
      input.documentId,
    );
  }

  const callback = tracing.getHandlerForTrace({ trace: rootSpan });
  const r = await classifyingChain.invoke(
    {
      funds: formattedFunds,
      documentName: doc.name,
      document: doc.chunks
        .map((d) => `${d.content} ${JSON.stringify(d.metadata)}`)
        .join("\n\n"),
    },
    { callbacks: callback ? [callback] : [] },
  );

  console.log("Classifier result", r);

  try {
    //Remove existing funds first
    try {
      await input.db.document.update({
        where: { id: input.documentId },
        data: {
          funds: {
            set: [],
          },
        },
      });
    } catch (error) {
      console.error("Error deleting existing fund connections", error);
      throw error;
    }

    try {
      if (r.document_funds.length > 0) {
        await input.db.document.update({
          where: { id: input.documentId },
          data: {
            funds: {
              connect: r.document_funds.map((f) => ({
                id: f.fund_id,
              })),
            },
          },
        });
      }
    } catch (error) {
      console.error("Error updating document funds", error);
      throw error;
    }

    // console.log(
    //   "Tagging document",
    //   input.documentId,
    //   r.document_funds,
    //   r.document_funds.length,
    // );

    console.timeEnd(`Tagging document ${input.documentId}`);

    return true;
  } catch (error) {
    console.error("Error updating document and tags", error);
    throw error;
  }
}

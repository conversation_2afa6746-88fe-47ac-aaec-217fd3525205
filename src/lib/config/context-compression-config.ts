import { ChatModel } from "../types";

export interface ContextCompressionConfig {
  // Token limits and safety margins
  defaultSafetyMargin: number;
  maxChunkRatio: number;
  reservedTokens: number;

  // Compression strategies
  defaultStrategy: "semantic" | "relevance" | "hybrid";
  enableSemanticDeduplication: boolean;
  enableChunkMerging: boolean;

  // Relevance thresholds
  minRelevanceScore: number;
  relevanceDecayFactor: number;

  // Chunk processing
  maxChunkSize: number;
  minChunkSize: number;
  chunkOverlap: number;

  // Model-specific overrides
  modelOverrides: Record<string, Partial<ContextCompressionConfig>>;
}

export const DEFAULT_CONTEXT_COMPRESSION_CONFIG: ContextCompressionConfig = {
  // Token limits and safety margins
  defaultSafetyMargin: 0.75, // Use 75% of context window
  maxChunkRatio: 0.6, // Use 60% of context for chunks
  reservedTokens: 10000, // Reserve tokens for system prompt, user input, etc.

  // Compression strategies
  defaultStrategy: "hybrid",
  enableSemanticDeduplication: true,
  enableChunkMerging: true,

  // Relevance thresholds
  minRelevanceScore: 0.1,
  relevanceDecayFactor: 0.1,

  // Chunk processing
  maxChunkSize: 1000,
  minChunkSize: 100,
  chunkOverlap: 200,

  // Model-specific overrides
  modelOverrides: {
    [ChatModel.CLAUDE_40_LATEST]: {
      defaultSafetyMargin: 0.9, // Claude can handle more context
      maxChunkRatio: 0.7,
    },
    [ChatModel.GPT_4O]: {
      defaultSafetyMargin: 0.7, // GPT-4o is more conservative
      maxChunkRatio: 0.5,
    },
    [ChatModel.GEMINI_25_PRO]: {
      defaultSafetyMargin: 0.9, // Gemini has very large context
      maxChunkRatio: 0.8,
    },
  },
};

/**
 * Get configuration for a specific model
 */
export function getModelConfig(model: string): ContextCompressionConfig {
  const baseConfig = { ...DEFAULT_CONTEXT_COMPRESSION_CONFIG };
  const modelOverride = baseConfig.modelOverrides[model];

  if (modelOverride) {
    return { ...baseConfig, ...modelOverride };
  }

  return baseConfig;
}

/**
 * Get compression settings for a specific use case
 */
export function getCompressionSettings(
  model: string,
  useCase: "chat" | "analysis" | "summarization" = "chat",
): Partial<ContextCompressionConfig> {
  const modelConfig = getModelConfig(model);

  switch (useCase) {
    case "chat":
      return {
        defaultStrategy: "hybrid",
        enableSemanticDeduplication: true,
        enableChunkMerging: true,
        maxChunkRatio: modelConfig.maxChunkRatio * 0.8, // More conservative for chat
      };

    case "analysis":
      return {
        defaultStrategy: "semantic",
        enableSemanticDeduplication: true,
        enableChunkMerging: false, // Keep chunks separate for analysis
        maxChunkRatio: modelConfig.maxChunkRatio * 0.9,
      };

    case "summarization":
      return {
        defaultStrategy: "relevance",
        enableSemanticDeduplication: false,
        enableChunkMerging: true,
        maxChunkRatio: modelConfig.maxChunkRatio * 0.7,
      };

    default:
      return modelConfig;
  }
}

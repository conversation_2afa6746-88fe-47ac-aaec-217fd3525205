import { PrismaClient } from "@prisma/client";
import * as pgvector from "pgvector";
import { invokeRemotePyrpc } from "./integrations/pyrpc-remote/utils";

// Types
interface QuestionVector {
  question_id: string;
  question_vector: number[];
  question_text?: string;
}

interface QuestionGroupUpdate {
  question_id: string;
  group: number;
}

interface QuestionWithDistance extends QuestionVector {
  question_distance?: number;
  group?: number | null;
  question_text?: string;
}

// Constants
const DEFAULT_BATCH_SIZE = 10;
const UNASSIGNED_GROUP = -1;
const SIMILARITY_THRESHOLD = 0.2;

export async function getBatchQuestions(options: {
  db: PrismaClient;
  orgId: string;
}): Promise<QuestionVector[]> {
  const { db, orgId } = options;

  try {
    const result = await db.$queryRaw<
      Array<{
        question_id: string;
        question_content_vector: string;
        question_text: string;
      }>
    >`
      SELECT 
        q.id AS question_id, 
        qc.content->'text' as question_text,
        qc.vector::text AS question_content_vector 
      FROM "Question" q
      JOIN "QuestionContent" qc ON qc."questionId" = q."id"
      WHERE qc.vector IS NOT NULL 
        AND q."orgId" = ${orgId}
      GROUP BY question_id, qc.vector, qc.content;
    `;

    return result.map((item) => ({
      question_id: item.question_id,
      question_text: item.question_text,
      question_vector: pgvector.fromSql(item.question_content_vector),
    }));
  } catch (error) {
    throw new Error(
      `Failed to fetch batch questions: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

function groupQuestionsByTargetGroup(
  questions: QuestionGroupUpdate[],
): Record<number, string[]> {
  return questions.reduce<Record<number, string[]>>(
    (acc, { question_id, group }) => {
      if (group === UNASSIGNED_GROUP) {
        return acc;
      }

      if (!acc[group]) {
        acc[group] = [];
      }

      acc[group]!.push(question_id);
      return acc;
    },
    {},
  );
}
async function resetQuestionGroups({
  db,
  orgId,
}: {
  db: PrismaClient;
  orgId: string;
}): Promise<void> {
  try {
    await db.question.updateMany({
      where: {
        orgId: orgId,
      },
      data: {
        group: null,
      },
    });
  } catch (error) {
    throw new Error(
      `Failed to reset question groups: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
export async function bulkUpdateQuestionGroups({
  db,
  questions,
}: {
  db: PrismaClient;
  questions: QuestionGroupUpdate[];
}): Promise<void> {
  if (!questions.length) {
    return;
  }

  try {
    const groupedUpdates = groupQuestionsByTargetGroup(questions);
    const updatePromises = Object.entries(groupedUpdates).map(
      ([group, questionIds]) =>
        db.question.updateMany({
          where: { id: { in: questionIds } },
          data: { group: parseInt(group, 10) },
        }),
    );

    await Promise.all(updatePromises);
  } catch (error) {
    throw new Error(
      `Failed to update question groups: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Finds similar questions in the database based on vector similarity
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @param question Question to find similar ones for
 * @param limit Maximum number of similar questions to return
 * @returns Promise resolving to an array of similar questions with their distances
 */
async function findSimilarQuestions(
  db: PrismaClient,
  orgId: string,
  question: QuestionVector,
  limit: number = DEFAULT_BATCH_SIZE,
): Promise<QuestionWithDistance[]> {
  try {
    return await db.$queryRaw<QuestionWithDistance[]>`
      SELECT 
        q.id as "question_id",
        q.group as group,
        qc.vector <=> ${pgvector.toSql(question.question_vector)}::vector as question_distance,
        qc.content->'text' as question_text
      FROM "Question" q
      JOIN "QuestionContent" qc ON qc."questionId" = q."id"
      WHERE q."orgId" = ${orgId}
        AND q.id != ${question.question_id}
      ORDER BY question_distance ASC
      LIMIT ${limit}
    `;
  } catch (error) {
    throw new Error(
      `Failed to find similar questions: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Gets the next available group number for clustering
 * @param db Prisma client instance
 * @param orgId Organization ID
 * @returns Promise resolving to the next available group number
 */
async function getNextGroupNumber(
  db: PrismaClient,
  orgId: string,
): Promise<number> {
  try {
    const {
      _max: { group: largestGroup },
    } = await db.question.aggregate({
      where: { orgId },
      _max: { group: true },
    });
    return (largestGroup ?? 0) + 1;
  } catch (error) {
    throw new Error(
      `Failed to get next group number: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

export async function clusterQuestions({
  db,
  orgId,
  questions,
}: {
  db: PrismaClient;
  orgId: string;
  questions: QuestionVector[];
}): Promise<QuestionGroupUpdate[]> {
  const updatedQuestions: QuestionGroupUpdate[] = [];

  for (const question of questions) {
    try {
      console.log(`Processing question ${question.question_id}`);
      const similarQuestions = await findSimilarQuestions(
        db,
        orgId,
        question,
        DEFAULT_BATCH_SIZE,
      );

      if (!similarQuestions.length || !similarQuestions[0]?.question_distance) {
        continue;
      }

      if (similarQuestions[0].question_distance > SIMILARITY_THRESHOLD) {
        console.log(
          `Question ${question.question_id} is not similar to any other question`,
          similarQuestions,
        );
        continue;
      }

      const mostSimilar = similarQuestions[0];
      let group = mostSimilar.group ?? null;

      if (group !== null) {
        // Assign to existing group
        console.log(
          `Assigning question ${question.question_id} to existing group ${group}`,
        );
        await db.question.update({
          where: { id: question.question_id },
          data: { group },
        });
      } else {
        // Create new group with the two most similar questions
        group = await getNextGroupNumber(db, orgId);
        console.log(
          `Creating new group ${group} for question ${question.question_id}`,
        );

        await db.question.updateMany({
          where: {
            id: { in: [question.question_id, mostSimilar.question_id] },
          },
          data: { group },
        });
      }

      updatedQuestions.push({
        question_id: question.question_id,
        group,
      });
    } catch (error) {
      console.error(
        `Error processing question ${question.question_id}:`,
        error,
      );
      throw new Error(
        `Failed to cluster question ${question.question_id}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return updatedQuestions;
}

export async function clusterAllQuestions({
  db,
  orgId,
  questions,
}: {
  db: PrismaClient;
  orgId: string;
  questions: QuestionVector[];
}): Promise<QuestionGroupUpdate[]> {
  if (!questions.length) {
    return [];
  }
  console.time("Calling Pyrpc API for clustering questions");
  const endpoint = "pyrpc/generate-answer/cluster-similar-question";
  try {
    const response = await invokeRemotePyrpc(endpoint, { questions });

    if (!response.ok) {
      const errorText = await response.text().catch(() => response.statusText);
      throw new Error(
        `Clustering API returned ${response.status}: ${errorText}, URL: ${endpoint}`,
      );
    }

    const groupResponse: QuestionGroupUpdate[] = await response.json();
    console.timeEnd("Calling Pyrpc API for clustering questions");

    // Ensure all required fields are present
    const isValidResponse = groupResponse.every(
      (item) =>
        typeof item === "object" &&
        item !== null &&
        "question_id" in item &&
        "group" in item,
    );

    if (!isValidResponse) {
      throw new Error("Invalid response format from clustering service");
    }

    console.time("Resetting clustering questions groups");
    await resetQuestionGroups({ db, orgId });
    console.timeEnd("Resetting clustering questions groups");

    console.time("Bulk update question groups");
    await bulkUpdateQuestionGroups({ db, questions: groupResponse });
    console.timeEnd("Bulk update question groups");

    return groupResponse;
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Unknown error during clustering";
    console.error("ClusterAllQuestions error:", errorMessage, error);

    throw error;
  }
}

import { toast } from "~/components/snackbar";

export async function copyToClipboard(text: string) {
  if (!navigator?.clipboard) {
    console.warn("Clipboard not supported");
    return false;
  }

  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.warn("Copy failed", error);
    return false;
  }
}

export function copyToClipboardClickHandler(
  text: string,
  duration: number = 500,
  enabled: boolean = true,
  successMessage?: string,
) {
  return async (e: React.MouseEvent<HTMLElement>) => {
    if (!enabled) return;
    const message = successMessage ?? "Copied to clipboard";
    e.preventDefault();
    e.stopPropagation();
    if (await copyToClipboard(text)) {
      toast.success(message, {
        duration,
      });
    }
  };
}

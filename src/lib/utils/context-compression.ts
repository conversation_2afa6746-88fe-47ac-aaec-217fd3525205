import { DocumentChunkType } from "~/lib/rag-response/steps/types";
import { ChatModel } from "../types";

// Conservative estimate: 1 token ≈ 4 characters
const TOKEN_RATIO = 4;

// Context window limits for different models (in tokens)
const MODEL_CONTEXT_LIMITS: Record<string, number> = {
  [ChatModel.CLAUDE_40_LATEST]: 1000000,
  [ChatModel.CLAUDE_37_LATEST]: 200000,
  [ChatModel.GPT_4O]: 128000,
  [ChatModel.GPT_4O_MINI]: 128000,
  [ChatModel.GEMINI_25_PRO]: 1000000,
  [ChatModel.GEMINI_25_FLASH]: 1000000,
};

// Reserve tokens for system prompt, user input, and response
const RESERVED_TOKENS = 20000;

export interface ContextCompressionOptions {
  maxTokens?: number;
  preserveSystemPrompt?: boolean;
  preserveUserInput?: boolean;
  preserveChatHistory?: boolean;
  preserveExcerpts?: boolean;
  compressionStrategy?: "truncate" | "summarize" | "priority";
}

export interface CompressedContext {
  systemPrompt: string;
  userInput: string;
  chatHistory: string;
  excerpts: string;
  totalTokens: number;
  compressionRatio: number;
}

/**
 * Estimate token count for text (conservative estimate)
 */
export function estimateTokenCount(text: string): number {
  return Math.ceil(text.length / TOKEN_RATIO);
}

/**
 * Get context window limit for a specific model
 */
export function getModelContextLimit(model: string): number {
  return MODEL_CONTEXT_LIMITS[model] || 100000; // Default fallback
}

/**
 * Compress context to fit within token limits
 */
export function compressContext(
  systemPrompt: string,
  userInput: string,
  chatHistory: string,
  excerpts: string,
  model: string,
  options: ContextCompressionOptions = {},
): CompressedContext {
  const maxTokens = options.maxTokens || getModelContextLimit(model);
  const availableTokens = maxTokens - RESERVED_TOKENS;

  const initialContext = {
    systemPrompt,
    userInput,
    chatHistory,
    excerpts,
  };

  const initialTokens = Object.values(initialContext).reduce(
    (sum, text) => sum + estimateTokenCount(text),
    0,
  );

  if (initialTokens <= availableTokens) {
    return {
      ...initialContext,
      totalTokens: initialTokens,
      compressionRatio: 1.0,
    };
  }

  // Start with essential components
  const compressed = {
    systemPrompt: options.preserveSystemPrompt !== false ? systemPrompt : "",
    userInput: options.preserveUserInput !== false ? userInput : "",
    chatHistory: "",
    excerpts: "",
  };

  let currentTokens =
    estimateTokenCount(compressed.systemPrompt) +
    estimateTokenCount(compressed.userInput);

  // Add chat history if space allows
  if (
    options.preserveChatHistory !== false &&
    currentTokens < availableTokens
  ) {
    const maxChatHistoryTokens = availableTokens - currentTokens;
    compressed.chatHistory = truncateText(chatHistory, maxChatHistoryTokens);
    currentTokens += estimateTokenCount(compressed.chatHistory);
  }

  // Add excerpts with remaining space
  if (options.preserveExcerpts !== false && currentTokens < availableTokens) {
    const maxExcerptTokens = availableTokens - currentTokens;
    compressed.excerpts = compressExcerpts(excerpts, maxExcerptTokens);
    currentTokens += estimateTokenCount(compressed.excerpts);
  }

  return {
    ...compressed,
    totalTokens: currentTokens,
    compressionRatio: currentTokens / initialTokens,
  };
}

/**
 * Truncate text to fit within token limit
 */
function truncateText(text: string, maxTokens: number): string {
  const maxChars = maxTokens * TOKEN_RATIO;
  if (text.length <= maxChars) return text;

  // Try to truncate at sentence boundaries
  const sentences = text.split(/[.!?]+/).filter((s) => s.trim());
  let truncated = "";
  let currentTokens = 0;

  for (const sentence of sentences) {
    const sentenceTokens = estimateTokenCount(sentence + ".");
    if (currentTokens + sentenceTokens <= maxTokens) {
      truncated += sentence + ".";
      currentTokens += sentenceTokens;
    } else {
      break;
    }
  }

  if (truncated.length === 0) {
    // Fallback to character-based truncation
    return text.substring(0, maxChars - 3) + "...";
  }

  return truncated;
}

/**
 * Compress excerpts using priority-based selection
 */
function compressExcerpts(excerpts: string, maxTokens: number): string {
  const maxChars = maxTokens * TOKEN_RATIO;
  if (excerpts.length <= maxChars) return excerpts;

  // Split into individual excerpts
  const excerptList = excerpts.split("\n\n").filter((e) => e.trim());

  // Simple priority: keep first excerpts (assuming they're most relevant)
  let compressed = "";
  let currentTokens = 0;

  for (const excerpt of excerptList) {
    const excerptTokens = estimateTokenCount(excerpt + "\n\n");
    if (currentTokens + excerptTokens <= maxTokens) {
      compressed += excerpt + "\n\n";
      currentTokens += excerptTokens;
    } else {
      break;
    }
  }

  if (compressed.length === 0) {
    // Fallback: take first excerpt and truncate
    const firstExcerpt = excerptList[0] ?? "";
    return truncateText(firstExcerpt, maxTokens);
  }

  return compressed.trim();
}

/**
 * Smart context compression with LLM-based summarization
 */
export async function smartCompressContext(
  systemPrompt: string,
  userInput: string,
  chatHistory: string,
  excerpts: string,
  model: string,
  options: ContextCompressionOptions = {},
): Promise<CompressedContext> {
  const maxTokens = options.maxTokens || getModelContextLimit(model);
  const availableTokens = maxTokens - RESERVED_TOKENS;

  const initialTokens =
    estimateTokenCount(systemPrompt) +
    estimateTokenCount(userInput) +
    estimateTokenCount(chatHistory) +
    estimateTokenCount(excerpts);

  if (initialTokens <= availableTokens) {
    return {
      systemPrompt,
      userInput,
      chatHistory,
      excerpts,
      totalTokens: initialTokens,
      compressionRatio: 1.0,
    };
  }

  // Use basic compression for now
  // TODO: Implement LLM-based summarization for more intelligent compression
  return compressContext(
    systemPrompt,
    userInput,
    chatHistory,
    excerpts,
    model,
    options,
  );
}

/**
 * Prioritize document chunks based on relevance scores
 */
export function prioritizeChunks(
  chunks: DocumentChunkType[],
  maxTokens: number,
): DocumentChunkType[] {
  // Sort by relevance (assuming _distance is lower = more relevant)
  const sortedChunks = [...chunks].sort(
    (a, b) => (a.metadata._distance || 0) - (b.metadata._distance || 0),
  );

  let totalTokens = 0;
  const selectedChunks: DocumentChunkType[] = [];

  for (const chunk of sortedChunks) {
    const chunkTokens = estimateTokenCount(chunk.pageContent);
    if (totalTokens + chunkTokens <= maxTokens) {
      selectedChunks.push(chunk);
      totalTokens += chunkTokens;
    } else {
      break;
    }
  }

  return selectedChunks;
}

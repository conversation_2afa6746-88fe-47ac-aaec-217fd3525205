import { DocumentChunkType } from "~/lib/rag-response/steps/types";
import {
  estimateTokenCount,
  getModelContextLimit,
} from "./context-compression";

export interface SummarizationOptions {
  maxSummaryLength?: number;
  preserveKeyEntities?: boolean;
  preserveNumbers?: boolean;
  preserveDates?: boolean;
}

export interface ChunkSummary {
  originalChunk: DocumentChunkType;
  summary: string;
  keyEntities: string[];
  relevance: number;
  tokenCount: number;
}

/**
 * Advanced context compression using semantic chunking and summarization
 */
export class AdvancedContextCompressor {
  private maxTokens: number;
  private chunkOverlap: number;
  private minChunkSize: number;

  constructor(
    maxTokens: number,
    chunkOverlap: number = 100,
    minChunkSize: number = 50,
  ) {
    this.maxTokens = maxTokens;
    this.chunkOverlap = chunkOverlap;
    this.minChunkSize = minChunkSize;
  }

  /**
   * Compress context using multiple strategies
   */
  async compressContext(
    chunks: DocumentChunkType[],
    userQuery: string,
    chatHistory: string,
    options: {
      strategy?: "semantic" | "relevance" | "hybrid";
      maxChunks?: number;
      preserveRelevance?: number;
    } = {},
  ): Promise<{
    compressedChunks: DocumentChunkType[];
    totalTokens: number;
    compressionRatio: number;
    strategy: string;
  }> {
    const strategy = options.strategy || "hybrid";
    const maxChunks = options.maxChunks || Math.ceil(this.maxTokens / 1000); // Rough estimate

    let compressedChunks: DocumentChunkType[];
    let totalTokens: number;
    let compressionRatio: number;

    switch (strategy) {
      case "semantic":
        ({
          chunks: compressedChunks,
          totalTokens,
          compressionRatio,
        } = await this.semanticCompression(chunks, userQuery, maxChunks));
        break;

      case "relevance":
        ({
          chunks: compressedChunks,
          totalTokens,
          compressionRatio,
        } = this.relevanceCompression(chunks, userQuery, maxChunks));
        break;

      case "hybrid":
      default:
        ({
          chunks: compressedChunks,
          totalTokens,
          compressionRatio,
        } = await this.hybridCompression(
          chunks,
          userQuery,
          chatHistory,
          maxChunks,
        ));
        break;
    }

    return {
      compressedChunks,
      totalTokens,
      compressionRatio,
      strategy,
    };
  }

  /**
   * Semantic compression using chunk similarity and deduplication
   */
  private async semanticCompression(
    chunks: DocumentChunkType[],
    userQuery: string,
    maxChunks: number,
  ): Promise<{
    chunks: DocumentChunkType[];
    totalTokens: number;
    compressionRatio: number;
  }> {
    // Sort by relevance to query
    const scoredChunks = chunks
      .map((chunk) => ({
        chunk,
        relevance: this.calculateRelevance(chunk, userQuery),
        tokens: estimateTokenCount(chunk.pageContent),
      }))
      .sort((a, b) => b.relevance - a.relevance);

    // Remove duplicate content using simple similarity
    const uniqueChunks: DocumentChunkType[] = [];
    const seenContent = new Set<string>();

    for (const { chunk } of scoredChunks) {
      if (uniqueChunks.length >= maxChunks) break;

      const contentHash = this.getContentHash(chunk.pageContent);
      if (!seenContent.has(contentHash)) {
        uniqueChunks.push(chunk);
        seenContent.add(contentHash);
      }
    }

    const totalTokens = uniqueChunks.reduce(
      (sum, chunk) => sum + estimateTokenCount(chunk.pageContent),
      0,
    );

    return {
      chunks: uniqueChunks,
      totalTokens,
      compressionRatio:
        totalTokens /
        chunks.reduce(
          (sum, chunk) => sum + estimateTokenCount(chunk.pageContent),
          0,
        ),
    };
  }

  /**
   * Relevance-based compression
   */
  private relevanceCompression(
    chunks: DocumentChunkType[],
    userQuery: string,
    maxChunks: number,
  ): {
    chunks: DocumentChunkType[];
    totalTokens: number;
    compressionRatio: number;
  } {
    const scoredChunks = chunks
      .map((chunk) => ({
        chunk,
        relevance: this.calculateRelevance(chunk, userQuery),
        tokens: estimateTokenCount(chunk.pageContent),
      }))
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, maxChunks);

    const totalTokens = scoredChunks.reduce(
      (sum, { tokens }) => sum + tokens,
      0,
    );
    const originalTokens = chunks.reduce(
      (sum, chunk) => sum + estimateTokenCount(chunk.pageContent),
      0,
    );

    return {
      chunks: scoredChunks.map(({ chunk }) => chunk),
      totalTokens,
      compressionRatio: totalTokens / originalTokens,
    };
  }

  /**
   * Hybrid compression combining multiple strategies
   */
  private async hybridCompression(
    chunks: DocumentChunkType[],
    userQuery: string,
    chatHistory: string,
    maxChunks: number,
  ): Promise<{
    chunks: DocumentChunkType[];
    totalTokens: number;
    compressionRatio: number;
  }> {
    // First, use relevance-based selection
    const relevantChunks = this.relevanceCompression(
      chunks,
      userQuery,
      maxChunks * 2,
    );

    // Then apply semantic deduplication
    const semanticResult = await this.semanticCompression(
      relevantChunks.chunks,
      userQuery,
      maxChunks,
    );

    return semanticResult;
  }

  /**
   * Calculate relevance score between chunk and query
   */
  private calculateRelevance(chunk: DocumentChunkType, query: string): number {
    // Simple keyword-based relevance (can be enhanced with embeddings)
    const queryWords = query.toLowerCase().split(/\s+/);
    const chunkWords = chunk.pageContent.toLowerCase().split(/\s+/);

    let matches = 0;
    for (const queryWord of queryWords) {
      if (chunkWords.some((chunkWord) => chunkWord.includes(queryWord))) {
        matches++;
      }
    }

    // Normalize by query length and add distance penalty
    const relevance = matches / queryWords.length;
    const distancePenalty = (chunk.metadata._distance || 0) * 0.1;

    return Math.max(0, relevance - distancePenalty);
  }

  /**
   * Generate content hash for deduplication
   */
  private getContentHash(content: string): string {
    // Simple hash for deduplication
    const normalized = content.toLowerCase().replace(/\s+/g, " ").trim();
    return normalized.substring(0, 100); // Use first 100 chars as hash
  }

  /**
   * Smart chunk merging for better context preservation
   */
  mergeRelatedChunks(chunks: DocumentChunkType[]): DocumentChunkType[] {
    if (chunks.length <= 1) return chunks;

    const merged: DocumentChunkType[] = [];
    let currentChunk = chunks[0];

    for (let i = 1; i < chunks.length; i++) {
      const nextChunk = chunks[i];
      const combinedTokens =
        estimateTokenCount(currentChunk?.pageContent ?? "") +
        estimateTokenCount(nextChunk?.pageContent ?? "");

      // Merge if combined size is reasonable and chunks are related
      if (
        combinedTokens <= this.maxTokens * 0.1 &&
        this.areChunksRelated(
          currentChunk ?? {
            pageContent: "",
            metadata: {
              id: "",
              documentId: "",
              fileName: "",
              _distance: 0,
              languages: [],
              filetype: "",
            },
          },
          nextChunk ?? {
            pageContent: "",
            metadata: {
              id: "",
              documentId: "",
              fileName: "",
              _distance: 0,
              languages: [],
              filetype: "",
            },
          },
        )
      ) {
        currentChunk = {
          ...currentChunk,
          pageContent:
            (currentChunk?.pageContent ?? "") +
            "\n\n" +
            (nextChunk?.pageContent ?? ""),
          metadata: {
            ...(currentChunk?.metadata ?? {
              id: "",
              documentId: "",
              fileName: "",
              _distance: 0,
              languages: [],
              filetype: "",
            }),
            _distance: Math.min(
              (currentChunk?.metadata?._distance ?? 0) || 0,
              (nextChunk?.metadata?._distance ?? 0) || 0,
            ),
          },
        };
      } else {
        merged.push(
          currentChunk ?? {
            pageContent: "",
            metadata: {
              id: "",
              documentId: "",
              fileName: "",
              _distance: 0,
              languages: [],
              filetype: "",
            },
          },
        );
        currentChunk = nextChunk ?? {
          pageContent: "",
          metadata: {
            id: "",
            documentId: "",
            fileName: "",
            _distance: 0,
            languages: [],
            filetype: "",
          },
        };
      }
    }

    merged.push(
      currentChunk ?? {
        pageContent: "",
        metadata: {
          id: "",
          documentId: "",
          fileName: "",
          _distance: 0,
          languages: [],
          filetype: "",
        },
      },
    );
    return merged as DocumentChunkType[];
  }

  /**
   * Check if two chunks are semantically related
   */
  private areChunksRelated(
    chunk1: DocumentChunkType,
    chunk2: DocumentChunkType,
  ): boolean {
    // Simple heuristic: check if they share common entities or topics
    const words1 = chunk1.pageContent.toLowerCase().split(/\s+/);
    const words2 = chunk2.pageContent.toLowerCase().split(/\s+/);

    const commonWords = words1.filter(
      (word) => word.length > 3 && words2.includes(word),
    );

    return commonWords.length >= 2; // At least 2 common words
  }
}

/**
 * Factory function for creating context compressors
 */
export function createContextCompressor(
  model: string,
  options: {
    chunkOverlap?: number;
    minChunkSize?: number;
    safetyMargin?: number;
  } = {},
): AdvancedContextCompressor {
  const modelLimit = getModelContextLimit(model);
  const safetyMargin = options.safetyMargin || 0.8; // Use 80% of context window

  return new AdvancedContextCompressor(
    Math.floor(modelLimit * safetyMargin),
    options.chunkOverlap || 100,
    options.minChunkSize || 50,
  );
}

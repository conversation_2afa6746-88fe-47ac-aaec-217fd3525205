export interface ContextCompressionMetrics {
  timestamp: Date;
  model: string;
  originalTokens: number;
  compressedTokens: number;
  compressionRatio: number;
  strategy: string;
  processingTime: number;
  success: boolean;
  error?: string;
}

export interface CompressionPerformanceStats {
  totalRequests: number;
  averageCompressionRatio: number;
  averageProcessingTime: number;
  successRate: number;
  modelBreakdown: Record<
    string,
    {
      requests: number;
      avgCompressionRatio: number;
      avgProcessingTime: number;
    }
  >;
}

/**
 * Monitor and track context compression performance
 */
export class ContextCompressionMonitor {
  private metrics: ContextCompressionMetrics[] = [];
  private maxMetrics: number = 1000; // Keep last 1000 metrics

  /**
   * Record a compression operation
   */
  recordCompression(
    metrics: Omit<ContextCompressionMetrics, "timestamp">,
  ): void {
    const metric: ContextCompressionMetrics = {
      ...metrics,
      timestamp: new Date(),
    };

    this.metrics.push(metric);

    // Keep only the last maxMetrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log performance metrics
    this.logMetrics(metric);
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): CompressionPerformanceStats {
    if (this.metrics.length === 0) {
      return {
        totalRequests: 0,
        averageCompressionRatio: 0,
        averageProcessingTime: 0,
        successRate: 0,
        modelBreakdown: {},
      };
    }

    const successfulMetrics = this.metrics.filter((m) => m.success);
    const totalRequests = this.metrics.length;
    const successRate = successfulMetrics.length / totalRequests;

    const averageCompressionRatio =
      successfulMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) /
      successfulMetrics.length;

    const averageProcessingTime =
      successfulMetrics.reduce((sum, m) => sum + m.processingTime, 0) /
      successfulMetrics.length;

    // Model breakdown
    const modelBreakdown: Record<string, any> = {};
    const modelGroups = this.groupBy(this.metrics, "model");

    for (const [model, modelMetrics] of Object.entries(modelGroups)) {
      const successfulModelMetrics = modelMetrics.filter((m) => m.success);

      if (successfulModelMetrics.length > 0) {
        modelBreakdown[model] = {
          requests: modelMetrics.length,
          avgCompressionRatio:
            successfulModelMetrics.reduce(
              (sum, m) => sum + m.compressionRatio,
              0,
            ) / successfulModelMetrics.length,
          avgProcessingTime:
            successfulModelMetrics.reduce(
              (sum, m) => sum + m.processingTime,
              0,
            ) / successfulModelMetrics.length,
        };
      }
    }

    return {
      totalRequests,
      averageCompressionRatio,
      averageProcessingTime,
      successRate,
      modelBreakdown,
    };
  }

  /**
   * Get recent metrics for a specific model
   */
  getRecentMetrics(
    model: string,
    limit: number = 100,
  ): ContextCompressionMetrics[] {
    return this.metrics.filter((m) => m.model === model).slice(-limit);
  }

  /**
   * Get compression ratio trends
   */
  getCompressionTrends(
    model: string,
    hours: number = 24,
  ): {
    timestamps: Date[];
    ratios: number[];
    tokens: number[];
  } {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentMetrics = this.metrics
      .filter((m) => m.model === model && m.timestamp >= cutoff && m.success)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    return {
      timestamps: recentMetrics.map((m) => m.timestamp),
      ratios: recentMetrics.map((m) => m.compressionRatio),
      tokens: recentMetrics.map((m) => m.compressedTokens),
    };
  }

  /**
   * Check if compression is performing well
   */
  isCompressionHealthy(model: string): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const recentMetrics = this.getRecentMetrics(model, 50);
    if (recentMetrics.length === 0) {
      return {
        healthy: true,
        issues: [],
        recommendations: [],
      };
    }

    const successfulMetrics = recentMetrics.filter((m) => m.success);
    const successRate = successfulMetrics.length / recentMetrics.length;
    const avgCompressionRatio =
      successfulMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) /
      successfulMetrics.length;
    const avgProcessingTime =
      successfulMetrics.reduce((sum, m) => sum + m.processingTime, 0) /
      successfulMetrics.length;

    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check success rate
    if (successRate < 0.95) {
      issues.push(`Low success rate: ${(successRate * 100).toFixed(1)}%`);
      recommendations.push(
        "Check for token limit errors and adjust safety margins",
      );
    }

    // Check compression ratio
    if (avgCompressionRatio > 0.9) {
      issues.push(
        `High compression ratio: ${(avgCompressionRatio * 100).toFixed(1)}%`,
      );
      recommendations.push(
        "Consider reducing chunk size or increasing relevance thresholds",
      );
    }

    // Check processing time
    if (avgProcessingTime > 5000) {
      // 5 seconds
      issues.push(`Slow processing: ${avgProcessingTime.toFixed(0)}ms average`);
      recommendations.push(
        "Consider using simpler compression strategies or reducing chunk count",
      );
    }

    return {
      healthy: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    return JSON.stringify(this.metrics, null, 2);
  }

  /**
   * Clear old metrics
   */
  clearOldMetrics(daysOld: number = 30): void {
    const cutoff = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    this.metrics = this.metrics.filter((m) => m.timestamp >= cutoff);
  }

  /**
   * Helper function to group metrics by property
   */
  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce(
      (groups, item) => {
        const groupKey = String(item[key]);
        if (!groups[groupKey]) {
          groups[groupKey] = [];
        }
        groups[groupKey].push(item);
        return groups;
      },
      {} as Record<string, T[]>,
    );
  }

  /**
   * Log metrics for monitoring
   */
  private logMetrics(metric: ContextCompressionMetrics): void {
    const logLevel = metric.success ? "info" : "error";
    const message =
      `Context compression: ${metric.model} | ` +
      `Ratio: ${(metric.compressionRatio * 100).toFixed(1)}% | ` +
      `Tokens: ${metric.originalTokens} → ${metric.compressedTokens} | ` +
      `Time: ${metric.processingTime}ms | ` +
      `Strategy: ${metric.strategy}`;

    if (metric.success) {
      console.log(`[CONTEXT-COMPRESSION] ${message}`);
    } else {
      console.error(
        `[CONTEXT-COMPRESSION-ERROR] ${message} | Error: ${metric.error}`,
      );
    }
  }
}

// Global monitor instance
export const contextCompressionMonitor = new ContextCompressionMonitor();

/**
 * Decorator to automatically monitor compression operations
 */
export function monitorCompression() {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
  ) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let success = false;
      let error: string | undefined;

      try {
        const result = await method.apply(this, args);
        success = true;
        return result;
      } catch (e) {
        error = e instanceof Error ? e.message : String(e);
        throw e;
      } finally {
        const processingTime = Date.now() - startTime;

        // Try to extract metrics from the context
        // This is a simplified version - you may need to adapt based on your actual method signature
        const metrics: Partial<ContextCompressionMetrics> = {
          processingTime,
          success,
          error,
        };

        // Record metrics if we have enough information
        if (Object.keys(metrics).length > 2) {
          contextCompressionMonitor.recordCompression(
            metrics as ContextCompressionMetrics,
          );
        }
      }
    };
  };
}

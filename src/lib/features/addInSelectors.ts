import { RootState } from "../store";

// Common
export const selectDocumentId = (state: RootState) => state.addIn.documentId;
export const selectDocumentUrl = (state: RootState) => state.addIn.documentUrl;
export const selectAddInNavigationPath = (state: RootState) =>
  state.addIn.addInNavigationPath;
export const selectBackRoute = (state: RootState) => state.addIn.backRoute;
export const selectAddInMode = (state: RootState) => state.addIn.mode;
export const selectHostType = (state: RootState) => state.addIn.hostType;

// Excel
export const selectWorksheets = (state: RootState) => state.addIn.worksheets;
export const selectActiveSheetName = (state: RootState) =>
  state.addIn.activeSheet;

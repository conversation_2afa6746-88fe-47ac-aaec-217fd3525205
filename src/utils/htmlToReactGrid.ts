import {
  ReactGrid,
  Column,
  Row,
  Cell,
  TextCell,
  NonEditableCell,
} from "@silevis/reactgrid";

// Interface for table data structure
interface TableData {
  rows: Row[];
  columns: Column[];
  cells: Cell[];
}

// Helper function to calculate row height based on largest cell text length
const calculateRowHeight = (cellTexts: string[]): number => {
  const baseHeight = 40; // Minimum height
  const lineHeight = 20; // Height per line
  const maxCharsPerLine = 80; // Characters per line

  // Find the largest cell text length
  const maxCellLength = Math.max(...cellTexts.map((text) => text?.length || 0));

  // Calculate lines needed for the largest cell
  const lines = Math.ceil(maxCellLength / maxCharsPerLine);

  // Return height based on content, with minimum of baseHeight
  return Math.max(baseHeight, lines * lineHeight);
};

// Helper function to get colspan and rowspan values from HTML element
const getSpanValues = (
  element: Element,
): { colSpan: number; rowSpan: number } => {
  const colSpan = parseInt(element.getAttribute("colspan") || "1", 10);
  const rowSpan = parseInt(element.getAttribute("rowspan") || "1", 10);
  return { colSpan, rowSpan };
};

// Helper function to check if a cell position is occupied by a spanning cell
const isPositionOccupied = (
  rowIndex: number,
  colIndex: number,
  spanningCells: Array<{
    rowIndex: number;
    colIndex: number;
    colSpan: number;
    rowSpan: number;
  }>,
): boolean => {
  return spanningCells.some((spanCell) => {
    const rowEnd = spanCell.rowIndex + spanCell.rowSpan - 1;
    const colEnd = spanCell.colIndex + spanCell.colSpan - 1;

    return (
      rowIndex >= spanCell.rowIndex &&
      rowIndex <= rowEnd &&
      colIndex >= spanCell.colIndex &&
      colIndex <= colEnd &&
      !(rowIndex === spanCell.rowIndex && colIndex === spanCell.colIndex)
    ); // Not the origin cell
  });
};

// This function takes in a string of HTML and returns ReactGrid compatible rows and columns
export const htmlToReactGrid = (
  html: string,
  parentWidth?: number,
): TableData => {
  // Create a temporary DOM element to parse the HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");

  // Find the first table in the HTML
  const table = doc.querySelector("table");

  if (!table) {
    // If no table found, return empty structure
    return {
      rows: [],
      columns: [],
      cells: [],
    };
  }

  const tableRows = table.querySelectorAll("tr");
  const rows: Row[] = [];
  const columns: Column[] = [];
  const cells: Cell[] = [];
  const spanningCells: Array<{
    rowIndex: number;
    colIndex: number;
    colSpan: number;
    rowSpan: number;
  }> = [];

  // First pass: collect all spanning cells and determine column count
  let maxColumns = 0;
  tableRows.forEach((tr, rowIndex) => {
    const cellsInRow = tr.querySelectorAll("td, th");
    let colIndex = 0;

    cellsInRow.forEach((cell) => {
      const { colSpan, rowSpan } = getSpanValues(cell);

      // Skip positions occupied by spanning cells from previous rows
      while (isPositionOccupied(rowIndex, colIndex, spanningCells)) {
        colIndex++;
      }

      // If this cell spans, record it
      if (colSpan > 1 || rowSpan > 1) {
        spanningCells.push({ rowIndex, colIndex, colSpan, rowSpan });
      }

      colIndex += colSpan;
    });

    maxColumns = Math.max(maxColumns, colIndex);
  });

  // Calculate column width dynamically based on parent width
  const defaultColumnWidth = 150; // Fallback width
  const columnWidth =
    parentWidth && maxColumns > 0
      ? Math.max(parentWidth / maxColumns, 100) // Minimum width of 100px
      : defaultColumnWidth;

  // Create columns based on the maximum number of columns found
  for (let i = 0; i < maxColumns; i++) {
    columns.push({
      colIndex: i,
      width: columnWidth,
    });
  }

  // Second pass: create rows and cells
  tableRows.forEach((tr, rowIndex) => {
    const cellsInRow = tr.querySelectorAll("td, th");

    // Create row
    const row: Row = {
      rowIndex: rowIndex,
      height: calculateRowHeight(
        Array.from(cellsInRow).map((cell) => cell.textContent?.trim() || ""),
      ),
    };
    rows.push(row);

    let colIndex = 0;

    // Process each cell in the row
    cellsInRow.forEach((cell) => {
      const cellText = cell.textContent?.trim() || "";
      const isHeader = cell.tagName.toLowerCase() === "th" || rowIndex === 0;
      const { colSpan, rowSpan } = getSpanValues(cell);

      // Skip positions occupied by spanning cells from previous rows
      while (isPositionOccupied(rowIndex, colIndex, spanningCells)) {
        colIndex++;
      }

      // Create cell
      const cellData: Cell = {
        rowIndex: rowIndex,
        colIndex: colIndex,
        Template: isHeader ? NonEditableCell : TextCell,
        props: isHeader
          ? {
              value: cellText,
              style: { background: "var(--virgil-color-primary-500)" },
            }
          : { text: cellText, onTextChanged: () => {} },
        // Add colspan and rowspan if they are greater than 1
        ...(colSpan > 1 && { colSpan }),
        ...(rowSpan > 1 && { rowSpan }),
      };

      cells.push(cellData);
      colIndex += colSpan;
    });
  });

  return { rows, columns, cells };
};

// Alternative function that takes in an array of rows and columns and returns a ReactGrid component
export const createReactGridComponent = (tableData: TableData) => {
  return {
    rows: tableData.rows,
    columns: tableData.columns,
    cells: tableData.cells,
  };
};

// Helper function to convert simple string data to ReactGrid format
export const stringToReactGrid = (
  data: string[][],
  parentWidth?: number,
): TableData => {
  if (data.length === 0) {
    return { rows: [], columns: [], cells: [] };
  }

  // Calculate column width dynamically based on parent width
  const defaultColumnWidth = 150; // Fallback width
  const maxColumns = data[0]?.length || 0;
  const columnWidth =
    parentWidth && maxColumns > 0
      ? Math.max(parentWidth / maxColumns, 100) // Minimum width of 100px
      : defaultColumnWidth;

  const columns: Column[] =
    data[0]?.map((_, index) => ({
      colIndex: index,
      width: columnWidth,
    })) || [];

  const rows: Row[] = data.map((row, rowIndex) => ({
    rowIndex: rowIndex,
    height: calculateRowHeight(row.map((cell) => cell.trim())),
  }));

  const cells: Cell[] = [];

  data.forEach((row, rowIndex) => {
    row.forEach((cell, cellIndex) => {
      const isHeader = rowIndex === 0;
      const cellData: Cell = {
        rowIndex: rowIndex,
        colIndex: cellIndex,
        Template: isHeader ? NonEditableCell : TextCell,
        props: isHeader
          ? { value: cell.trim() }
          : { text: cell.trim(), onTextChanged: () => {} },
      };
      cells.push(cellData);
    });
  });

  return { rows, columns, cells };
};

/*
Usage Example:

import React, { useRef, useEffect, useState } from 'react';
import { ReactGrid } from '@silevis/reactgrid';
import { htmlToReactGrid } from './htmlToReactGrid';

function TableComponent() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [parentWidth, setParentWidth] = useState<number | undefined>(undefined);

  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setParentWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  const htmlTable = `
    <table>
      <tr>
        <th colspan="2">Name</th>
        <th rowspan="2">Age</th>
      </tr>
      <tr>
        <th>First</th>
        <th>Last</th>
      </tr>
      <tr>
        <td>Thomas</td>
        <td>Goldman</td>
        <td>30</td>
      </tr>
      <tr>
        <td>Susie</td>
        <td>Quattro</td>
        <td>25</td>
      </tr>
    </table>
  `;

  const { rows, columns, cells } = htmlToReactGrid(htmlTable, parentWidth);

  return (
    <div ref={containerRef} className="w-full h-full">
      <ReactGrid
        rows={rows}
        columns={columns}
        cells={cells}
      />
    </div>
  );
}

// Or using string data:
const data = [
  ['Name', 'Surname'],
  ['Thomas', 'Goldman'],
  ['Susie', 'Quattro']
];

const { rows, columns, cells } = stringToReactGrid(data, parentWidth);
*/

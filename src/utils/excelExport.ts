import * as XLSX from "xlsx";

export interface CategoryExportData {
    seed_id: string;
    parent_seed_id: string;
    name: string;
    description: string;
    stale_after_days: number;
    status: string;
    allocated: string;
}

// Helper function to style the worksheet
const styleWorksheet = (worksheet: XLSX.WorkSheet) => {
    // Set column widths
    const columnWidths = [
        { wch: 10 }, // seed_id
        { wch: 15 }, // parent_seed_id
        { wch: 40 }, // name
        { wch: 50 }, // description
        { wch: 15 }, // status
        { wch: 15 }, // stale_after_days
    ];
    worksheet['!cols'] = columnWidths;

    // Style the headers
    const headerStyle = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "366092" } },
        alignment: { horizontal: "center" as const }
    };

    // Apply header styles
    const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:F1');
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!worksheet[cellAddress]) {
            worksheet[cellAddress] = { v: '', t: 's' };
        }
        worksheet[cellAddress].s = headerStyle;
    }


};

export const exportCategoriesToExcel = (data: CategoryExportData[], filename: string = 'categories') => {
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Style the worksheet
    styleWorksheet(worksheet);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Categories');

    // Write file
    XLSX.writeFile(workbook, `${filename}.xlsx`);
};

export const downloadExcelFile = (data: CategoryExportData[], filename: string = 'categories') => {
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Style the worksheet
    styleWorksheet(worksheet);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Categories');

    // Generate blob and download
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}; 
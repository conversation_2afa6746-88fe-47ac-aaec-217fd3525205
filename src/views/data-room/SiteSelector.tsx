import {
  <PERSON>complete,
  Box,
  CircularProgress,
  <PERSON><PERSON>ield,
  Tooltip,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Iconify } from "~/components/iconify";
import { toast } from "~/components/snackbar";
import { type OrgGetType } from "~/server/api/routers/organization";
import { api } from "~/trpc/react";

type SiteSelectorProps = {
  org: OrgGetType;
};

type OptionType = {
  label: string;
  value: string;
};

export function SiteSelector({ org }: SiteSelectorProps) {
  const [value, setValue] = useState<OptionType | null>(null);
  const [options, setOptions] = useState<OptionType[]>([]);
  const [webhookState, setWebhookState] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");
  const [webhookError, setWebhookError] = useState<string | null>(null);
  const [azureDriveId, setAzureDriveId] = useState<string | null>(null);

  const sites = api.azure.listAllSites.useQuery(undefined, {
    enabled: !!org?.azureAccessTokenId,
  });

  const setConnectedSites = api.azure.setConnectedSites.useMutation();
  const registerWebhook = api.azure.registerWebhook.useMutation();
  const deleteAllWebhooks = api.azure.deleteAllWebhooks.useMutation();
  const getConnectedSites = api.azure.getConnectedSites.useQuery(undefined, {
    enabled: !!org?.azureAccessTokenId,
  });

  const utils = api.useUtils();

  useEffect(() => {
    const options =
      sites.data?.value
        .filter((x) => x.name)
        .map((site) => ({
          label: site.name ?? "",
          value: site.id,
        })) ?? [];

    setOptions(options);
  }, [sites.data]);

  useEffect(() => {
    const value = getConnectedSites.data?.[0];

    setValue({
      label: value?.name ?? "",
      value: value?.azureId ?? "",
    });
  }, [getConnectedSites.data]);

  useEffect(() => {
    if (
      getConnectedSites.data?.[0]?.subscriptions[0]?.expirationDateTime &&
      getConnectedSites.data?.[0]?.subscriptions[0]?.expirationDateTime >
      new Date()
    ) {
      setWebhookState("success");
    } else {
      setWebhookState("error");
      setWebhookError("Subscription expired");
    }
  }, [getConnectedSites.data]);

  // useEffect(() => {
  //   if (azureDriveId && value) {
  //     console.log("registering webhook for", value);

  //     setWebhookState("loading");
  //     registerWebhook.mutate(
  //       {
  //         sites: [{ name: value?.label ?? "", azureId: value?.value ?? "" }],
  //         azureDriveId: azureDriveId,
  //       },
  //       {
  //         onSuccess: () => {
  //           setWebhookState("success");
  //         },
  //         onError: (error) => {
  //           setWebhookState("error");
  //           setWebhookError(error.message);
  //         },
  //       },
  //     );
  //   }
  // }, [azureDriveId, value]);

  return (
    <Autocomplete
      sx={{ width: 200 }}
      options={options}
      value={value}
      loading={sites.isLoading}
      loadingText="Loading sites..."
      size="small"
      isOptionEqualToValue={(option, value) => option.value === value.value}
      renderInput={(params) => (
        <Box display="flex" alignItems="center" gap={1}>
          <TextField
            {...params}
            label="Site"
          // helperText={webhookState === "error" ? webhookError : undefined}
          />
          {webhookState === "loading" && <CircularProgress size={16} />}
          {webhookState === "success" && (
            <Tooltip title="Real-time updates enabled">
              <Iconify
                icon="mdi:check"
                width={18}
                height={18}
                sx={{
                  color: "white",
                  backgroundColor: "green",
                  borderRadius: "50%",
                  p: 0.5,
                }}
              />
            </Tooltip>
          )}
          {webhookState === "error" && (
            <Tooltip
              title={`Real-time updates disabled. Click to reauthorize. Error: ${webhookError}`}
            >
              <Iconify
                icon="mdi:alert-circle"
                width={18}
                height={18}
                sx={{
                  color: "white",
                  backgroundColor: "red",
                  borderRadius: "50%",
                  p: 0,
                }}
                onClick={() => {
                  setWebhookState("loading");
                  deleteAllWebhooks.mutate(
                    {
                      sites: [{ name: "", azureId: "" }],
                    },
                    {
                      onSuccess: () => {
                        registerWebhook.mutate(
                          {
                            sites: [
                              {
                                name: value?.label ?? "",
                                azureId: value?.value ?? "",
                              },
                            ],
                            azureDriveId: org.AzureDrive?.[0]?.id ?? "",
                          },
                          {
                            onSuccess: async () => {
                              setWebhookState("success");
                              await utils.organization.get.refetch();
                            },
                            onError: (error) => {
                              setWebhookState("error");
                              setWebhookError(error.message);
                            },
                          },
                        );
                      },
                      onError: (error) => {
                        setWebhookState("error");
                        setWebhookError(error.message);
                      },
                    },
                  );
                }}
              />
            </Tooltip>
          )}
        </Box>
      )}
      onChange={(_, value) => {
        console.log("value", value);

        setWebhookState("loading");

        setConnectedSites.mutate(
          {
            // Only connecting one drive for now
            // Should be able to connect multiple drives in the future
            sites:
              value !== null
                ? [
                  {
                    name: value?.label ?? "",
                    azureId: value?.value ?? "",
                  },
                ]
                : [],
          },
          {
            onSuccess: (data) => {
              console.log("onSuccess", data);

              if (data.length > 0) {
                setAzureDriveId(data[0]?.id ?? null);
                toast.success("Site connected");

                registerWebhook.mutate(
                  {
                    sites: [
                      { name: value?.label ?? "", azureId: value?.value ?? "" },
                    ],
                    azureDriveId: data[0]?.id ?? "",
                  },
                  {
                    onSuccess: () => {
                      setWebhookState("success");
                      toast.success("Real-time updates enabled");

                      // Refresh the connected sites
                      utils.organization.get.invalidate();
                    },
                    onError: (error) => {
                      setWebhookState("error");
                      setWebhookError(error.message);
                    },
                  },
                );
              } else {
                toast.error("Site disconnected");
                setWebhookState("idle");
              }
            },
            onError: (error) => {
              toast.error(`Error connecting site: ${error.message}`);
              setWebhookState("idle");
            },
          },
        );

        if (value == null) {
          deleteAllWebhooks.mutate(
            { sites: [{ name: "", azureId: "" }] },
            {
              onSettled: () => {
                setWebhookState("idle");
                setAzureDriveId(null);
              },
            },
          );
        }
      }}
    />
  );
}

"use client";

import { Category, type Fund, type Tag } from "@prisma/client";
import { FilterIcon } from "lucide-react";
import { useState } from "react";
import { type UseSetStateReturn } from "src/hooks/use-set-state";
import type { IFileFilters } from "src/types/file";
import { env } from "~/env";
import {
  MultiSelect,
  MultiSelectClear,
  MultiSelectCommand,
  MultiSelectCommandItem,
  MultiSelectCommandList,
  MultiSelectContent,
  MultiSelectTrigger,
  MultiSelectValues,
  Option,
} from "~/v2/components/composit/MultiSelect/MultiSelect";
import { Badge } from "~/v2/components/ui/Badge";
import { Button } from "~/v2/components/ui/Button";
import { CardContent } from "~/v2/components/ui/Card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import { Typography } from "~/v2/components/ui/Typography";

export const FundsAndTagsFilterPopup = ({
  tags,
  funds,
  categories,
  filters,
  buttonText,
}: {
  tags: Tag[] | undefined;
  funds: Fund[] | undefined;
  categories: Category[] | undefined;
  filters: UseSetStateReturn<IFileFilters>;
  buttonText?: string | null;
}) => {
  const [open, setOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedFunds, setSelectedFunds] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const tagOptions: Option[] =
    tags?.map((tag) => ({ value: tag.id, label: tag.name })) ?? [];
  const fundOptions: Option[] =
    funds?.map((fund) => ({ value: fund.id, label: fund.name })) ?? [];
  const categoryOptions: Option[] =
    categories?.filter(c => !!c.parentId).map((category) => ({
      value: category.id,
      label: category.name,
    })) ?? [];

  const handleTagsChange = (selected: string[]) => {
    setSelectedTags(selected);
    filters.setState({
      tags: selected
        .map((id) => {
          const tag = tags?.find((tag) => tag.id === id);
          return tag ? { name: tag.name, id: tag.id, color: tag.color } : null;
        })
        .filter((tag) => tag !== null),
    });
  };

  const handleCategoriesChange = (selected: string[]) => {
    setSelectedCategories(selected);
    filters.setState({
      categories: selected
        .map((id) => {
          const category = categories?.find((category) => category.id === id);
          return category ? { name: category.name, id: category.id } : null;
        })
        .filter((category) => category !== null),
    });
  };
  const handleFundsChange = (selected: string[]) => {
    setSelectedFunds(selected);
    filters.setState({
      funds: selected
        .map((id) => {
          const fund = funds?.find((fund) => fund.id === id);
          return fund
            ? { name: fund.name, id: fund.id, color: "#000000" }
            : null;
        })
        .filter((fund) => fund !== null),
    });
  };

  const handleClear = () => {
    setSelectedTags([]);
    setSelectedFunds([]);
    setSelectedCategories([]);
    filters.setState({ tags: [], funds: [], categories: [] });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="min-w-0 h-9 !text-[10px]"
        >
          <FilterIcon className="mr-1" size={16} />
          {buttonText}
          {[...selectedTags, ...selectedFunds, ...selectedCategories].length >
            0 && (
            <Badge className="ml-2" variant="secondary">
              {`${selectedTags.length} tags, ${selectedFunds.length} funds, ${selectedCategories.length} categories`}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[400px] max-w-[90vw] p-0">
        <CardContent className="p-4 space-y-4">
          <div className="flex flex-col gap-4">
            {env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true" && (
              <>
                <Typography variant="body" className="font-bold mt-2">
                  Categories
                </Typography>
                <MultiSelect
                  options={categoryOptions}
                  selected={selectedCategories}
                  onClose={handleCategoriesChange}
                >
                  <MultiSelectTrigger>
                    <MultiSelectValues placeholder="Select categories" />
                  </MultiSelectTrigger>
                  <MultiSelectContent>
                    <MultiSelectCommand>
                      <MultiSelectCommandList>
                        {categoryOptions.map((option) => (
                          <MultiSelectCommandItem
                            key={option.value}
                            value={option.value}
                          >
                            {option.label}
                          </MultiSelectCommandItem>
                        ))}
                      </MultiSelectCommandList>
                      <MultiSelectClear />
                    </MultiSelectCommand>
                  </MultiSelectContent>
                </MultiSelect>
              </>
            )}
            <Typography variant="body" className="font-bold">
              Tags
            </Typography>
            <MultiSelect
              options={tagOptions}
              selected={selectedTags}
              onClose={handleTagsChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select tags" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {tagOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectClear />
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
            <Typography variant="body" className="font-bold mt-2">
              Funds
            </Typography>
            <MultiSelect
              options={fundOptions}
              selected={selectedFunds}
              onClose={handleFundsChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select funds" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {fundOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectClear />
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
            <div className="flex flex-row gap-4 mt-2 w-full">
              <Button
                variant="outline"
                onClick={handleClear}
                className="w-full"
              >
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </PopoverContent>
    </Popover>
  );
};

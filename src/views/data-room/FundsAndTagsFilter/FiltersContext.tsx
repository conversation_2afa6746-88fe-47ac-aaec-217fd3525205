"use client";
import React, { createContext, useContext } from 'react';
import { useSetState, type UseSetStateReturn } from "src/hooks/use-set-state";
import type { IFileFilters } from "src/types/file";

const FiltersContext = createContext<UseSetStateReturn<IFileFilters> | undefined>(undefined);

export const FiltersProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const filters = useSetState<IFileFilters>({
        name: "",
        type: [],
        tags: [],
        funds: [],
        categories: [],
        startDate: null,
        endDate: null,
    });

    return (
        <FiltersContext.Provider value={filters}>
            {children}
        </FiltersContext.Provider>
    );
};

export const useFilters = () => {
    const context = useContext(FiltersContext);
    if (!context) {
        throw new Error("useFilters must be used within a FiltersProvider");
    }
    return context;
};
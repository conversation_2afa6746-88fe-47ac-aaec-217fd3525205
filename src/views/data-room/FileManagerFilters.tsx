import type { UseSetStateReturn } from "src/hooks/use-set-state";
import type { IDatePickerControl } from "src/types/common";
import type { IFileFilters } from "src/types/file";

import { useCallback } from "react";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import CardActionArea from "@mui/material/CardActionArea";
import InputAdornment from "@mui/material/InputAdornment";
import Stack from "@mui/material/Stack";
import { Input } from "src/v2/components/ui/Input";

import { fDateRangeShortLabel } from "src/utils/format-time";

import { varAlpha } from "src/theme/styles";

import { Typography } from "@mui/material";
import { CustomDateRangePicker } from "src/components/custom-date-range-picker";
import { CustomPopover, usePopover } from "src/components/custom-popover";
import { FileThumbnail } from "src/components/file-thumbnail";
import { Iconify } from "src/components/iconify";
import { Label } from "src/components/label";

// ----------------------------------------------------------------------

type Props = {
  dateError: boolean;
  openDateRange: boolean;
  onResetPage: () => void;
  onOpenDateRange: () => void;
  onCloseDateRange: () => void;
  filters: UseSetStateReturn<IFileFilters>;
  options: {
    types: string[];
    tags: { name: string; id: string; color: string }[];
    funds: { name: string; id: string; color: string }[];
    categories: { name: string; id: string }[];
  };
};

export function FileManagerFilters({
  filters,
  options,
  dateError,
  onResetPage,
  openDateRange,
  onOpenDateRange,
  onCloseDateRange,
}: Props) {
  const popover = usePopover();
  const tagsPopover = usePopover();
  const fundsPopover = usePopover();
  const categoriesPopover = usePopover();

  const renderLabel = filters.state.type.length
    ? filters.state.type.slice(0, 2).join(",")
    : "All type";

  const renderTagLabel = filters.state.tags.length ? (
    <Box
      sx={{
        display: "flex",
        flexWrap: "wrap",
        flexDirection: "column",
        gap: 1,
      }}
    >
      {filters.state.tags.slice(0, 2).map((tag) => (
        <Typography variant="caption" key={tag.id}>
          {tag.name}
        </Typography>
      ))}
    </Box>
  ) : (
    "All tags"
  );

  const renderFundLabel = filters.state.funds.length ? (
    <Box
      sx={{
        display: "flex",
        flexWrap: "wrap",
        flexDirection: "column",
        gap: 1,
      }}
    >
      {filters.state.funds.slice(0, 2).map((fund) => (
        <Typography variant="caption" key={fund.id}>
          {fund.name}
        </Typography>
      ))}
    </Box>
  ) : (
    "All funds"
  );

  const handleFilterName = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onResetPage();
      filters.setState({ name: event.target.value });
    },
    [filters, onResetPage],
  );

  const handleFilterStartDate = useCallback(
    (newValue: IDatePickerControl) => {
      onResetPage();
      filters.setState({ startDate: newValue });
    },
    [filters, onResetPage],
  );

  const handleFilterEndDate = useCallback(
    (newValue: IDatePickerControl) => {
      filters.setState({ endDate: newValue });
    },
    [filters],
  );

  const handleFilterType = useCallback(
    (newValue: string) => {
      const checked = filters.state.type.includes(newValue)
        ? filters.state.type.filter((value) => value !== newValue)
        : [...filters.state.type, newValue];

      filters.setState({ type: checked });
    },
    [filters],
  );

  const handleFilterTags = useCallback(
    (newValue: { name: string; id: string; color: string }) => {
      const checked = filters.state.tags.includes(newValue)
        ? filters.state.tags.filter((value) => value !== newValue)
        : [...filters.state.tags, newValue];

      filters.setState({ tags: checked });
    },
    [filters],
  );

  const handleFilterCategories = useCallback(
    (newValue: { name: string; id: string }) => {
      const checked = filters.state.categories.includes(newValue)
        ? filters.state.categories.filter((value) => value !== newValue)
        : [...filters.state.categories, newValue];

      filters.setState({ categories: checked });
    },
    [filters],
  );

  const handleFilterFunds = useCallback(
    (newValue: { name: string; id: string; color: string }) => {
      const checked = filters.state.funds.includes(newValue)
        ? filters.state.funds.filter((value) => value !== newValue)
        : [...filters.state.funds, newValue];

      filters.setState({ funds: checked });
    },
    [filters],
  );

  const handleResetType = useCallback(() => {
    popover.onClose();
    filters.setState({ type: [] });
  }, [filters, popover]);

  const handleResetTags = useCallback(() => {
    tagsPopover.onClose();
    filters.setState({ tags: [] });
  }, [filters, tagsPopover]);

  const handleResetFunds = useCallback(() => {
    fundsPopover.onClose();
    filters.setState({ funds: [] });
  }, [filters, fundsPopover]);

  const handleResetCategories = useCallback(() => {
    categoriesPopover.onClose();
    filters.setState({ categories: [] });
  }, [filters, categoriesPopover]);

  const renderFilterName = (
    <div className="relative bg-white w-full flex-1">
      <Input
        value={filters.state.name}
        onChange={handleFilterName}
        placeholder="Search..."
        className="pl-10"
      />
      <div className="absolute left-2 top-1/2 transform -translate-y-1/2">
        <Iconify icon="eva:search-fill" sx={{ color: "text.disabled" }} />
      </div>
    </div>
  );

  const renderFilterType = (
    <>
      <Button
        color="inherit"
        onClick={popover.onOpen}
        endIcon={
          <Iconify
            icon={
              popover.open
                ? "eva:arrow-ios-upward-fill"
                : "eva:arrow-ios-downward-fill"
            }
            sx={{ ml: -0.5 }}
          />
        }
      >
        {renderLabel}
        {filters.state.type.length > 2 && (
          <Label color="info" sx={{ ml: 1 }}>
            +{filters.state.type.length - 2}
          </Label>
        )}
      </Button>

      <CustomPopover
        open={popover.open}
        anchorEl={popover.anchorEl}
        onClose={popover.onClose}
        slotProps={{ paper: { sx: { p: 2.5 } } }}
      >
        <Stack spacing={2.5}>
          <Box
            gap={1}
            display="grid"
            gridTemplateColumns={{ xs: "repeat(2, 1fr)", sm: "repeat(4, 1fr)" }}
          >
            {options.types.map((type) => {
              const selected = filters.state.type.includes(type);

              return (
                <CardActionArea
                  key={type}
                  onClick={() => handleFilterType(type)}
                  sx={{
                    p: 1,
                    borderRadius: 1,
                    cursor: "pointer",
                    border: (theme) =>
                      `solid 1px ${varAlpha(theme.vars.palette.grey["500Channel"], 0.08)}`,
                    ...(selected && { bgcolor: "action.selected" }),
                  }}
                >
                  <Stack
                    spacing={1}
                    direction="row"
                    alignItems="center"
                    sx={{
                      typography: "caption",
                      textTransform: "capitalize",
                      ...(selected && { fontWeight: "fontWeightSemiBold" }),
                    }}
                  >
                    <FileThumbnail file={type} sx={{ width: 24, height: 24 }} />
                    {type}
                  </Stack>
                </CardActionArea>
              );
            })}
          </Box>

          <Stack
            spacing={1.5}
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
          >
            <Button
              variant="outlined"
              color="inherit"
              onClick={handleResetType}
            >
              Clear
            </Button>

            <Button variant="contained" onClick={popover.onClose}>
              Apply
            </Button>
          </Stack>
        </Stack>
      </CustomPopover>
    </>
  );

  const renderFilterDate = (
    <>
      <Button
        color="inherit"
        onClick={onOpenDateRange}
        endIcon={
          <Iconify
            icon={
              openDateRange
                ? "eva:arrow-ios-upward-fill"
                : "eva:arrow-ios-downward-fill"
            }
            sx={{ ml: -0.5 }}
          />
        }
      >
        {!!filters.state.startDate && !!filters.state.endDate
          ? fDateRangeShortLabel(filters.state.startDate, filters.state.endDate)
          : "Select date"}
      </Button>

      <CustomDateRangePicker
        variant="input"
        startDate={filters.state.startDate}
        endDate={filters.state.endDate}
        onChangeStartDate={handleFilterStartDate}
        onChangeEndDate={handleFilterEndDate}
        open={openDateRange}
        onClose={onCloseDateRange}
        selected={!!filters.state.startDate && !!filters.state.endDate}
        error={dateError}
      />
    </>
  );

  const renderFilterTags = (
    <>
      <Button
        color="inherit"
        onClick={tagsPopover.onOpen}
        endIcon={
          <Iconify
            icon={
              tagsPopover.open
                ? "eva:arrow-ios-upward-fill"
                : "eva:arrow-ios-downward-fill"
            }
            sx={{ ml: -0.5 }}
          />
        }
      >
        {renderTagLabel}
        {filters.state.tags.length > 2 && (
          <Label color="info" sx={{ ml: 1 }}>
            +{filters.state.tags.length - 2}
          </Label>
        )}
      </Button>

      <CustomPopover
        open={tagsPopover.open}
        anchorEl={tagsPopover.anchorEl}
        onClose={tagsPopover.onClose}
        slotProps={{ paper: { sx: { p: 2.5 } } }}
      >
        <Stack spacing={2.5}>
          <Box
            gap={1}
            display="grid"
            gridTemplateColumns={{ xs: "repeat(2, 1fr)", sm: "repeat(4, 1fr)" }}
          >
            {options.tags.map((tag) => {
              const selected = filters.state.tags.some(
                (item) => item.id === tag.id,
              );

              return (
                <CardActionArea
                  key={tag.id}
                  onClick={() => handleFilterTags(tag)}
                  sx={{
                    p: 1,
                    borderRadius: 1,
                    cursor: "pointer",
                    border: (theme) =>
                      `solid 1px ${varAlpha(theme.vars.palette.grey["500Channel"], 0.08)}`,
                    ...(selected && { bgcolor: "action.selected" }),
                  }}
                >
                  <Stack
                    spacing={1}
                    direction="row"
                    alignItems="center"
                    sx={{
                      typography: "caption",
                      textTransform: "capitalize",
                      ...(selected && { fontWeight: "fontWeightSemiBold" }),
                    }}
                  >
                    {tag.name}
                  </Stack>
                </CardActionArea>
              );
            })}
          </Box>

          <Stack
            spacing={1.5}
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
          >
            <Button
              variant="outlined"
              color="inherit"
              onClick={handleResetTags}
            >
              Clear
            </Button>

            <Button variant="contained" onClick={tagsPopover.onClose}>
              Apply
            </Button>
          </Stack>
        </Stack>
      </CustomPopover>
    </>
  );

  const renderFilterFunds = (
    <>
      <Button
        color="inherit"
        onClick={fundsPopover.onOpen}
        endIcon={
          <Iconify
            icon={
              fundsPopover.open
                ? "eva:arrow-ios-upward-fill"
                : "eva:arrow-ios-downward-fill"
            }
            sx={{ ml: -0.5 }}
          />
        }
      >
        {renderFundLabel}
        {filters.state.funds.length > 2 && (
          <Label color="info" sx={{ ml: 1 }}>
            +{filters.state.funds.length - 2}
          </Label>
        )}
      </Button>

      <CustomPopover
        open={fundsPopover.open}
        anchorEl={fundsPopover.anchorEl}
        onClose={fundsPopover.onClose}
        slotProps={{ paper: { sx: { p: 2.5 } } }}
      >
        <Stack spacing={2.5}>
          <Box
            gap={1}
            display="grid"
            gridTemplateColumns={{ xs: "repeat(2, 1fr)", sm: "repeat(4, 1fr)" }}
          >
            {options.funds.map((fund) => {
              const selected = filters.state.funds.some(
                (item) => item.id === fund.id,
              );

              return (
                <CardActionArea
                  key={fund.id}
                  onClick={() => handleFilterFunds(fund)}
                  sx={{
                    p: 1,
                    borderRadius: 1,
                    cursor: "pointer",
                    border: (theme) =>
                      `solid 1px ${varAlpha(theme.vars.palette.grey["500Channel"], 0.08)}`,
                    ...(selected && { bgcolor: "action.selected" }),
                  }}
                >
                  <Stack
                    spacing={1}
                    direction="row"
                    alignItems="center"
                    sx={{
                      typography: "caption",
                      textTransform: "capitalize",
                      ...(selected && { fontWeight: "fontWeightSemiBold" }),
                    }}
                  >
                    {fund.name}
                  </Stack>
                </CardActionArea>
              );
            })}
          </Box>

          <Stack
            spacing={1.5}
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
          >
            <Button
              variant="outlined"
              color="inherit"
              onClick={handleResetFunds}
            >
              Clear
            </Button>

            <Button variant="contained" onClick={fundsPopover.onClose}>
              Apply
            </Button>
          </Stack>
        </Stack>
      </CustomPopover>
    </>
  );

  return (
    <div className="flex flex-col md:flex-row gap-4 md:items-center flex-1">
      {renderFilterName}

      {/* <div className="flex items-center justify-end gap-4 flex-grow">
        {renderFilterDate}
        {renderFilterTags}
        {renderFilterFunds}
      </div> */}
    </div>
  );
}

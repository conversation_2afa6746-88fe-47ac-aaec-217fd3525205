"use client";

import { ActionButton, type ActionOption } from "~/v2/components/composit/ActionButton";

interface ProcessActionButtonsProps {
  processLoading: boolean;
  onProcess: (options: { skipReady?: boolean; onlyDDQs?: boolean }) => void;
  onGenerateAnswers: (options: {
    onlyErrored?: boolean;
    onlyEmpty?: boolean;
  }) => void;
  onClassify: () => void;
}

export function ProcessActionButtons({
  processLoading,
  onProcess,
  onGenerateAnswers,
  onClassify,
}: ProcessActionButtonsProps) {
  const options: ActionOption[] = [
    {
      label: "Process File(s)",
      action: () => onProcess({ skipReady: true }),
    },
    {
      label: "Reprocess Existing",
      action: () => onProcess({}),
    },
    {
      label: "Reclassify",
      action: () => onClassify(),
    },
    {
      label: "Process only DDQs",
      action: () => onProcess({ onlyDDQs: true }),
    },
    {
      label: "Generate New Answers",
      action: () => onGenerateAnswers({ onlyEmpty: true }),
    },
    {
      label: "Regenerate All Answers",
      action: () => onGenerateAnswers({}),
    },
    {
      label: "Regenerate Errored Answers",
      action: () => onGenerateAnswers({ onlyErrored: true }),
    },
  ];

  return (
    <ActionButton
      options={options}
      disabled={processLoading}
      label="File Actions"
      variant="default"
      menuClassName="w-56"
    />
  );
}

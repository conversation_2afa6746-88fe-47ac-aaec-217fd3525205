import { Button } from "@mui/material";
import { CategoryStatus, Org } from "@prisma/client";
import { XIcon } from "lucide-react";
import { CompleteCategory } from "prisma/zod";
import React, { useMemo, useState } from "react";
import { env } from "~/env";
import { api } from "~/trpc/react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "~/v2/components/ui/Dialog";
import { Separator } from "~/v2/components/ui/Separator";
import { cn } from "~/v2/lib/utils";
import { ChecklistItem } from "./ChecklistItem";
import { ParentCategoryList } from "./ParentCategoryList";

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  org: Org;
}

export const ChecklistDialog: React.FC<Props> = ({ open, setOpen, org }) => {
  const { data: categories, isLoading } =
    api.category.getCategoryTreeWithDocuments.useQuery(
      {},
      {
        enabled: !!org.id,
      },
    );

  const [selectedCategory, setSelectedCategory] =
    useState<CompleteCategory | null>(null);

  const visibleCategories: CompleteCategory[] = useMemo(() => {
    if (selectedCategory)
      return (
        selectedCategory?.children?.filter(
          (child) => child.status === CategoryStatus.ACTIVE,
        ) ?? []
      );
    return (categories as CompleteCategory[]) ?? [];
  }, [selectedCategory, categories]);

  const { data: checklistCountResponse } = api.category.countStatus.useQuery(
    {
      categoryId: selectedCategory?.id,
    },
    {
      enabled:
        !!org.id &&
        env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true" &&
        !!selectedCategory?.id,
    },
  );

  const { data: overallChecklistCountResponse } =
    api.category.countStatus.useQuery(
      {
        categoryId: undefined,
      },
      {
        enabled:
          !!org.id && env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true",
      },
    );

  const checklistProgress = useMemo(() => {
    if (!checklistCountResponse) {
      return 0;
    }
    const progress = checklistCountResponse.total
      ? ((checklistCountResponse.linked ?? 0) / checklistCountResponse.total) *
        100
      : 0;

    return progress;
  }, [checklistCountResponse]);

  const overallChecklistProgress = useMemo(() => {
    if (!overallChecklistCountResponse) {
      return 0;
    }
    const progress = overallChecklistCountResponse.total
      ? ((overallChecklistCountResponse.linked ?? 0) /
          overallChecklistCountResponse.total) *
        100
      : 0;

    return progress;
  }, [overallChecklistCountResponse]);

  const progressColor = (value: number) => {
    if (value < 99) {
      return "bg-warning";
    }
    return "bg-success";
  };

  const handleClickCategory = (category: CompleteCategory) => {
    if (!selectedCategory) setSelectedCategory(category);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="p-2 w-full !max-w-[1000px]"
        showCloseButton={false}
      >
        <section className="flex flex-col gap-2 !max-h-[800px] overflow-y-auto">
          <DialogHeader className="flex flex-row items-center justify-between border-b border-gray-200 pb-1">
            <DialogTitle>Diligence Checklist</DialogTitle>
            <Button
              variant="text"
              color="primary"
              onClick={() => setOpen(false)}
            >
              <XIcon />
            </Button>
          </DialogHeader>

          <section className="flex gap-2">
            <ParentCategoryList
              categories={categories}
              selectedCategory={selectedCategory}
              setSelectedCategory={setSelectedCategory}
            />

            <section className="flex flex-col gap-2 w-3/4 !max-h-[500px] overflow-y-auto">
              <header>
                <span className="flex flex-row items-center gap-1">
                  <div
                    className={cn(
                      "w-1.5 h-1.5 rounded-full",
                      progressColor(
                        Math.round(
                          selectedCategory
                            ? checklistProgress
                            : overallChecklistProgress,
                        ),
                      ),
                    )}
                  ></div>
                  <h2 className="text-lg font-bold">
                    {selectedCategory
                      ? selectedCategory.name
                      : Math.round(checklistProgress) === 100
                        ? "Due Diligence Progress: Complete"
                        : "Due Diligence Progress: Incomplete"}
                  </h2>
                </span>
                <h3 className="text-sm">{selectedCategory?.description}</h3>
              </header>

              <Separator />

              <div className="flex flex-col gap-1">
                {visibleCategories?.length > 0 ? (
                  visibleCategories?.map((category: CompleteCategory) => (
                    <ChecklistItem
                      isParentCategory={!selectedCategory}
                      key={category?.id}
                      org={org}
                      category={category}
                      onClick={() => handleClickCategory(category)}
                    />
                  ))
                ) : (
                  <div className="text-sm text-gray-600">
                    No active checklist items
                  </div>
                )}
              </div>
            </section>
          </section>
        </section>
      </DialogContent>
    </Dialog>
  );
};

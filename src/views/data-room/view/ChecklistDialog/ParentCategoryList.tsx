import { List, ListItemButton } from "@mui/material";
import { CompleteCategory } from "prisma/zod";
import { cn } from "~/v2/lib/utils";

const OverallCategory = "Overall";

interface ParentCategoryListProps {
  categories?: CompleteCategory[];
  selectedCategory?: CompleteCategory | null;
  setSelectedCategory: (category: CompleteCategory | null) => void;
}

export const ParentCategoryList = ({
  categories,
  selectedCategory,
  setSelectedCategory,
}: ParentCategoryListProps) => {
  return (
    <nav className="flex flex-col gap-0.5 w-1/4 px-0.5 !max-h-[500px] overflow-y-auto bg-gray-100">
      <List sx={{ height: "500px", overflowY: "auto" }}>
        <ListItemButton
          key={OverallCategory}
          onClick={() => setSelectedCategory(null)}
          className={cn("font-semibold", !selectedCategory && "!bg-gray-200")}
        >
          Overall
        </ListItemButton>
        {categories?.map((category) => {
          return (
            <ListItemButton
              key={category.id}
              onClick={() => setSelectedCategory(category)}
              className={cn(
                "font-semibold",
                selectedCategory?.id === category.id && "!bg-gray-200",
              )}
            >
              {category.name}
            </ListItemButton>
          );
        })}
      </List>
    </nav>
  );
};

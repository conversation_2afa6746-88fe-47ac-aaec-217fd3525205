"use client";

import { useCallback, useMemo, useState, useEffect, useRef } from "react";
import { api } from "~/trpc/react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "~/v2/components/ui/Dialog";
import { Checkbox } from "~/v2/components/ui/Checkbox";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";
import { Badge } from "~/v2/components/ui/Badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/v2/components/ui/Tooltip";
import { toast } from "src/components/snackbar";
import { Input } from "~/v2/components";
import { Fund } from "@prisma/client";
import { X } from "lucide-react";

type FundState = "checked" | "unchecked" | "partial";

interface ApplyFundsModalProps {
  selectedDocuments: Array<{
    id: string;
    name: string;
    funds: Array<{
      id: string;
      name: string;
      color: string;
    }>;
  }>;
  onClose: () => void;
}

export function ApplyFundsModal({
  selectedDocuments,
  onClose,
}: ApplyFundsModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [fundStates, setFundStates] = useState<Record<string, FundState>>({});
  const [initialFundStates, setInitialFundStates] = useState<
    Record<string, FundState>
  >({});
  const [searchQuery, setSearchQuery] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const hasInitialized = useRef(false);

  // Get all available funds
  const { data: allFunds, isLoading: isLoadingFunds } =
    api.fund.getAllFunds.useQuery();

  // Initialize fund states based on selected documents
  const initializeFundStates = useCallback(() => {
    if (!allFunds || hasInitialized.current) return;

    const states: Record<string, FundState> = {};

    allFunds.forEach((fund) => {
      const documentsWithFund = selectedDocuments.filter((doc) =>
        doc.funds?.some((f) => f.id === fund.id),
      ).length;

      const totalDocuments = selectedDocuments.length;

      if (documentsWithFund === 0) {
        states[fund.id] = "unchecked";
      } else if (documentsWithFund === totalDocuments) {
        states[fund.id] = "checked";
      } else {
        states[fund.id] = "partial";
      }
    });

    // Set initial states (what the funds actually are on the documents)
    setInitialFundStates(states);
    // Set current states (what the user sees and can modify)
    setFundStates(states);

    // Mark as initialized
    hasInitialized.current = true;
  }, [allFunds, selectedDocuments]);

  // Initialize states when data becomes available - only run once when modal opens
  useEffect(() => {
    if (isOpen && allFunds) {
      initializeFundStates();
    }
  }, [isOpen, allFunds, initializeFundStates]);

  // Handle fund selection change
  const handleFundChange = (fundId: string, checked: boolean) => {
    setFundStates((prev) => ({
      ...prev,
      [fundId]: checked ? "checked" : "unchecked",
    }));
  };

  // Calculate changes between current and initial states
  const getChanges = useMemo(() => {
    const changes = {
      fundsToApply: [] as Array<{ name: string; backgroundColor: string }>,
      fundsToRemove: [] as Array<{ name: string; backgroundColor: string }>,
    };

    Object.entries(fundStates).forEach(([fundId, currentState]) => {
      const initialState = initialFundStates[fundId];

      if (initialState === currentState) return; // No change

      const fund = allFunds?.find((f) => f.id === fundId);
      if (!fund) return;

      if (currentState === "checked" && initialState !== "checked") {
        changes.fundsToApply.push({
          name: fund.name,
          backgroundColor: fund.color || "#000000",
        });
      } else if (currentState === "unchecked" && initialState !== "unchecked") {
        changes.fundsToRemove.push({
          name: fund.name,
          backgroundColor: fund.color || "#000000",
        });
      }
    });

    return changes;
  }, [fundStates, initialFundStates, allFunds]);

  // Apply funds to selected documents
  const applyFundsMutation = api.document.update.useMutation({
    onSuccess: () => {
      toast.success("Funds applied successfully");
      setIsOpen(false);
      setShowConfirmation(false);
      onClose();
    },
    onError: (error) => {
      toast.error(`Failed to apply funds: ${error.message}`);
    },
  });

  const handleApplyFunds = () => {
    if (selectedDocuments.length === 0) {
      toast.error("No documents selected");
      return;
    }

    // Calculate which funds to add/remove for each document
    const documentsToUpdate = selectedDocuments.map((doc) => {
      const currentDocFunds = doc.funds || [];
      const currentDocFundIds = currentDocFunds.map((fund) => fund.id);

      // Start with existing funds
      let finalFundIds = [...currentDocFundIds];

      // Process each fund based on its state change
      Object.entries(fundStates).forEach(([fundId, currentState]) => {
        const initialState = initialFundStates[fundId];

        // Skip if no change
        if (initialState === currentState) return;

        const hasFund = currentDocFundIds.includes(fundId);

        if (currentState === "checked" && !hasFund) {
          // Add fund if it's now checked and document doesn't have it
          finalFundIds.push(fundId);
        } else if (currentState === "unchecked" && hasFund) {
          // Remove fund if it's now unchecked and document has it
          finalFundIds = finalFundIds.filter((id) => id !== fundId);
        }
        // For "partial" state, we don't change anything - keep existing state
      });

      return {
        id: doc.id,
        funds: finalFundIds,
      };
    });

    applyFundsMutation.mutate({
      documents: documentsToUpdate,
    });
  };

  // Filter funds based on search query
  const filteredFunds = useMemo(() => {
    if (!allFunds) return [];
    if (!searchQuery.trim()) return allFunds;

    const query = searchQuery.toLowerCase().trim();
    return allFunds.filter(
      (fund) =>
        fund.name.toLowerCase().includes(query) ||
        (fund.description && fund.description.toLowerCase().includes(query)),
    );
  }, [allFunds, searchQuery]);

  useEffect(() => {
    if (isOpen) {
      return;
    }
    setFundStates({});
    setInitialFundStates({});
    setShowConfirmation(false);
    hasInitialized.current = false;
  }, [isOpen]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <DialogTrigger asChild>
          <Button variant="default" disabled={selectedDocuments.length === 0}>
            Apply Funds
          </Button>
        </DialogTrigger>
        <DialogContent
          showCloseButton={false}
          className="w-[550px] h-[400px] p-2 flex flex-col"
        >
          <div className="flex flex-col h-full">
            <div className="flex flex-row items-center justify-between">
              <DialogTitle className="text-xl font-medium">
                Apply Funds
              </DialogTitle>
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  onClick={() => setShowConfirmation(false)}
                  className="w-8 h-8 p-0 relative -right-2 hover:bg-transparent"
                >
                  <X style={{ width: 24, height: 24 }} />
                </Button>
              </DialogClose>
            </div>
            <DialogDescription className="text-sm text-gray-500">
              Funds will be applied across all selected documents and override
              existing funds.
            </DialogDescription>

            {isLoadingFunds ? (
              <div className="flex items-center justify-center flex-1">
                <Typography>Loading funds...</Typography>
              </div>
            ) : (
              <>
                <div className="pb-2 pt-3">
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Search funds"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto min-h-0">
                  {filteredFunds && filteredFunds.length > 0 ? (
                    filteredFunds.map((fund) => {
                      const state = fundStates[fund.id] || "unchecked";
                      const isChecked = state === "checked";

                      return (
                        <div
                          key={fund.id}
                          className="flex items-center space-x-2 p-1 rounded hover:bg-gray-50"
                        >
                          <Checkbox
                            checked={
                              state === "partial" ? "indeterminate" : isChecked
                            }
                            onCheckedChange={(checked) => {
                              // When indeterminate, clicking should uncheck
                              if (state === "partial") {
                                handleFundChange(fund.id, false);
                              } else {
                                // For regular checked/unchecked states, use the checked parameter
                                handleFundChange(fund.id, checked as boolean);
                              }
                            }}
                            className="flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              {fund.description ? (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Badge
                                      className="text-xs"
                                      style={{
                                        backgroundColor: fund.color || "#000000",
                                      }}
                                    >
                                      {fund.name}
                                    </Badge>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{fund.description}</p>
                                  </TooltipContent>
                                </Tooltip>
                              ) : (
                                <Badge
                                  className="text-xs"
                                  style={{
                                    backgroundColor: fund.color || "#000000",
                                  }}
                                >
                                  {fund.name}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center justify-center py-8">
                      <Typography variant="caption">
                        {searchQuery.trim()
                          ? "No funds found matching your search"
                          : "No funds available"}
                      </Typography>
                    </div>
                  )}
                </div>

                <div className="flex items-center mt-2 flex-shrink-0">
                  <div className="flex flex-1">
                    <Button
                      variant="outline"
                      onClick={() => {
                        const clearedStates: Record<string, FundState> = {};
                        allFunds?.forEach((fund) => {
                          clearedStates[fund.id] = "unchecked";
                        });
                        setFundStates(clearedStates);
                      }}
                      disabled={!allFunds || allFunds.length === 0}
                    >
                      Clear
                    </Button>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      onClick={() => setIsOpen(false)}
                      disabled={applyFundsMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => setShowConfirmation(true)}
                      disabled={applyFundsMutation.isPending}
                    >
                      {applyFundsMutation.isPending ? "Applying..." : "Apply"}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent
          className="w-[500px] p-2 flex flex-col gap-0"
          showCloseButton={false}
        >
          <div className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl font-medium">
              Confirm Changes
            </DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                onClick={() => setShowConfirmation(false)}
                className="w-8 h-8 p-0 relative -right-2 hover:bg-transparent"
              >
                <X style={{ width: 24, height: 24 }} />
              </Button>
            </DialogClose>
          </div>
          <DialogDescription className="text-sm text-gray-500 mb-3">
            Review the changes that will be made to the selected documents:
          </DialogDescription>

          <div className="space-y-2">
            {getChanges.fundsToApply.length > 0 && (
              <div>
                <Typography variant="h5" className="mb-2">
                  Applying {getChanges.fundsToApply.length}
                </Typography>
                <div className="flex flex-wrap gap-1">
                  {getChanges.fundsToApply.map((fund) => (
                    <Badge
                      key={fund.name}
                      variant="secondary"
                      className="text-xs"
                      style={{ backgroundColor: fund.backgroundColor }}
                    >
                      {fund.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {getChanges.fundsToRemove.length > 0 && (
              <div>
                <Typography variant="h5" className="mb-2">
                  Removing {getChanges.fundsToRemove.length}
                </Typography>
                <div className="flex flex-wrap gap-1">
                  {getChanges.fundsToRemove.map((fund) => (
                    <Badge
                      key={fund.name}
                      variant="destructive"
                      className="text-xs"
                      style={{ backgroundColor: fund.backgroundColor }}
                    >
                      {fund.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {getChanges.fundsToApply.length === 0 &&
              getChanges.fundsToRemove.length === 0 && (
                <Typography variant="body" className="text-gray-500">
                  No changes detected. All funds will remain unchanged.
                </Typography>
              )}
          </div>

          <div className="flex items-center justify-end mt-6 gap-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={applyFundsMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              onClick={handleApplyFunds}
              disabled={applyFundsMutation.isPending}
            >
              {applyFundsMutation.isPending ? "Applying..." : "Confirm Changes"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

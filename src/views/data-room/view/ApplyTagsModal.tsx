"use client";

import { use<PERSON><PERSON>back, useMemo, useState, useEffect, useRef } from "react";
import { api } from "~/trpc/react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "~/v2/components/ui/Dialog";
import { Checkbox } from "~/v2/components/ui/Checkbox";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";
import { Badge } from "~/v2/components/ui/Badge";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/v2/components/ui/Tooltip";
import { toast } from "src/components/snackbar";
import { Input } from "~/v2/components";
import { Tag } from "@prisma/client";
import { X } from "lucide-react";

type TagState = "checked" | "unchecked" | "partial";

interface ApplyTagsModalProps {
  selectedDocuments: Array<{
    id: string;
    name: string;
    tags: Array<{
      id: string;
      name: string;
      color: string;
    }>;
  }>;
  onClose: () => void;
}

export function ApplyTagsModal({
  selectedDocuments,
  onClose,
}: ApplyTagsModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tagStates, setTagStates] = useState<Record<string, TagState>>({});
  const [initialTagStates, setInitialTagStates] = useState<
    Record<string, TagState>
  >({});
  const [searchQuery, setSearchQuery] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const hasInitialized = useRef(false);

  // Get all available tags
  const { data: allTags, isLoading: isLoadingTags } =
    api.tag.getAllTags.useQuery();

  // Initialize tag states based on selected documents
  const initializeTagStates = useCallback(() => {
    if (!allTags || hasInitialized.current) return;

    const states: Record<string, TagState> = {};

    allTags.forEach((tag) => {
      const documentsWithTag = selectedDocuments.filter((doc) =>
        doc.tags?.some((t) => t.id === tag.id),
      ).length;

      const totalDocuments = selectedDocuments.length;

      if (documentsWithTag === 0) {
        states[tag.id] = "unchecked";
      } else if (documentsWithTag === totalDocuments) {
        states[tag.id] = "checked";
      } else {
        states[tag.id] = "partial";
      }
    });

    // Set initial states (what the tags actually are on the documents)
    setInitialTagStates(states);
    // Set current states (what the user sees and can modify)
    setTagStates(states);

    // Mark as initialized
    hasInitialized.current = true;
  }, [allTags, selectedDocuments]);

  // Initialize states when data becomes available - only run once when modal opens
  useEffect(() => {
    if (isOpen && allTags) {
      initializeTagStates();
    }
  }, [isOpen, allTags, initializeTagStates]);

  // Handle tag selection change
  const handleTagChange = (tagId: string, checked: boolean) => {
    setTagStates((prev) => ({
      ...prev,
      [tagId]: checked ? "checked" : "unchecked",
    }));
  };

  // Calculate changes between current and initial states
  const getChanges = useMemo(() => {
    const changes = {
      tagsToApply: [] as Array<{ name: string; backgroundColor: string }>,
      tagsToRemove: [] as Array<{ name: string; backgroundColor: string }>,
    };

    Object.entries(tagStates).forEach(([tagId, currentState]) => {
      const initialState = initialTagStates[tagId];

      if (initialState === currentState) return; // No change

      const tag = allTags?.find((t) => t.id === tagId);
      if (!tag) return;

      if (currentState === "checked" && initialState !== "checked") {
        changes.tagsToApply.push({
          name: tag.name,
          backgroundColor: tag.color,
        });
      } else if (currentState === "unchecked" && initialState !== "unchecked") {
        changes.tagsToRemove.push({
          name: tag.name,
          backgroundColor: tag.color,
        });
      }
    });

    return changes;
  }, [tagStates, initialTagStates, allTags]);

  // Apply tags to selected documents
  const applyTagsMutation = api.document.update.useMutation({
    onSuccess: () => {
      toast.success("Tags applied successfully");
      setIsOpen(false);
      setShowConfirmation(false);
      onClose();
    },
    onError: (error) => {
      toast.error(`Failed to apply tags: ${error.message}`);
    },
  });

  const handleApplyTags = () => {
    if (selectedDocuments.length === 0) {
      toast.error("No documents selected");
      return;
    }

    // Calculate which tags to add/remove for each document
    const documentsToUpdate = selectedDocuments.map((doc) => {
      const currentDocTags = doc.tags || [];
      const currentDocTagIds = currentDocTags.map((tag) => tag.id);

      // Start with existing tags
      let finalTagIds = [...currentDocTagIds];

      // Process each tag based on its state change
      Object.entries(tagStates).forEach(([tagId, currentState]) => {
        const initialState = initialTagStates[tagId];

        // Skip if no change
        if (initialState === currentState) return;

        const hasTag = currentDocTagIds.includes(tagId);

        if (currentState === "checked" && !hasTag) {
          // Add tag if it's now checked and document doesn't have it
          finalTagIds.push(tagId);
        } else if (currentState === "unchecked" && hasTag) {
          // Remove tag if it's now unchecked and document has it
          finalTagIds = finalTagIds.filter((id) => id !== tagId);
        }
        // For "partial" state, we don't change anything - keep existing state
      });

      return {
        id: doc.id,
        tags: finalTagIds,
      };
    });

    applyTagsMutation.mutate({
      documents: documentsToUpdate,
    });
  };

  // Filter tags based on search query
  const filteredTags = useMemo(() => {
    if (!allTags) return [];
    if (!searchQuery.trim()) return allTags;

    const query = searchQuery.toLowerCase().trim();
    return allTags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(query) ||
        (tag.summary && tag.summary.toLowerCase().includes(query)),
    );
  }, [allTags, searchQuery]);

  useEffect(() => {
    if (isOpen) {
      return;
    }
    setTagStates({});
    setInitialTagStates({});
    setShowConfirmation(false);
    hasInitialized.current = false;
  }, [isOpen]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <DialogTrigger asChild>
          <Button variant="default" disabled={selectedDocuments.length === 0}>
            Apply Tags
          </Button>
        </DialogTrigger>
        <DialogContent
          showCloseButton={false}
          className="w-[550px] h-[400px] p-2 flex flex-col"
        >
          <div className="flex flex-col h-full">
            <div className="flex flex-row items-center justify-between">
              <DialogTitle className="text-xl font-medium">
                Apply Tags
              </DialogTitle>
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  onClick={() => setShowConfirmation(false)}
                  className="w-8 h-8 p-0 relative -right-2 hover:bg-transparent"
                >
                  <X style={{ width: 24, height: 24 }} />
                </Button>
              </DialogClose>
            </div>
            <DialogDescription className="text-sm text-gray-500">
              Tags will be applied across all selected documents and override
              existing tags.
            </DialogDescription>

            {isLoadingTags ? (
              <div className="flex items-center justify-center flex-1">
                <Typography>Loading tags...</Typography>
              </div>
            ) : (
              <>
                <div className="pb-2 pt-3">
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Search tags"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto min-h-0">
                  {filteredTags && filteredTags.length > 0 ? (
                    filteredTags.map((tag) => {
                      const state = tagStates[tag.id] || "unchecked";
                      const isChecked = state === "checked";

                      return (
                        <div
                          key={tag.id}
                          className="flex items-center space-x-2 p-1 rounded hover:bg-gray-50"
                        >
                          <Checkbox
                            checked={
                              state === "partial" ? "indeterminate" : isChecked
                            }
                            onCheckedChange={(checked) => {
                              // When indeterminate, clicking should uncheck
                              if (state === "partial") {
                                handleTagChange(tag.id, false);
                              } else {
                                // For regular checked/unchecked states, use the checked parameter
                                handleTagChange(tag.id, checked as boolean);
                              }
                            }}
                            className="flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              {tag.summary ? (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Badge
                                      className="text-xs"
                                      style={{
                                        backgroundColor: tag.color,
                                      }}
                                    >
                                      {tag.name}
                                    </Badge>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{tag.summary}</p>
                                  </TooltipContent>
                                </Tooltip>
                              ) : (
                                <Badge
                                  className="text-xs"
                                  style={{
                                    backgroundColor: tag.color,
                                  }}
                                >
                                  {tag.name}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center justify-center py-8">
                      <Typography variant="caption">
                        {searchQuery.trim()
                          ? "No tags found matching your search"
                          : "No tags available"}
                      </Typography>
                    </div>
                  )}
                </div>

                <div className="flex items-center mt-2 flex-shrink-0">
                  <div className="flex flex-1">
                    <Button
                      variant="outline"
                      onClick={() => {
                        const clearedStates: Record<string, TagState> = {};
                        allTags?.forEach((tag) => {
                          clearedStates[tag.id] = "unchecked";
                        });
                        setTagStates(clearedStates);
                      }}
                      disabled={!allTags || allTags.length === 0}
                    >
                      Clear
                    </Button>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      onClick={() => setIsOpen(false)}
                      disabled={applyTagsMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => setShowConfirmation(true)}
                      disabled={applyTagsMutation.isPending}
                    >
                      {applyTagsMutation.isPending ? "Applying..." : "Apply"}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent
          className="w-[500px] p-2 flex flex-col gap-0"
          showCloseButton={false}
        >
          <div className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl font-medium">
              Confirm Changes
            </DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                onClick={() => setShowConfirmation(false)}
                className="w-8 h-8 p-0 relative -right-2 hover:bg-transparent"
              >
                <X style={{ width: 24, height: 24 }} />
              </Button>
            </DialogClose>
          </div>
          <DialogDescription className="text-sm text-gray-500 mb-3">
            Review the changes that will be made to the selected documents:
          </DialogDescription>

          <div className="space-y-2">
            {getChanges.tagsToApply.length > 0 && (
              <div>
                <Typography variant="h5" className="mb-2">
                  Applying {getChanges.tagsToApply.length}
                </Typography>
                <div className="flex flex-wrap gap-1">
                  {getChanges.tagsToApply.map((tag) => (
                    <Badge
                      key={tag.name}
                      variant="secondary"
                      className="text-xs"
                      style={{ backgroundColor: tag.backgroundColor }}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {getChanges.tagsToRemove.length > 0 && (
              <div>
                <Typography variant="h5" className="mb-2">
                  Removing {getChanges.tagsToRemove.length}
                </Typography>
                <div className="flex flex-wrap gap-1">
                  {getChanges.tagsToRemove.map((tag) => (
                    <Badge
                      key={tag.name}
                      variant="destructive"
                      className="text-xs"
                      style={{ backgroundColor: tag.backgroundColor }}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {getChanges.tagsToApply.length === 0 &&
              getChanges.tagsToRemove.length === 0 && (
                <Typography variant="body" className="text-gray-500">
                  No changes detected. All tags will remain unchanged.
                </Typography>
              )}
          </div>

          <div className="flex items-center justify-end mt-6 gap-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={applyTagsMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              onClick={handleApplyTags}
              disabled={applyTagsMutation.isPending}
            >
              {applyTagsMutation.isPending ? "Applying..." : "Confirm Changes"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

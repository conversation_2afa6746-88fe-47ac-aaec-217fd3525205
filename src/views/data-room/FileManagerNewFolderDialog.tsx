import type { DialogProps } from "@mui/material/Dialog";

import { useCallback, useEffect, useState } from "react";

import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";

import { useAuth } from "@clerk/nextjs";
import { LinearProgress } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import axios from "axios";
import { Iconify } from "src/components/iconify";
import { type FileWithPath, Upload } from "src/components/upload";
import { toast } from "~/components/snackbar";
import { api } from "~/trpc/react";
import { getDocumentType } from "~/utils/document";

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  orgId: string;
  orgSlug: string;
  title?: string;
  folderName?: string;
  onClose: () => void;
  onCreate?: () => void;
  onUpdate?: () => void;
  onChangeFolderName?: (event: React.ChangeEvent<HTMLInputElement>) => void;
};

const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};

type ToastToFileNameType = { toastId: number; fileName: string };

export function FileManagerNewFolderDialog({
  open,
  onClose,
  orgId,
  orgSlug,
  onCreate,
  onUpdate,
  folderName,
  onChangeFolderName,
  title = "Upload files",
  ...other
}: Props) {
  const utils = api.useUtils();
  const [files, setFiles] = useState<(FileWithPath | string)[]>([]);
  const { isLoaded, userId } = useAuth();
  const theme = useTheme();

  const { data: user } = api.user.getByClerkId.useQuery(
    { clerkId: userId },
    { enabled: !!userId },
  );

  const presignedUrls = api.document.generatePresignedUrls.useMutation({});
  const createDocument = api.document.create.useMutation({});

  useEffect(() => {
    if (!open) {
      setFiles([]);
    }
  }, [open]);

  const handleDrop = useCallback(
    (acceptedFiles: FileWithPath[]) => {
      setFiles([...files, ...acceptedFiles]);
    },
    [files],
  );

  const handleUpload = () => {
    const toastId = toast.loading("Preparing secure upload", {
      duration: 1000,
    });

    const filesToUpload = files.map((file) => {
      const f = file as FileWithPath;

      return { name: f.path ?? f.name, contentType: f.type };
    });

    presignedUrls.mutateAsync(
      filesToUpload,

      {
        onSuccess: async (data) => {
          toast.success("Secure upload initiated", {
            id: toastId,
            duration: 1000,
          });

          data.forEach(async (d) => {
            // toast.loading(`${d.inputFile.name}`, {
            //   id: d.inputFile.name,
            //   duration: 1000,
            // });

            try {
              const file = (files as FileWithPath[]).find(
                (f: FileWithPath) => `${f.path}` === d.inputFile.name,
              );

              if (file) {
                console.log("File", file);
                const fileName = file.path || file.name;
                const fileData = Buffer.from(await file?.arrayBuffer());

                const s3response = await axios.put(d.signedUrl, fileData, {
                  onUploadProgress: (progressEvent) => {
                    toast.loading(
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "start",
                          background: theme.vars.palette.background.default,
                          color: theme.vars.palette.text.primary,
                          width: "100%",
                          padding: "5px",
                          gap: "10px",
                        }}
                      >
                        <div style={{ width: "100%" }}>
                          Uploading {file.name}
                        </div>
                        <div style={{ width: "100%" }}>
                          <LinearProgress
                            variant="determinate"
                            value={Number(
                              Math.round(
                                (progressEvent.loaded / file.size) * 100,
                              ),
                            )}
                          />
                        </div>
                      </div>,
                      {
                        id: fileName,
                        duration: 1000,
                        style: {
                          color: theme.vars.palette.text.primary,
                          backgroundColor:
                            theme.vars.palette.background.default,
                          fontSize: "14px",
                        },
                      },
                    );
                  },
                  headers: { "Content-Type": d.inputFile.contentType },
                });

                console.log("Uploaded to S3", s3response);

                toast.success(`Uploaded ${file.name}`, {
                  id: fileName,
                  duration: 1000,
                });

                await createDocument.mutateAsync(
                  {
                    name: fileName,
                    url: d.signedUrl,
                    type: getDocumentType(d.inputFile.contentType),
                    size: file?.size || 0,
                    createdById: user?.id || "",
                  },
                  {
                    onSuccess: (data) => { },
                    onError: (error) => {
                      console.error(`Error creating document ${fileName}`, {
                        id: fileName,
                      });
                    },
                  },
                );
              }
            } catch (error) {
              toast.error(`Error uploading ${d.inputFile.name}`, {
                id: d.inputFile.name,
              });
              throw new Error(`Error uploading document: ${error}`);
            }
          });
        },
        onError: (error) => {
          console.error("Error generating presigned URL", error);
          toast.error(`Error initiating upload`);
        },
      },
    );

    onClose();
  };

  const handleRemoveFile = (inputFile: File | string) => {
    const filtered = files.filter((file) => file !== inputFile);
    setFiles(filtered);
  };

  const handleRemoveAllFiles = () => {
    setFiles([]);
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={{ p: (theme) => theme.spacing(3, 3, 2, 3) }}>
        {" "}
        {title}{" "}
      </DialogTitle>

      <DialogContent dividers sx={{ pt: 1, pb: 0, border: "none" }}>
        {(onCreate || onUpdate) && (
          <TextField
            fullWidth
            label="Folder name"
            value={folderName}
            onChange={onChangeFolderName}
            sx={{ mb: 3 }}
          />
        )}

        <Upload
          multiple
          value={files}
          onDrop={handleDrop}
          onRemove={handleRemoveFile}
        />
      </DialogContent>

      <DialogActions>
        <Button
          variant="contained"
          startIcon={<Iconify icon="eva:cloud-upload-fill" />}
          onClick={handleUpload}
        >
          Upload
        </Button>

        {!!files.length && (
          <Button
            variant="outlined"
            color="inherit"
            onClick={handleRemoveAllFiles}
          >
            Remove all
          </Button>
        )}

        {(onCreate || onUpdate) && (
          <Stack direction="row" justifyContent="flex-end" flexGrow={1}>
            <Button variant="soft" onClick={onCreate || onUpdate}>
              {onUpdate ? "Save" : "Create"}
            </Button>
          </Stack>
        )}
      </DialogActions>
    </Dialog>
  );
}

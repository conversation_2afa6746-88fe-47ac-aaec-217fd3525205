import { Stack } from "@mui/material";
import { Typography } from "~/v2/components";
import { type UserActivityType } from "@prisma/client";
import { type JsonValue } from "@prisma/client/runtime/library";
import { ActivityTimeline, ActivityTimelineItem } from "~/v2/components";

type ActivityEvent = {
    id: string;
    type: UserActivityType;
    memo: string | null;
    createdAt: Date;
    createdBy: {
        id: string;
        name: string | null;
        email: string;

    };
    metadata: JsonValue;
    question: {
        id: string;
        createdById: string;
        createdAt: Date;
        updatedAt: Date;
        index: number | null;
        type: any;
        answerTemplate: string | null;
        orgId: string;
        category: any;
        responseId: string | null;
        status: any;
    } | null;
    response: any | null;
};

const getActivityTitle = (type: UserActivityType) => {
    switch (type) {
        case "RESPONSE_UPDATED":
            return "Response Updated";
        case "RESPONSE_APPROVED":
            return "Response Approved";
        case "RESPONSE_DRAFTED":
            return "Status changed to Draft";
        case "RESPONSE_CREATED":
            return "Response Created";
        case "RESPONSE_FEEDBACK_CREATED":
            return "Feedback Provided";
        case "QUESTION_ASSIGNED":
            return "Collaborator Added";
        case "QUESTION_UNASSIGNED":
            return "Collaborator Removed";
        case "DOCUMENT_APPROVED":
            return "Document Approved";
        case "RE_GENERATE_ANSWER":
            return "Re-generate Answer";
        default:
            return "Activity";
    }
};

export const QuestionActivityTimeline = ({
    activities,
}: {
    activities: ActivityEvent[];
}) => {
    const data = activities
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .map((activity) => ({
            ...activity,
            timestamp: activity.createdAt,
        }));

    return (
        <ActivityTimeline data={data}>
            {(activity) => (
                <ActivityTimelineItem key={activity.id}>
                    <Stack width="100%">
                        <Typography variant="h5" className="text-sm">
                            {getActivityTitle(activity.type)}
                        </Typography>
                        <Typography variant="caption" className="text-xs">
                            By {activity.createdBy.name ?? activity.createdBy.email} {activity.createdAt.toLocaleString()}
                        </Typography>
                    </Stack>
                    {activity.memo && (
                        <Typography variant="body" className="text-sm">
                            {activity.memo}
                        </Typography>
                    )}
                </ActivityTimelineItem>
            )}
        </ActivityTimeline>
    );
}; 
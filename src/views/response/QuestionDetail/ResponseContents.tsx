import { Stack } from "@mui/material"
import { MarkdownWithCopy } from "~/components/virgil/OfficeAddIn/MarkdownWithCopy";
type Props = {
    responseText: string;
}
export const ResponseContents = ({
    responseText
}: Props) => {
    return (
        <Stack direction="column" sx={{ height: "calc(40vh)" }}>
            <Stack
                spacing={1.5}
                alignItems="flex-start"
                sx={{
                    typography: "body2",
                    border: "0.5px solid grey",
                    borderRadius: 1,
                    p: 2,
                    backgroundColor: "#f4f4f6",
                    height: "100%",
                }}
            >
                <div
                    style={{
                        height: "100%",
                        width: "100%",
                        overflow: "auto",
                    }}
                >
                    <MarkdownWithCopy markdown={responseText} />
                </div>
            </Stack>
        </Stack>
    )
}
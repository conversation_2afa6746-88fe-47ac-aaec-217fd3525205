import {
  Box,
  CircularProgress,
  Typography,
} from "@mui/material";
import { useCallback } from "react";
import { useUpdateResponse } from "./useUpdateResponse";
import Wysiwyg from "~/components/virgil/Wysiwyg/Wysiwyg";
import { useQuestionDetailContext } from "./QuestionDetailContext";
import { Button } from "~/v2/components/ui";

type EditorProps = {
  questionText: string;
  responseId: string;
  responseText: string;
};
const Editor = ({
  questionText,
  responseId,
  responseText,
}: EditorProps) => {
  const { editorStateRef, updateResponse, submitting } = useUpdateResponse();
  const { setMode } = useQuestionDetailContext();

  const handleSave = useCallback(() => {
    updateResponse(responseId);
  }, [responseId, updateResponse]);
  return (
    <>
      <Box
        sx={{ height: "calc(100vh - 55px - 50px - 70px)", overflow: "auto" }}
      >
        <Box
          sx={{
            border: "1px solid #ddd",
            backgroundColor: "#F4F4F6",
            borderRadius: 1,
            p: 0,
          }}
        >
          <Wysiwyg
            markdownString={responseText}
            editorStateRef={editorStateRef}
            editorConentClass="add-in markdown-body"
          />
        </Box>
      </Box>
      <Box
        sx={{
          mt: 2,
          position: "sticky",
          bottom: 0,
          p: 2,
          height: "70px",
          display: "flex",
          gap: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Button
          variant="default"
          color="primary"
          onClick={handleSave}
          disabled={submitting}
        >
          Save
        </Button>
        <Button
          variant="outline"
          color="secondary"
          onClick={() => {
            setMode("view");
          }}
          disabled={submitting}
        >
          Close
        </Button>
      </Box>
    </>
  );
};

export default Editor;

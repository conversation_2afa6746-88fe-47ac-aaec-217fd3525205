import { LexicalEditor } from "lexical";
import { useCallback, useRef, useState } from "react";
import { api } from "~/trpc/react";
import { $convertToMarkdownString } from "@lexical/markdown";
import { toast } from "~/components/snackbar";
import { PLAYGROUND_TRANSFORMERS } from "~/components/virgil/Wysiwyg/transformers/playgroundTransformers";
import { useQuestionDetailContext } from "./QuestionDetailContext";

export const useUpdateResponse = () => {
  const editorStateRef = useRef<LexicalEditor | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const updateResponseById = api.response.updateResponseById.useMutation();
  const { mode, setMode } = useQuestionDetailContext();

  const updateResponse = useCallback(
    (similarResponseId: string | null) => {
      if (submitting) return;
      editorStateRef.current?.getEditorState().read(() => {
        const markdown = $convertToMarkdownString(PLAYGROUND_TRANSFORMERS);
        setSubmitting(true);
        if (similarResponseId) {
          updateResponseById.mutate(
            {
              id: similarResponseId,
              responseText: markdown,
            },
            {
              onSuccess: () => {
                setSubmitting(false);
                setMode("view");
                toast.success("Response updated successfully");
              },
              onError: (error: any) => {
                setSubmitting(false);
                toast.error("Error updating response", error.message);
              },
            },
          );
        }
      });
    },
    [submitting, setMode],
  );

  return {
    submitting,
    editorStateRef,
    updateResponse,
    mode,
  };
};

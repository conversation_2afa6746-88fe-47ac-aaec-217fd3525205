import { ResponseStatus } from "@prisma/client";
import { useUpdateResponseStatus } from "./useUpdateResponseStatus";
import { Button } from "~/v2/components";

export const UpdateStatusButton = ({
    canonicalResponseId,
    responseStatus,
}: {
    canonicalResponseId: string;
    responseStatus: ResponseStatus;
}) => {
    const { approveResponse, switchToDraft, isLoading } = useUpdateResponseStatus({
        canonicalResponseId,
    });
    if (responseStatus === ResponseStatus.APPROVED) {
        return (
            <Button
                variant="outline"
                onClick={switchToDraft}
                disabled={isLoading}
            >
                Set as Draft
            </Button>
        )
    }

    return (
        <Button
            variant="default"
            onClick={approveResponse}
            disabled={isLoading}
        >
            Approve
        </Button>
    )
}
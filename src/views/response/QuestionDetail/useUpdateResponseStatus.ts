import { ResponseStatus } from "@prisma/client";
import { useCallback } from "react";
import { toast } from "~/components/snackbar";
import { api } from "~/trpc/react";

export const useUpdateResponseStatus = ({
  canonicalResponseId,
}: {
  canonicalResponseId: string;
}) => {
  const mutation = api.response.updateResponseStatus.useMutation();

  const updateResponseStatus = useCallback(
    (status: ResponseStatus) => {
      mutation.mutate(
        {
          id: canonicalResponseId,
          status,
        },
        {
          onSuccess: () => {
            toast.success("Response status updated");
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    },
    [mutation, canonicalResponseId],
  );

  const isLoading = mutation.isPending;
  const approveResponse = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      updateResponseStatus(ResponseStatus.APPROVED);
    },
    [updateResponseStatus],
  );
  const switchToDraft = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      updateResponseStatus(ResponseStatus.DRAFT);
    },
    [updateResponseStatus],
  );

  return {
    approveResponse,
    switchToDraft,
    isLoading,
  };
};

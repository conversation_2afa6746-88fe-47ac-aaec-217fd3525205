import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the mode type
export type QuestionDetailMode = 'view' | 'edit';

// Define the context interface
interface QuestionDetailContextType {
    mode: QuestionDetailMode;
    setMode: (mode: QuestionDetailMode) => void;
    toggleMode: () => void;
    isViewMode: boolean;
    isEditMode: boolean;
}

// Create the context
const QuestionDetailContext = createContext<QuestionDetailContextType | undefined>(undefined);

// Provider props interface
interface QuestionDetailProviderProps {
    children: ReactNode;
    initialMode?: QuestionDetailMode;
}

// Provider component
export const QuestionDetailProvider: React.FC<QuestionDetailProviderProps> = ({
    children,
    initialMode = 'view',
}) => {
    const [mode, setMode] = useState<QuestionDetailMode>(initialMode);

    const toggleMode = () => {
        setMode(prevMode => prevMode === 'view' ? 'edit' : 'view');
    };

    const isViewMode = mode === 'view';
    const isEditMode = mode === 'edit';

    const value: QuestionDetailContextType = {
        mode,
        setMode,
        toggleMode,
        isViewMode,
        isEditMode,
    };

    return (
        <QuestionDetailContext.Provider value={value}>
            {children}
        </QuestionDetailContext.Provider>
    );
};

// Custom hook to use the context
export const useQuestionDetailContext = (): QuestionDetailContextType => {
    const context = useContext(QuestionDetailContext);
    if (context === undefined) {
        throw new Error('useQuestionDetailContext must be used within a QuestionDetailProvider');
    }
    return context;
};

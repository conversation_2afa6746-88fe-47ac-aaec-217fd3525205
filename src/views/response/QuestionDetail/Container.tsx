import { useGetQuestionWithFeedback } from "~/components/virgil/hooks/useGetQuestionWithFeedback";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import { QuestionDetail } from "./QuestionDetail";
import { QuestionDetailProvider } from "./QuestionDetailContext";
import { AnimateLogo2 } from "~/components/animate";
import { Button, Typography } from "~/v2/components";
import { useResponseLibraryContext } from "../context";
import { XIcon } from "lucide-react";

type Props = {
  questionId: string;
}
const QuestionDetailContainer: React.FC<Props> = ({ questionId }) => {
  const {
    questionWithFeedback,
    isLoading: isLoadingQuestion,
  }: {
    questionWithFeedback: DDQuestionWithFeedback | null | undefined;
    isLoading: boolean;
  } = useGetQuestionWithFeedback({ questionId });
  const filters = useResponseLibraryContext();
  if (isLoadingQuestion || !questionWithFeedback) {
    return (
      <div className="flex flex-col items-center justify-center h-screen w-full gap-4 relative">
        <AnimateLogo2 />
        <Typography variant="h5">Loading...</Typography>
        <Button
          variant="ghost"
          onClick={() => filters.setState({ selectedQuestionId: null, allowEdit: false })}
          className="p-0 absolute top-2 right-2"
        >
          <XIcon />
        </Button>
      </div>
    );
  }

  return (
    <QuestionDetailProvider>
      <QuestionDetail
        question={questionWithFeedback}
      />
    </QuestionDetailProvider>
  );
};

export default QuestionDetailContainer;

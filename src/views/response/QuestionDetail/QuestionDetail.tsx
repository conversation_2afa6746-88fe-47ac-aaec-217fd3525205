import parseQuestionData from "~/components/virgil/OfficeAddIn/IndexView/SingleDDQWithData/parseQuestionData";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import Editor from "./Editor";
import { useQuestionDetailContext } from "./QuestionDetailContext";
import { UserCollaboration } from "~/components/virgil/OfficeAddIn/UserCollaboration";
import { AssignedAvatarGroup } from "~/components/virgil/OfficeAddIn/UserCollaboration/AssignedAvatarGroup";
import { UpdateStatusButton } from "./UpdateStatusButtons";
import { QuestionActivityTimelineContainer } from "../QuestionActivityTimelineContainer";
import useRegenerateBtn from "~/components/virgil/hooks/useRegenerateBtn";
import React from "react";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import { ResponseDetails } from "~/components/virgil/Response/ResponseDetails";
import { Button, Typography } from "~/v2/components";
import { XIcon } from "lucide-react";

type Props = {
    question: DDQuestionWithFeedback;
}
export const QuestionDetail = ({ question }: Props) => {
    const { questionText, responseCanonicalId, responseId, responseReason, answerGenerationType, insufficientData, isAnswered, responseText, responseStatus, questionId, documentId } = parseQuestionData(question);
    const { regenerateBtn } = useRegenerateBtn({ questionId, documentId });
    const { mode, setMode } = useQuestionDetailContext();
    const context = useResponseLibraryContext();
    return (
        <div className="flex flex-col gap-4">
            <div className="flex justify-between items-start">
                <Typography variant="h6">{questionText}</Typography>
                <Button
                    variant="ghost"
                    className="ml-auto p-0"
                    onClick={() => context.setState({ selectedQuestionId: null, allowEdit: false })}
                    style={{
                        height: "24px",
                        width: "24px",
                    }}
                >
                    <XIcon />
                </Button>
            </div>
            {mode === 'edit' && (
                <div>
                    <Editor
                        questionText={questionText}
                        responseId={responseId}
                        responseText={responseText}
                    />
                </div>
            )}
            {mode === 'view' && (
                <>
                    <ResponseDetails
                        responseReason={responseReason}
                        responseStatus={responseStatus}
                        responseText={responseText}
                        answerGenerationType={answerGenerationType}
                        insufficientData={insufficientData}
                        isAnswered={isAnswered}
                        isGenerating={false}
                    />
                    {context.state.allowEdit && (
                        <React.Fragment>
                            <div className="flex gap-1 items-center flex-1">
                                {regenerateBtn}
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setMode("edit");
                                    }}
                                >
                                    Edit
                                </Button>
                                {responseStatus && <UpdateStatusButton responseStatus={responseStatus} canonicalResponseId={responseCanonicalId} />}
                            </div>
                            <div className="border-b border-gray-200" />
                        </React.Fragment>
                    )}
                    <div className="space-y-1">
                        <Typography variant="h6">Collaborators</Typography>
                        <div className="flex gap-1 items-end flex-1">
                            <UserCollaboration questionId={questionId} responseId={responseCanonicalId} />
                            <AssignedAvatarGroup questionId={questionId} />
                        </div>
                    </div>
                    <div className="border-b border-gray-200" />
                    <QuestionActivityTimelineContainer questionId={questionId} />
                </>
            )}
        </div>
    )
}
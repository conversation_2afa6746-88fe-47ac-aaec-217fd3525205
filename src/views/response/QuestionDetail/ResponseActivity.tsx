import { Box } from "@mui/material"
import { ResponseEditsTimelineContainer } from "../ResponseEditsTimelineContainer"

type Props = {
    responseId: string;
}

export const ResponseActivity = ({
    responseId
}: Props) => {
    return (
        <Box
            display="flex"
            alignItems="flex-start"
            flexDirection="column"
            justifyContent="flex-start"
            sx={{ p: 0.5, gap: 0.5, typography: "body2" }}
        >
            <ResponseEditsTimelineContainer responseId={responseId} />
        </Box>
    )
}
"use client";
import { GridRowSelectionModel } from '@mui/x-data-grid';
import React, { createContext, useContext } from 'react';
import { useSetState, type UseSetStateReturn } from "src/hooks/use-set-state";
import { ResponseStatus } from "@prisma/client";

export type IQuestionFiltersWithView = {
    search: string;
    type: string[];
    ddq: { name: string; id: string }[];
    tags: { name: string; id: string; color: string }[];
    funds: { name: string; id: string; color: string }[];
    responseStatus: ResponseStatus[];
    view: "list" | "grid";
    currentPage: number;
    pageSize: number;
    rowSelectionModel: GridRowSelectionModel;
    tab: "all" | "collaboration";
    selectedQuestionId: string | null;
    allowEdit: boolean;
};

const ResponseLibraryContext = createContext<UseSetStateReturn<IQuestionFiltersWithView> | undefined>(undefined);

export const ResponseLibraryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const filters = useSetState<IQuestionFiltersWithView>({
        search: "",
        type: [],
        ddq: [],
        tags: [],
        funds: [],
        responseStatus: [],
        view: "grid",
        currentPage: 0,
        pageSize: 10,
        rowSelectionModel: [],
        tab: "all",
        selectedQuestionId: null,
        allowEdit: false,
    });

    return (
        <ResponseLibraryContext.Provider value={filters}>
            {children}
        </ResponseLibraryContext.Provider>
    );
};

export const useResponseLibraryContext = () => {
    const context = useContext(ResponseLibraryContext);
    if (!context) {
        throw new Error("useResponseLibraryContext must be used within a ResponseLibraryFiltersProvider");
    }
    return context;
};
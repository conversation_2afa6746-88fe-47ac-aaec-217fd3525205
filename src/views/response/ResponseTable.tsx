import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/v2/components/ui/Tooltip";
import ResponseStatusLabel from "~/components/virgil/Response/ResponseStatusLabel";

import { type Tag, type TagEntityConnection } from "@prisma/client";

import {
  DataGridPremium,
  type GridColDef,
  useGridApiRef,
} from "@mui/x-data-grid-premium";
import { EmptyContent } from "~/components/empty-content";
import { Badge } from "~/v2/components/ui/Badge";
import { Typography } from "~/v2/components/ui/Typography";
import { type ResponseContentType } from "~/lib/types";
import { type GetQuestionsType } from "~/server/api/routers/question.common";
import { api } from "~/trpc/react";
import { getContrastColor } from "./QuestionDetails";
import { useResponseLibraryContext } from "./context/ResponseLibraryContext";
import { useEffect, useMemo } from "react";
import { getFilename } from "~/v2/lib/utils";

type Props = {
  items?: GetQuestionsType[];
  loading?: boolean;
  totalPages?: number;
  totalRowCount?: number;
  allowEdit?: boolean;
};

export default function ResponseTable({
  items,
  loading,
  totalRowCount,
  allowEdit = false,
}: Props) {
  const apiRef = useGridApiRef();
  const filters = useResponseLibraryContext();

  const tagEntityConnections = api.tag.getTagEntityConnections.useQuery(
    {
      questions: items?.map((q) => q.id) ?? [],
    },
    {
      enabled: !!items,
    },
  );

  const columns: GridColDef[] = useMemo(
    () => [
      {
        field: "id",
      },
      {
        field: "question",
        headerName: "Question",
        flex: 0.5,
        renderCell: (params) => {
          const questionContent = params.row.question.questionContents[0]
            ?.content as ResponseContentType;

          return (
            <div className="flex flex-col justify-center overflow-hidden h-full w-full">
              <Typography variant="h6" className="line-clamp-2 text-sm">
                {questionContent.text}
              </Typography>
            </div>
          );
        },
      },
      {
        field: "response",
        headerName: "Response",
        flex: 0.5,
        renderCell: (params) => {
          const responseContent = params.row.question.response
            ?.responseContents[0]?.content as ResponseContentType;

          return (
            <div className="flex flex-col justify-center overflow-hidden h-full w-full">
              <Typography
                variant="h6"
                className="line-clamp-2 text-sm font-normal"
              >
                {responseContent?.text || "No response"}
              </Typography>
            </div>
          );
        },
      },
      {
        field: "tags",
        headerName: "Tags",
        flex: 0.2,
        renderCell: (params) => {
          const question = params.row as GetQuestionsType;
          const tagEntityConnections = params.row
            .entityConnections as TagEntityConnection[];
          return (
            <>
              {question?.tags.map((t: Tag) => (
                <Tooltip key={t.id}>
                  <TooltipTrigger>
                    <Badge
                      variant="outline"
                      style={{
                        backgroundColor: t.color,
                        color: getContrastColor(t.color),
                      }}
                    >
                      {t.name}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    {tagEntityConnections?.find(
                      (c: TagEntityConnection) => c.tagId === t.id,
                    )?.connectionReason || "No connection reason"}
                  </TooltipContent>
                </Tooltip>
              ))}
              {question?.tags.length === 0 && (
                <Badge variant="secondary">No tags</Badge>
              )}
            </>
          );
        },
      },
      {
        field: "usedInDDQs",
        headerName: "Usage",
        flex: 0.15,
        renderCell: (params) => {
          const question = params.row.question;
          const documents = question?.response?.documents;
          const ddqCount = documents?.length ?? 0;

          if (ddqCount === 0) {
            return <span>0 DDQs</span>;
          }

          const ddqNames =
            documents?.map((doc: any) => getFilename(doc.document.name)).filter(Boolean) ??
            [];

          return (
            <Tooltip>
              <TooltipTrigger>
                {ddqCount} DDQ{ddqCount === 1 ? "" : "s"}
              </TooltipTrigger>
              <TooltipContent>
                {ddqNames.map((name: string, index: number) => (
                  <div key={index}>{name}</div>
                ))}
              </TooltipContent>
            </Tooltip>
          );
        },
      },
      {
        field: "status",
        headerName: "Status",
        flex: 0.15,
        renderCell: (params) => {
          const question = params.row.question;

          return question?.response?.status ? (
            <ResponseStatusLabel responseStatus={question.response.status} />
          ) : (
            <span className="text-gray-500">No status</span>
          );
        },
      },
      // ...(!!filters.state.search ? [{
      //   field: "score",
      //   headerName: "Trust Score",
      //   flex: 0.15,
      //   renderCell: (params) => {
      //     TODO: Add trust score
      //   },
      // }] : []),
    ],
    [filters.state.search],
  );

  // Set default selected row when component mounts
  useEffect(() => {
    if (filters.state.selectedQuestionId && items) {
      const selectedRowIndex = items.findIndex(
        (item) => item.id === filters.state.selectedQuestionId,
      );
      if (selectedRowIndex !== -1) {
        apiRef.current.setRowSelectionModel([filters.state.selectedQuestionId]);
      }
    }
  }, [filters.state.selectedQuestionId, items, apiRef]);

  return (
    <DataGridPremium
      columnHeaderHeight={38}
      apiRef={apiRef}
      disableColumnSorting
      disableColumnFilter
      disableAggregation
      disableRowGrouping
      disableRowSelectionOnClick={loading}
      // checkboxSelection
      // disableRowSelectionOnClick
      rows={items?.map((item) => ({
        id: item.id,
        question: item,
        tags: item.tags,
        entityConnections: tagEntityConnections.data?.filter((c) =>
          item.tags.some((t) => t.id === c.tagId),
        ),
        usedInDDQs: item.response?.documents.length,
        approvedBy: "",
        updatedAt: item.updatedAt,
      }))}
      columns={columns}
      loading={loading}
      initialState={{
        columns: {
          columnVisibilityModel: {
            id: false,
          },
        },
      }}
      pagination
      paginationMode="server"
      paginationModel={{
        page: filters.state.currentPage,
        pageSize: filters.state.pageSize,
      }}
      rowCount={totalRowCount}
      pageSizeOptions={[10, 25, 50, 100]}
      onPaginationModelChange={(model) => {
        // Prevent pagination changes while loading to avoid state conflicts
        if (loading) {
          return;
        }

        if (model.page !== filters.state.currentPage) {
          filters.setState({ currentPage: model.page });
        }
        if (model.pageSize !== filters.state.pageSize) {
          filters.setState({ pageSize: model.pageSize });
        }
      }}
      onRowClick={(params) => {
        // Prevent row selection during loading
        if (loading) {
          return;
        }

        if (filters.state.selectedQuestionId !== params.row.id) {
          filters.setState({ selectedQuestionId: params.row.id, allowEdit });
        } else {
          filters.setState({ selectedQuestionId: null, allowEdit: false });
        }
      }}
      sx={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        "--DataGrid-cellOffsetMultiplier": 0.1,
        minWidth: "0",
        border: "1px solid grey.200",
        backgroundColor: "white",
        // maxWidth: "100%",

        "& .MuiDataGrid-cell:focus": {
          outline: "none",
        },
        "& .MuiDataGrid-cell": {
          userSelect: "none",
          WebkitUserSelect: "none",
          MozUserSelect: "none",
          msUserSelect: "none",
        },
        "& .MuiDataGrid-columnHeader": {
          userSelect: "none",
          WebkitUserSelect: "none",
          MozUserSelect: "none",
          msUserSelect: "none",
          fontSize: "12px",
        },
        "& .MuiDataGrid-row": {
          userSelect: "none",
          WebkitUserSelect: "none",
          MozUserSelect: "none",
          msUserSelect: "none",
        },
      }}
      slots={{ noRowsOverlay: EmptyContent }}
      rowHeight={40}
    />
  );
}

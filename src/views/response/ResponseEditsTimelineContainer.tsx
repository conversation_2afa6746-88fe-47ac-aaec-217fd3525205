import { useResponseEdits } from "~/components/virgil/hooks/useResponseEdits";
import { ResponseEditTimeline } from "./ResponseEditTimeline";
import { Skeleton } from "@mui/material";
import { Typography } from "~/v2/components";

export const ResponseEditsTimelineContainer = ({ responseId }: { responseId: string }) => {
    const responseEdits = useResponseEdits(responseId);
    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                alignItems: "left",
                gap: 10,
                width: "100%",
                padding: 0,
                wordWrap: "break-word"
            }}
        >
            <Typography variant="h3">Activity</Typography>
            {responseEdits.isLoading ? (
                <Skeleton sx={{ height: 7 }} />
            ) : (
                <ResponseEditTimeline edits={responseEdits.data ?? []} />
            )}
        </div>
    )
}
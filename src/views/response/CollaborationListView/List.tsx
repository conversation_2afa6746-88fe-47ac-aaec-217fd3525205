import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { api } from "~/trpc/react";
import { QuestionDetails } from "../QuestionDetails";
import { UpdateStatusButton } from "../QuestionDetail/UpdateStatusButtons";
import { ResponseStatus } from "@prisma/client";

type Props = {
  questions: QuestionWithSearchType[];
};

export function AssignedQuestionList({
  questions,
}: Props) {
  const tagEntityConnections = api.tag.getTagEntityConnections.useQuery(
    {
      questions: questions.map((q) => q.id),
    },
    {
      enabled: !!questions,
    },
  );

  return (
    <>
      {questions
        .filter((q) => q.questionContents.length > 0)
        .map((question) => (
          <QuestionDetails
            key={question.id}
            question={question}
            tagEntityConnections={tagEntityConnections.data ?? []}
            allowEdit={true}
            actions={
              <UpdateStatusButton
                responseStatus={question?.response?.status ?? ResponseStatus.DRAFT}
                canonicalResponseId={question?.response?.id ?? ""}
              />
            }
          />
        ))}
    </>
  );
}

import Loading from "~/app/dashboard/loading";
import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { AssignedQuestionList } from "./List";

type Props = {
  items: QuestionWithSearchType[];
  loading: boolean;
};

export function AssignedQuestionsListContainer({
  items,
  loading,
}: Props) {

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="flex flex-col items-start space-y-2 w-full h-full">
      <div className="grid grid-cols-1 w-full overflow-auto max-h-full gap-2">
        <AssignedQuestionList
          questions={items}
        />
      </div>
    </div>
  );
}

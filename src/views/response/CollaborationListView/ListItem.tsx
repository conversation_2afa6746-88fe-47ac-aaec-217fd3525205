import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import Stack from "@mui/material/Stack";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import Highlighter from "react-highlight-words";
import ResponseStatusLabel from "~/components/virgil/Response/ResponseStatusLabel";
import { Iconify } from "~/components/iconify";
import { Label } from "~/components/label";
import { type ResponseContentType } from "~/lib/types";
import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { type GetTagEntityConnectionsType } from "~/server/api/routers/tag";
import { fDateTime } from "~/utils/format-time";
import { Button } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import { getContrastColor } from "../QuestionDetails";
import { useUpdateResponseStatus } from "../QuestionDetail/useUpdateResponseStatus";
import parseQuestionData from "~/components/virgil/OfficeAddIn/IndexView/SingleDDQWithData/parseQuestionData";
import { UpdateStatusButton } from "../QuestionDetail/UpdateStatusButtons";
import { copyToClipboardClickHandler } from "~/lib/copyToClipboard";

type Props = {
  question: QuestionWithSearchType;
  tagEntityConnections: GetTagEntityConnectionsType[];
  search?: string | undefined;
  accordion?: boolean;
  expanded?: string | null;
  setExpanded?: (expanded: string | null) => void;
  similarQuestionCount?: number;
};

export function AssignedQuestionListItem({
  question,
  tagEntityConnections,
  search = "",
  accordion = false,
}: Props) {
  const context = useResponseLibraryContext();
  const { approveResponse, switchToDraft, isLoading } = useUpdateResponseStatus({
    canonicalResponseId: question.response?.id ?? "",
  });

  const questionContent = question?.questionContents[0]
    ?.content as ResponseContentType;

  const responseContent = question?.response?.responseContents[0]
    ?.content as ResponseContentType;

  const renderResponseContents = (
    <>
      <Stack
        direction="row"
        justifyContent={accordion ? "space-between" : "flex-start"}
        width="100%"
        sx={{ pl: accordion ? 0 : 3, mt: accordion ? 0 : 2, pr: 2 }}
      >
        <Stack
          gap={1}
          alignItems="flex-start"
          sx={{
            width: accordion ? "100%" : "80%",
          }}
        >
          {questionContent.text && (
            <Typography
              variant={accordion ? "h6" : "subtitle2"}
              sx={{
                display: "-webkit-box",
                overflow: "hidden",
                WebkitBoxOrient: "vertical",
                WebkitLineClamp: accordion ? 2 : 1,
              }}
            >
              {accordion
                ? questionContent.text
                : questionContent.text.slice(0, 300)}
              {!accordion && questionContent.text.length > 300 && "..."}
            </Typography>
          )}
        </Stack>
      </Stack>
    </>
  );

  const renderResponseSnippet = (
    <>
      <Stack
        direction="row"
        sx={{
          pl: accordion ? 0 : 3,
          mt: 0,
          pr: accordion ? 0 : 2,
          height: accordion ? "100px" : "40px",
          width: accordion ? "95%" : "80%",
        }}
      >
        <Typography
          variant={accordion ? "body2" : "caption"}
          overflow={accordion ? "auto" : "hidden"}
          textOverflow="ellipsis"
          sx={{
            display: "-webkit-box",
            overflow: accordion ? "auto" : "hidden",
            WebkitBoxOrient: "vertical",
            WebkitLineClamp: 2,
          }}
        >
          <Highlighter
            highlightStyle={{
              backgroundColor: "yellow",
              fontWeight: "bold",
            }}
            searchWords={[search ?? ""]}
            autoEscape={true}
            textToHighlight={responseContent?.text ?? ""}
          />
        </Typography>
      </Stack>
    </>
  );

  const renderResponseDetails = (
    <>
      <Box
        display="flex"
        alignItems="flex-start"
        justifyContent="space-between"
        sx={{
          pl: accordion ? 0 : 3,
          mb: accordion ? 0 : 2,
          pr: accordion ? 0 : 2,
          pt: accordion ? 1 : 0,
          typography: "body2",
        }}
      >
        <Stack direction="row" alignItems="center" gap={0.5}>
          {question?.tags.map((t, i) => (
            <Tooltip
              key={i}
              title={
                tagEntityConnections.find((c) => c.tagId === t.id)
                  ?.connectionReason
              }
            >
              <Label
                key={t.id}
                variant="soft"
                sx={{
                  backgroundColor: t.color,
                  color: getContrastColor(t.color),
                }}
              >
                {t.name}
              </Label>
            </Tooltip>
          ))}
          {question?.tags.length === 0 && (
            <Label variant="soft" color="warning">
              No tags
            </Label>
          )}
          <div>|</div>
          {question?.response?.status && (
            <ResponseStatusLabel responseStatus={question?.response?.status} />
          )}
          <div>|</div>
          <Stack direction="row" alignItems="center" gap={0.5}>
            <Iconify icon="mdi:file-document-outline" />
            <Typography variant="body2">
              {question?.response?.documents.length &&
                question?.response?.documents.length > 0
                ? `Used in ${question?.response?.documents.length} DDQs`
                : "Not used"}
            </Typography>
          </Stack>
          <div>|</div>
          <Stack direction="row" alignItems="center" gap={0.5}>
            <Iconify icon="mdi:clock-outline" />
            <Typography variant="body2">{`Last updated ${fDateTime(question?.response?.updatedAt)}`}</Typography>
          </Stack>
        </Stack>
        <Box display="flex" alignItems="center" gap={1}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<Iconify icon="solar:copy-bold" width={16} />}
            size="small"
            onClick={copyToClipboardClickHandler(
              responseContent?.text ?? "",
              2000,
              true,
              "Copied response to clipboard",
            )}
            sx={{
              minWidth: "11em",
              display: "flex",
            }}
          >
            Copy Response
          </Button>
          {question?.response?.status && (
            <UpdateStatusButton responseStatus={question?.response?.status} canonicalResponseId={question?.response?.id ?? ""} />
          )}
        </Box>
      </Box>
    </>
  );

  return (
    <Card
      onClick={() => {
        context.setState({ selectedQuestionId: question.id });
      }}
      sx={{
        borderLeft:
          context.state.selectedQuestionId === question.id ? "3px solid" : "inherit",
        cursor: "pointer",
        height: "130px",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        padding: 0,
      }}
    >
      {renderResponseContents}
      {renderResponseSnippet}
      <Divider sx={{ borderStyle: "dashed" }} />

      {renderResponseDetails}
    </Card>
  );
}

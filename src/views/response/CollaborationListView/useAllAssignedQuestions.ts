import { api } from "~/trpc/react";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";

export const useAllAssignedQuestions = () => {
  const filters = useResponseLibraryContext();
  const query = api.question.getAllQuestionsAssignedToUser.useQuery({
    page: filters.state.currentPage + 1, // Convert from 0-based to 1-based
    pageSize: filters.state.pageSize,
  });

  return query;
};

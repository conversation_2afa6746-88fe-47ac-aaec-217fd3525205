import { QuestionCategory } from "@prisma/client";
import { api } from "~/trpc/react";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";

export const useAllQuestionWithSearch = () => {
  const filters = useResponseLibraryContext();
  const questions = api.question.getAllQuestions.useQuery({
    category: [
      QuestionCategory.OTHER,
      QuestionCategory.DATA_ASSURANCE,
      QuestionCategory.STANDARDS_AND_FRAMEWORKS,
      QuestionCategory.INVESTMENT_PROCESS,
    ],
    search: filters.state.search || undefined,
    ddq: filters.state.ddq.map((ddq) => ddq.id),
    tagIds: filters.state.tags.map((tag) => tag.id),
    fundIds: filters.state.funds.map((fund) => fund.id),
    responseStatus:
      filters.state.responseStatus.length > 0
        ? filters.state.responseStatus
        : undefined,
    page: filters.state.currentPage,
    pageSize: filters.state.pageSize,
  });

  return questions;
};

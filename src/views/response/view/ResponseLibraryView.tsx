"use client";

import { <PERSON>boardContent, DashboardHeader } from "~/layouts/Dashboard";
import { ResponseLibraryProvider } from "../context/ResponseLibraryContext";
import { AllQuestionsTabContent } from "./AllQuestionsTabContent";
import { CollaborationTabContent } from "./CollaborationTabContent";
import { NavProvider, useNavContext } from "~/v2/contexts/NavContext";
import { Filters } from "./Filters";
import { Button } from "~/v2/components";
import { api } from "~/trpc/react";
import { toast } from "~/components/snackbar";
import { useState } from "react";
import { QuestionDetailsDrawer } from "./QuestionDetailsDrawer";

const TABS = [
  { value: "all", label: "All" },
  { value: "pending-approval", label: "Pending Approval" },
];

function ResponseLibraryContent() {
  const { currentTab } = useNavContext();

  const [processLoading, setProcessLoading] = useState(false);

  const groupAllQuestions = api.question.groupAllQuestions.useMutation();

  const handleGroupAllQuestions = () => {
    setProcessLoading(true);
    groupAllQuestions.mutate(
      {},
      {
        onSuccess: (data) => {
          toast.success(data?.message ?? "Indexing similar questions success");
          setProcessLoading(false);
        },
        onError: (error) => {
          toast.error(
            `Error indexing similar questions: ${JSON.stringify(error)}`,
          );
          setProcessLoading(false);
        },
      },
    );
  };

  return (
    <>
      <ResponseLibraryProvider>
        <div className="w-full flex flex-1 max-h-full">
          <div className="w-full flex flex-col flex-1 max-h-full">
            <DashboardHeader
              title="Response Library"
              tabs={TABS}
              actions={
                <Button
                  onClick={handleGroupAllQuestions}
                  disabled={processLoading}
                >
                  Cluster Response
                </Button>
              }
            >
              {currentTab === "all" && <Filters />}
            </DashboardHeader>
            <DashboardContent>
              {currentTab === "all" && <AllQuestionsTabContent />}
              {currentTab === "pending-approval" && <CollaborationTabContent />}
            </DashboardContent>
          </div>
          <QuestionDetailsDrawer />
        </div>
      </ResponseLibraryProvider>
    </>
  );
}

export function ResponseLibraryView() {
  return (
    <NavProvider>
      <ResponseLibraryContent />
    </NavProvider>
  );
}

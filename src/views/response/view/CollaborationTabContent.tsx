"use client";
import React from "react";
import { useAllAssignedQuestions } from "../CollaborationListView/useAllAssignedQuestions";
import ResponseTable from "../ResponseTable";

export const CollaborationTabContent = () => {
  const { data: { pagination, items: questions = [] } = {}, isLoading } =
    useAllAssignedQuestions();

  return (
    <div className="flex-1 gap-2 overflow-y-auto relative">
      <ResponseTable
        allowEdit
        items={questions}
        loading={isLoading}
        totalPages={pagination?.totalPages ?? 0}
        totalRowCount={pagination?.total ?? 0}
      />
    </div>
  );
};

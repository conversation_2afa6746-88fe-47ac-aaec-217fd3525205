import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import { useState, useEffect } from "react";
import { Iconify } from "~/components/iconify";
import { Input } from "~/v2/components/ui/Input";
import { useDebounce } from "~/hooks/use-debounce";

export const SearchField = () => {
  const filters = useResponseLibraryContext();
  const [search, setSearch] = useState("");

  const debouncedSearch = useDebounce(search, 500);

  useEffect(() => {
    filters.setState({ search: debouncedSearch });
  }, [debouncedSearch]);

  return (
    <div className="relative w-full">
      <Iconify
        icon="eva:search-fill"
        className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground pointer-events-none"
      />
      <Input
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        placeholder="Search by question or DDQ"
        className="pl-10 bg-white"
        data-testid="response-library-search-input"
      />
    </div>
  );
};

"use client";
import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { useAllQuestionWithSearch } from "./useAllQuestionWithSearch";
import ResponseTable from "../ResponseTable";

export const AllQuestionsTabContent = () => {
  const { data: { pagination, questions = [] } = {}, isLoading } =
    useAllQuestionWithSearch();

  return (
    <div className="flex-1 gap-2 overflow-y-auto relative">
      <ResponseTable
        items={questions as QuestionWithSearchType[]}
        loading={isLoading}
        totalPages={pagination?.totalPages ?? 0}
        totalRowCount={pagination?.total ?? 0}
      />
    </div>
  );
};

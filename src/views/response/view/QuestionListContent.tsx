import {
  type QuestionWithSearchType,
  type GroupQuestionType,
} from "~/server/api/routers/question.common";
import { PaginatedView } from "../PaginatedView";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import { useDashboardLayout } from "~/layouts/Dashboard/DashboardLayoutContext";

type Props = {
  questions: QuestionWithSearchType[];
  isLoading: boolean;
  totalPages: number;
  groupItems?: GroupQuestionType[];
  allowEdit: boolean;
};

export const QuestionListContent = ({
  questions,
  totalPages,
  isLoading,
  groupItems,
  allowEdit,
}: Props) => {
  const filters = useResponseLibraryContext();

  const { setHeaderElevated } = useDashboardLayout();

  return (
    <>
      <PaginatedView
        items={questions}
        groupItems={groupItems}
        totalPages={totalPages}
        currentPage={filters.state.currentPage}
        pageSize={filters.state.pageSize}
        setPageSize={(size) => filters.setState({ pageSize: size })}
        setCurrentPage={(page) => filters.setState({ currentPage: page })}
        loading={isLoading}
        search={filters.state.search}
        allowEdit={allowEdit}
        onScroll={(x, y) => {
          setHeaderElevated(y > 2);
        }}
      />
    </>
  );
};

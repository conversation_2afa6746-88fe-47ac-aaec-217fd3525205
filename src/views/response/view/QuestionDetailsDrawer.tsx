import { Button } from "~/v2/components";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import QuestionDetailContainer from "../QuestionDetail/Container";
import { Drawer, DrawerContent } from "~/v2/components/ui/Drawer";
import { XIcon } from "lucide-react";
import { DialogTitle } from "~/v2/components/ui/Dialog";

export const QuestionDetailsDrawer = () => {
  const filters = useResponseLibraryContext();
  const selectedQuestionId = filters.state.selectedQuestionId;

  if (!selectedQuestionId) {
    return null;
  }

  return (
    <Drawer
      open={!!selectedQuestionId}
      modal={false}
      onOpenChange={(open) => {
        if (!open) {
          filters.setState({ selectedQuestionId: null, allowEdit: false });
        }
      }}
      data-testid="response-library-drawer"
    >
      <DrawerContent
        className="p-3 bg-white"
        data-vaul-drawer-direction="right"
        portal={false}
        size="500px"
      >
        <DialogTitle className="sr-only">Question and Response Details</DialogTitle>
        <QuestionDetailContainer
          questionId={selectedQuestionId}
        />
      </DrawerContent>
    </Drawer>
  );
};

import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { api } from "~/trpc/react";
import { QuestionDetails } from "./QuestionDetails";

type Props = {
  questions: QuestionWithSearchType[];
  search: string | undefined;
  allowEdit: boolean;
};

export function QuestionList({ questions, search, allowEdit }: Props) {
  const tagEntityConnections = api.tag.getTagEntityConnections.useQuery(
    {
      questions: questions.map((q) => q.id),
    },
    {
      enabled: !!questions,
    },
  );

  return (
    <>
      {questions
        .filter((q) => q.questionContents.length > 0)
        .map((question, index) => (
          <div key={question.id}>
            <QuestionDetails
              key={question.id}
              question={question}
              tagEntityConnections={tagEntityConnections.data ?? []}
              search={search}
              allowEdit={allowEdit}
            />
            {index < questions.length - 1 && (
              <div style={{ margin: "8px 0" }} />
            )}
          </div>
        ))}
    </>
  );
}

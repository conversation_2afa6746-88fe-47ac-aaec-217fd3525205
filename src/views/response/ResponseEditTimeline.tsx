
import { Stack } from "@mui/material";
import { type GetEditsForResponseType } from "~/server/api/routers/response";
import { ActivityTimeline, ActivityTimelineItem, Typography } from "~/v2/components";

export const ResponseEditTimeline = ({
  edits,
}: {
  edits: GetEditsForResponseType[];
}) => {
  const data = edits.map((edit) => ({
    ...edit,
    timestamp: edit.response?.createdAt ?? new Date(),
  }));
  return (
    <ActivityTimeline data={data}>
      {(edit, index) => (
        <ActivityTimelineItem>
          <Stack width="100%">
            <Typography variant="h5" className="text-sm">
              {index === data.length - 1 ? "Response Created" : "Response Edited"}
            </Typography>
            <Typography variant="caption" className="text-xs">
              By {edit.createdBy.name} {edit.response?.updatedAt.toLocaleString()}
            </Typography>
          </Stack>
          <Typography variant="body" className="text-sm">
            {
              index === data.length - 1 ? 
              `Automatically generated from ${edit?.response?.documents[0]?.document?.name}`
              : (edit?.content as { text: string })?.text
            }
          </Typography>
        </ActivityTimelineItem>
      )}
    </ActivityTimeline>
  );
};

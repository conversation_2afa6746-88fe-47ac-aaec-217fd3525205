import { useQuestionActivity } from "~/components/virgil/hooks/useQuestionActivity";
import { QuestionActivityTimeline } from "./QuestionActivityTimeline";
import { Typography } from "~/v2/components";
import { Skeleton } from "~/v2/components/ui/Skeleton";

export const QuestionActivityTimelineContainer = ({ questionId }: { questionId: string }) => {
    const questionActivity = useQuestionActivity(questionId);
    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                alignItems: "left",
                gap: 10,
                width: "100%",
                padding: 0,
                wordWrap: "break-word"
            }}
        >
            <Typography variant="h6">Activity</Typography>
            {questionActivity.isLoading ? (
                <Skeleton className="h-7" />
            ) : (
                <QuestionActivityTimeline activities={questionActivity.data ?? []} />
            )}
        </div>
    )
} 
import { Accordion, AccordionSummary, AccordionDetails, Typography } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import { TablePagination } from "@mui/material";
import Loading from "~/app/dashboard/loading";
import { type GroupQuestionType, type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { QuestionList } from "./QuestionList";
import { useEffect, useRef } from "react";

type Props = {
  items: QuestionWithSearchType[];
  groupItems?: GroupQuestionType[];
  search: string | undefined;
  loading: boolean;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  setPageSize: (pageSize: number) => void;
  setCurrentPage: (page: number) => void;
  onScroll?: (x: number, y: number) => void;
  allowEdit: boolean;
};

export function PaginatedView({
  items,
  search,
  loading,
  totalPages,
  currentPage,
  pageSize,
  setPageSize,
  setCurrentPage,
  groupItems = [],
  onScroll,
  allowEdit,
}: Props) {
  const handleChangePage = (
    event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number,
  ) => {
    setCurrentPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setPageSize(parseInt(event.target.value, 10));
    setCurrentPage(0);
  };

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement;
    onScroll?.(target.scrollLeft, target.scrollTop);
  };

  useEffect(() => {
    return () => {
      onScroll?.(0, 0);
    };
  }, []);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="flex flex-col items-start space-y-2 w-full h-full">
      <div
        className="grid grid-cols-1 w-full overflow-auto max-h-full gap-2 m-0 pr-4"
        onScroll={handleScroll}
      >
        {groupItems.length > 0 ? (
          groupItems.map((group) => {
            const groupQuestions = items.filter((item) =>
              group.questionIds.includes(item.id),
            );

            if (group.questionIds.length < 2) {
              return (
                <QuestionList key={group.id} questions={groupQuestions} search={search} allowEdit={allowEdit} />
              )
            }

            return (
              <Accordion key={group.id} defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>{group.name}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <QuestionList questions={groupQuestions} search={search} allowEdit={allowEdit} />
                </AccordionDetails>
              </Accordion>
            );
          })
        ) : (
          <QuestionList questions={items} search={search} allowEdit={allowEdit} />
        )}
      </div>

      <div className="flex flex-row items-center justify-between w-full">
        <TablePagination
          sx={{
            "& .css-1ai7qwe-MuiToolbar-root-MuiTablePagination-toolbar": {
              height: "32px",
              minHeight: "32px",
            },
            overflow: "hidden",
          }}
          component="div"
          page={currentPage}
          count={Math.ceil(totalPages * pageSize)}
          rowsPerPage={pageSize}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </div>
    </div>
  );
}

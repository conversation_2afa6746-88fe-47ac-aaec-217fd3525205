import { Category } from "@prisma/client";
import React, { useEffect, useState } from "react";
import { useDeleteCategory } from "~/hooks/useDeleteCategory";
import { api } from "~/trpc/react";
import { Button } from "~/v2/components/ui/Button";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "~/v2/components/ui/Dialog";
import { Input } from "~/v2/components/ui/Input";
import { Textarea } from "~/v2/components/ui/Textarea";
import { DeleteCategoryDialog } from "./DeleteCategoryDialog";

interface UpdateCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category: any;
}

type CategoryForm = Omit<Category, "id" | "createdAt" | "updatedAt"> & {
  children: string[];
};

const UpdateCategoryDialog: React.FC<UpdateCategoryDialogProps> = ({
  open,
  onOpenChange,
  category,
}) => {
  const [updating, setUpdating] = useState(false);
  const [form, setForm] = useState<CategoryForm>();

  useEffect(() => {
    if (!category) return;

    setForm({
      ...category,
    });
  }, [category]);

  const [formError, setFormError] = useState<string | null>(null);
  const {
    confirmDelete,
    handleDelete,
    cancelDelete,
    deleting,
    error: deleteError,
    confirmOpen,
    pendingDelete,
  } = useDeleteCategory();

  const updateCategory = api.category.updateCategory.useMutation();
  const utils = api.useUtils?.() || {};

  const handleInput = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    if (!form) return;
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleConfirmDelete = async () => {
    const success = await handleDelete();
    if (success && pendingDelete && form) {
      // Remove from current children list if it was selected
      setForm({
        ...form,
        children: form.children.filter(
          (id: string) => id !== pendingDelete.categoryId,
        ),
      });
    }
  };

  const handleUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    if (!form) return;
    if (!form.name.trim()) {
      setFormError("Name is required");
      return;
    }
    setUpdating(true);
    updateCategory.mutate(
      {
        id: category.id,
        name: form.name.trim(),
        description: form.description?.trim() || undefined,
        staleAfterDays: form.staleAfterDays
          ? Number(form.staleAfterDays)
          : undefined,
      },
      {
        onSuccess: () => {
          onOpenChange(false);
          if (utils.category?.getCategoryTreeWithDocuments?.invalidate) {
            utils.category.getCategoryTreeWithDocuments.invalidate();
          }
        },
        onError: (err) => {
          setFormError(err.message || "Error updating category");
        },
        onSettled: () => {
          setUpdating(false);
        },
      },
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Category</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleUpdate} className="flex flex-col gap-2">
          <div>
            <label className="block font-medium">
              Name <span className="text-red-500">*</span>
            </label>
            <Input
              name="name"
              value={form?.name ?? ""}
              onChange={handleInput}
              required
              disabled={updating}
            />
          </div>
          <div>
            <label className="block font-medium">Description</label>
            <Textarea
              name="description"
              value={form?.description ?? ""}
              onChange={handleInput}
              disabled={updating}
            />
          </div>
          <div>
            <label className="block font-medium">Stale After Days</label>
            <Input
              name="staleAfterDays"
              type="number"
              min={0}
              value={form?.staleAfterDays ?? undefined}
              onChange={handleInput}
              disabled={updating}
            />
          </div>
          {formError && <div className="text-red-500 text-sm">{formError}</div>}
          {deleteError && (
            <div className="text-red-500 text-sm">{deleteError}</div>
          )}
          <DialogFooter>
            <Button type="submit" disabled={updating}>
              {updating ? "Saving..." : "Save"}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={updating}>
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>

      {pendingDelete && confirmOpen && (
        <DeleteCategoryDialog
          open={confirmOpen}
          onOpenChange={cancelDelete}
          categoryName={pendingDelete?.categoryName}
          onConfirmDelete={handleConfirmDelete}
        />
      )}
    </Dialog>
  );
};

export default UpdateCategoryDialog;

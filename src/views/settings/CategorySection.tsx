import {
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { CategoryStatus } from "@prisma/client";
import { CompleteCategory } from "prisma/zod";
import React, { useState } from "react";
import { Iconify } from "~/components/iconify";
import { useDeleteCategory } from "~/hooks/useDeleteCategory";
import { useDeleteDocumentFromCategory } from "~/hooks/useDeleteDocumentFromCategory";
import { api } from "~/trpc/react";
import { Button } from "~/v2/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/v2/components/ui/DropdownMenu";
import { Typography } from "~/v2/components/ui/Typography";
import AddCategoryDialog from "./AddCategoryDialog";
import { DeleteCategoryDialog } from "./DeleteCategoryDialog";
import { DeleteDocumentFromCategoryDialog } from "./DeleteDocumentFromCategoryDialog";
import UpdateCategoryDialog from "./UpdateCategoryDialog";
import { cn } from "~/v2/lib/utils";

interface CategorySectionProps {
  category: any;
  allCategories: CompleteCategory[];
  filterMode: "MISSING_ONLY" | "ALL" | "ACTIVE_ONLY";
  onRefetch: () => void;
}

type CategoryWithDocumentCount = CompleteCategory & {
  _count: {
    documents: number;
  };
};

const CategorySection: React.FC<CategorySectionProps> = ({
  category,
  filterMode,
  onRefetch,
}) => {
  const [addOpen, setAddOpen] = useState(false);
  const [updateOpen, setUpdateOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<CategoryWithDocumentCount | null>(null);

  const {
    confirmDelete,
    handleDelete,
    cancelDelete,
    deleting,
    error: deleteError,
    confirmOpen,
    pendingDelete,
  } = useDeleteCategory();
  const updateCategory = api.category.updateCategory.useMutation();

  const totalCriteria = category?.children.reduce((acc: number, child: any) => {
    if (filterMode === "ALL") {
      acc += 1;
      return acc;
    }

    if (child.status !== CategoryStatus.INACTIVE) {
      acc += 1;
    }

    return acc;
  }, 0);

  const matchCriteria = category?.children.reduce((acc: number, child: any) => {
    if (filterMode === "ALL") {
      acc += child._count.documents ? 1 : 0;
      return acc;
    }

    if (child.status !== CategoryStatus.INACTIVE) {
      acc += child._count.documents ? 1 : 0;
    }

    return acc;
  }, 0);

  const {
    confirmDelete: confirmDeleteDocument,
    handleDelete: handleDeleteDocument,
    cancelDelete: cancelDeleteDocument,
    deleting: deletingDocument,
    error: deleteDocumentError,
    confirmOpen: confirmDocumentOpen,
    pendingDelete: pendingDeleteDocument,
  } = useDeleteDocumentFromCategory();
  const createCategory = api.category.createCategory.useMutation();

  const handleAddCategory = (form: {
    name: string;
    description?: string;
    staleAfterDays?: number;
    parentId?: string;
  }) => {
    createCategory.mutate(form, {
      onSuccess: () => {
        setAddOpen(false);
        onRefetch();
      },
      onError: (err) => {},
      onSettled: () => {},
    });
  };

  let subCategories = [];

  if (filterMode === "MISSING_ONLY") {
    subCategories = category?.children?.filter(
      (child: any) => child._count.documents === 0,
    );
  } else {
    subCategories = category?.children;
  }

  return (
    <section>
      <div className="flex items-center gap-2 mb-2 justify-between">
        <Typography variant="h6" className="font-semibold">
          {category?.name} ({matchCriteria}/{totalCriteria})
        </Typography>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => setAddOpen(true)}>
            Add Checklist Item
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <span role="img" aria-label="actions">
                  <Iconify icon="eva:more-vertical-fill" width={24} />
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedCategory(category);
                  setUpdateOpen(true);
                }}
              >
                Update
              </DropdownMenuItem>
              {!category.seedId && (
                <DropdownMenuItem
                  variant="destructive"
                  onClick={() =>
                    confirmDelete(
                      category.id,
                      category.name,
                      category.children && category.children.length > 0,
                    )
                  }
                  disabled={
                    deleting ||
                    (category.children && category.children.length > 0)
                  }
                >
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {updateOpen && (
        <UpdateCategoryDialog
          open={updateOpen}
          onOpenChange={setUpdateOpen}
          category={selectedCategory}
        />
      )}

      <AddCategoryDialog
        open={addOpen}
        setOpen={setAddOpen}
        parentOptions={{
          id: category.id,
          name: category.name,
        }}
        onSubmit={handleAddCategory}
        loading={false}
        error={null}
      />

      {pendingDelete && confirmOpen && (
        <DeleteCategoryDialog
          open={confirmOpen}
          onOpenChange={cancelDelete}
          categoryName={pendingDelete?.categoryName}
          onConfirmDelete={handleDelete}
        />
      )}

      {pendingDeleteDocument && confirmDocumentOpen && (
        <DeleteDocumentFromCategoryDialog
          open={confirmDocumentOpen}
          onOpenChange={cancelDeleteDocument}
          documentName={pendingDeleteDocument?.documentName}
          onConfirmDelete={handleDeleteDocument}
          deleting={deletingDocument}
        />
      )}

      <TableContainer
        component={Paper}
        sx={{ maxWidth: "100%", overflowX: "auto" }}
      >
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: "bold", width: "100%" }}>
                Name
              </TableCell>
              <TableCell sx={{ fontWeight: "bold", width: 50 }}>
                Status
              </TableCell>
              <TableCell sx={{ width: 80 }}></TableCell>
              <TableCell sx={{ width: 80 }}></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {subCategories?.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={4}
                  align="center"
                  sx={{ color: "text.secondary" }}
                >
                  No checklist items
                </TableCell>
              </TableRow>
            ) : (
              subCategories.map((child: CategoryWithDocumentCount) => {
                const isIncluded = child._count.documents > 0;
                const isActive = child.status !== CategoryStatus.INACTIVE;
                return (
                  <TableRow key={child.id} hover>
                    <TableCell>
                      <div
                        className={cn(
                          "flex items-center gap-2",
                          !isActive && "text-gray-400",
                        )}
                      >
                        {child.name}
                      </div>
                    </TableCell>

                    <TableCell>
                      <Button
                        className={cn(
                          isActive && isIncluded && "!bg-success !text-white",
                          isActive && !isIncluded && "!bg-warning !text-white",
                          !isActive && "!bg-gray-200 !text-gray-400",
                        )}
                        variant="ghost"
                        size="sm"
                      >
                        {isIncluded ? "Included" : "Missing"}
                      </Button>
                    </TableCell>

                    <TableCell align="center">
                      <Switch
                        checked={child.status !== CategoryStatus.INACTIVE}
                        onChange={(e) => {
                          updateCategory.mutate({
                            id: child.id,
                            name: child.name,
                            status: e.target.checked
                              ? CategoryStatus.ACTIVE
                              : CategoryStatus.INACTIVE,
                          });
                          onRefetch();
                        }}
                        color="primary"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <span role="img" aria-label="actions">
                              <Iconify
                                icon="eva:more-vertical-fill"
                                width={24}
                              />
                            </span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedCategory(child);
                              setUpdateOpen(true);
                            }}
                            disabled={deletingDocument}
                          >
                            Update
                          </DropdownMenuItem>
                          {!child.seedId && (
                            <DropdownMenuItem
                              variant="destructive"
                              onClick={() => {
                                confirmDelete(child.id, child.name, false);
                              }}
                              disabled={deletingDocument}
                            >
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </section>
  );
};

export default CategorySection;

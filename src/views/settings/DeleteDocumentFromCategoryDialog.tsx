import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "~/v2/components/ui/Dialog";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";

interface DeleteDocumentFromCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentName: string;
  onConfirmDelete: () => void;
  deleting?: boolean;
}

export const DeleteDocumentFromCategoryDialog: React.FC<
  DeleteDocumentFromCategoryDialogProps
> = ({
  open,
  onOpenChange,
  documentName,
  onConfirmDelete,
  deleting = false,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Remove Document from Category</DialogTitle>
        </DialogHeader>
        <div>
          <Typography variant="body">
            Are you sure you want to remove{" "}
            <strong>&quot;{documentName}&quot;</strong> from this category.
          </Typography>
          <Typography variant="body" className="text-muted-foreground mt-2">
            This action will only remove the document from this category. The
            document will not be deleted from the system.
          </Typography>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={deleting}
          >
            Cancel
          </Button>

          <Button
            variant="destructive"
            onClick={onConfirmDelete}
            disabled={deleting}
          >
            {deleting ? "Removing..." : "Remove"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

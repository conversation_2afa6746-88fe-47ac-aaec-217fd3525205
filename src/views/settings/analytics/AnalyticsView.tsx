"use client";
import Loading from "~/app/loading";
import { DashboardContent } from "~/layouts/Dashboard/DashboardContent";
import { DashboardHeader } from "~/layouts/Dashboard/DashboardHeader";
import { api } from "~/trpc/react";
import { ChartPieByDocumentStatus } from "./ChartPieByDocumentStatus";

export function AnalyticsView() {
  const { data: stats, isLoading } = api.analytics.getDocumentStatsByStatus.useQuery();

  if (isLoading) {
    return <Loading />;
  }


  return (
    <>
      <DashboardHeader
        title="Analytics"
        actions={<></>}
      >
      </DashboardHeader>
      <DashboardContent>
        <ChartPieByDocumentStatus />
      </DashboardContent>
    </>
  );
}

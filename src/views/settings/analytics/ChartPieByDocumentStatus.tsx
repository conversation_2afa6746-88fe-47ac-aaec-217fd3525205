"use client"

import * as React from "react"
import { TrendingUp } from "lucide-react"
import { Label, Pie, <PERSON><PERSON><PERSON> } from "recharts"

import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "~/v2/components/ui/Card"
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from "~/v2/components/ui/Chart"
import { api } from "~/trpc/react"

const chartConfig = {
    count: {
        label: "Count",
    },
    PENDING: {
        label: "Pending",
        color: "var(--chart-1)",
    },
    PROCESSING: {
        label: "Processing",
        color: "var(--chart-2)",
    },
    TAGGING: {
        label: "Tagging",
        color: "var(--chart-3)",
    },
    GENERATING_ANSWERS: {
        label: "Generating Answers",
        color: "var(--chart-4)",
    },
    READY: {
        label: "Ready",
        color: "var(--chart-5)",
    },
    ERROR: {
        label: "Error",
        color: "var(--chart-6)",
    },
    EXTERNAL: {
        label: "External",
        color: "var(--chart-7)",
    },
} satisfies ChartConfig

export function ChartPieByDocumentStatus() {
    const { data: stats } = api.analytics.getDocumentStatsByStatus.useQuery();
    const chartData = stats?.byStatus?.map(({ status, count }) => ({
        status,
        count,
        fill: chartConfig[status]?.color,
    })) ?? [];

    return (
        <Card className="flex flex-col">
            <CardHeader className="items-center pb-0">
                <CardTitle>Documents by Status</CardTitle>
                <CardDescription>Total Documents: {stats?.totalCount}</CardDescription>
            </CardHeader>
            <CardContent className="flex-1 pb-0">
                <ChartContainer
                    config={chartConfig}
                    className="mx-auto aspect-square max-h-[250px]"
                >
                    <PieChart>
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent hideLabel />}
                        />
                        <Pie
                            data={chartData}
                            dataKey="count"
                            nameKey="status"
                            innerRadius={60}
                            strokeWidth={5}
                        >
                            <Label
                                content={({ viewBox }) => {
                                    if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                                        return (
                                            <text
                                                x={viewBox.cx}
                                                y={viewBox.cy}
                                                textAnchor="middle"
                                                dominantBaseline="middle"
                                            >
                                                <tspan
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    className="fill-foreground text-3xl font-bold"
                                                >
                                                    {stats?.totalCount.toLocaleString()}
                                                </tspan>
                                                <tspan
                                                    x={viewBox.cx}
                                                    y={(viewBox.cy || 0) + 24}
                                                    className="fill-muted-foreground"
                                                >
                                                    Documents
                                                </tspan>
                                            </text>
                                        )
                                    }
                                }}
                            />
                        </Pie>
                    </PieChart>
                </ChartContainer>
            </CardContent>
            <CardFooter className="flex-col gap-2 text-sm">
                <div className="grid grid-cols-2 gap-4 w-full">
                    {chartData?.map((item) => (
                        <div key={item.status} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: item.fill }}
                                />
                                <span className="text-sm font-medium">
                                    {chartConfig[item.status as keyof typeof chartConfig]?.label}
                                </span>
                            </div>
                            <span className="text-sm text-muted-foreground">
                                {item.count}
                            </span>
                        </div>
                    ))}
                </div>
                <div className="text-muted-foreground leading-none text-center mt-2">
                    Document status distribution across all uploaded files
                </div>
            </CardFooter>
        </Card>
    )
}


import { useEffect, useMemo, useState } from "react";

import { toast } from "~/components/snackbar";
import { Card } from "~/v2/components/ui/Card";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";
import Loading from "~/app/loading";
import { api } from "~/trpc/react";
import { getBaseUrl } from "~/utils/url";
import { SiteSelector } from "../data-room/SiteSelector";
import { env } from "~/env";
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";

const openEgnyteAuthPopup = (webhookSecret: string) => {
  const params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=500,height=500`;

  window.open(
    `https://${env.NEXT_PUBLIC_EGNYTE_DOMAIN}/puboauth/token?client_id=${env.NEXT_PUBLIC_EGNYTE_CONNECT_API_KEY}&redirect_uri=${getBaseUrl()}/api/oauth&response_type=code&scope=Egnyte.filesystem Egnyte.webhooks&state=EGNYTE.${webhookSecret}`,
    params,
  );
};

const openAzureAdminConsentPopup = (webhookSecret: string) => {
  const params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=500,height=760,left=${(window.screen.width - 500) / 2},top=${(window.screen.height - 760) / 2}`;

  const popup = window.open(
    `https://login.microsoftonline.com/common/adminconsent?client_id=${env.NEXT_PUBLIC_AZURE_CLIENT_APP_ID}&redirect_uri=${getBaseUrl()}/api/oauth&state=AZURE.${webhookSecret}`,
    "azure_admin_consent",
    params,
  );

  // Focus the popup if it was successfully opened
  if (popup) {
    popup.focus();
  }
};

const IntegrationsView = () => {
  const { data: org, isLoading } = api.organization.get.useQuery({});

  const [egnyteLoading, setEgnyteLoading] = useState(false);
  const [sharepointLoading, setSharepointLoading] = useState(false);

  const disconnectEgnyte = api.egnyte.disconnect.useMutation();
  const disconnectAzure = api.azure.disconnect.useMutation();

  useEffect(() => {
    if (org?.egnyteAccessTokenId) {
      setEgnyteLoading(false);
    }
  }, [org?.egnyteAccessTokenId]);

  useEffect(() => {
    if (org?.azureAccessTokenId) {
      setSharepointLoading(false);
    }
  }, [org?.azureAccessTokenId]);

  const renderSiteSelector = useMemo(() => {
    return org?.azureAccessTokenId ? <SiteSelector org={org} /> : null;
  }, [org]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <>
      <DashboardHeader title="Integrations" />
      <DashboardContent>
        <div className="flex flex-col h-[40vh]">
          <div className="grid gap-2 w-full overflow-auto max-h-full grid-cols-1 md:grid-cols-1 sm:grid-cols-1">
            <Card>
              <div className="flex items-start justify-between p-2 gap-0.5 text-sm">
                <Typography variant="h6" className="font-semibold p-0">
                  Egnyte
                </Typography>
                <Button
                  color="inherit"
                  disabled={!org?.webhookSecret || egnyteLoading}
                  onClick={() => {
                    setEgnyteLoading(true);
                    if (org?.egnyteAccessTokenId) {
                      disconnectEgnyte.mutate(undefined, {
                        onSuccess: () => {
                          toast.success("Egnyte disconnected");
                        },
                        onError: (error: any) => {
                          toast.error(
                            `Error disconnecting Egnyte: ${error.message}`,
                          );
                        },
                        onSettled: () => {
                          setEgnyteLoading(false);
                        },
                      });
                    } else {
                      openEgnyteAuthPopup(org?.webhookSecret ?? "");
                    }
                  }}
                >
                  {org?.egnyteAccessTokenId ? "Disconnect" : "Connect"}
                </Button>
              </div>
            </Card>
            <Card>
              <div className="flex items-start justify-between p-2 gap-0.5 text-sm">
                <Typography variant="h6" className="font-semibold p-0">
                  SharePoint
                </Typography>
                <div className="flex gap-1">
                  {renderSiteSelector}
                  <Button
                    color="inherit"
                    disabled={!org?.webhookSecret || sharepointLoading}
                    onClick={() => {
                      setSharepointLoading(true);
                      if (org?.azureAccessTokenId) {
                        disconnectAzure.mutate(undefined, {
                          onSuccess: () => {
                            toast.success("Sharepoint disconnected");
                          },
                          onError: (error: any) => {
                            toast.error(
                              `Error disconnecting SharePoint: ${error.message}`,
                            );
                          },
                          onSettled: () => {
                            setSharepointLoading(false);
                          },
                        });
                      } else {
                        openAzureAdminConsentPopup(org?.webhookSecret ?? "");
                      }
                    }}
                  >
                    {org?.azureAccessTokenId ? "Disconnect" : "Connect"}
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </DashboardContent>
    </>
  );
};

export default IntegrationsView;

import React, { type FC, useEffect, useState } from "react";
import Loading from "~/app/dashboard/loading";
import { api } from "~/trpc/react";
import { downloadExcelFile } from "~/utils/excelExport";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";
import AddCategoryDialog from "./AddCategoryDialog";
import CategorySection from "./CategorySection";
import dayjs from "dayjs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/v2/components/ui/Select";
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";

const DiligenceChecklistView: FC = () => {
  const filterModeOptions = [
    {
      label: "Missing only",
      value: "MISSING_ONLY",
    },
    {
      label: "All",
      value: "ALL",
    },
    {
      label: "Active only",
      value: "ACTIVE_ONLY",
    },
  ];
  const [filterMode, setFilterMode] = useState<
    "MISSING_ONLY" | "ALL" | "ACTIVE_ONLY"
  >("ALL");

  const {
    data: categoryResponse,
    isLoading,
    error,
    refetch,
  } = api.category.getCategoryTreeWithDocuments.useQuery({
    filterMode: filterMode,
  });

  const createCategory = api.category.createCategory.useMutation();
  const exportCategories = api.category.exportCategories.useMutation();

  const [categories, setCategories] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    if (categoryResponse) {
      setCategories(categoryResponse);
    }
  }, [categoryResponse]);

  const handleAddCategory = (form: {
    name: string;
    description?: string;
    staleAfterDays?: number;
    parentId?: string;
  }) => {
    setFormError(null);
    setCreating(true);
    createCategory.mutate(form, {
      onSuccess: () => {
        setOpen(false);
        refetch();
      },
      onError: (err) => {
        setFormError(err.message || "Error creating category");
      },
      onSettled: () => {
        setCreating(false);
      },
    });
  };

  const handleExport = () => {
    exportCategories.mutate(undefined, {
      onSuccess: (data) => {
        downloadExcelFile(data, `diligence_checklist_${dayjs().unix()}`);
      },
    });
  };

  return (
    <>
      <DashboardHeader
        title="Due Diligence"
        actions={
          <>
            <AddCategoryDialog
              open={open}
              setOpen={setOpen}
              onSubmit={handleAddCategory}
              loading={creating}
              error={formError}
            />
            <div className="flex flex-col gap-2">
              <div className="flex flex-row gap-2">
                <Button variant="outline" size="sm" onClick={handleExport}>
                  Export
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setOpen(true)}
                >
                  Add category
                </Button>
              </div>
            </div>
          </>
        }
      >
        {/* <Select
          value={filterMode}
          onValueChange={(value) => {
            setFilterMode(value as "MISSING_ONLY" | "ALL" | "ACTIVE_ONLY");
          }}
        >
          <SelectTrigger className="w-full cursor-pointer">
            <SelectValue placeholder="Select filter" />
          </SelectTrigger>
          <SelectContent>
            {filterModeOptions?.map(
              (option: { label: string; value: string }) => (
                <SelectItem
                  className="cursor-pointer"
                  key={option.value}
                  value={option.value}
                >
                  {option.label}
                </SelectItem>
              ),
            )}
          </SelectContent>
        </Select> */}
      </DashboardHeader>
      <DashboardContent>
        {isLoading && <Loading />}
        {error && (
          <Typography variant="body" className="text-red-500">
            Error loading categories
          </Typography>
        )}
        {!isLoading && !categories?.length && (
          <div className="flex flex-col gap-2 text-center">
            <Typography variant="body">No categories found.</Typography>
          </div>
        )}

        {categories?.map((category: any) => (
          <article
            key={category.id}
            className="mb-2 pb-2 border-b border-gray-200"
          >
            <CategorySection
              category={category}
              allCategories={categories}
              onRefetch={refetch}
              filterMode={filterMode}
            />
          </article>
        ))}
      </DashboardContent>
    </>
  );
};

export default DiligenceChecklistView;

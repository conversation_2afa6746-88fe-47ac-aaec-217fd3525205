import { Input } from "~/v2/components/ui/Input";

interface ColorInputProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export function ColorInput({
  value,
  onChange,
  disabled = false,
}: ColorInputProps) {
  return (
    <Input
      type="color"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-16 h-10 p-1"
      disabled={disabled}
    />
  );
}

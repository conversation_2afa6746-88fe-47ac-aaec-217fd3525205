import { useState, useEffect } from "react";
import {
  DataGridPremium,
  type GridColDef,
  type GridRowsProp,
  type GridRowSelectionModel,
} from "@mui/x-data-grid-premium";
import { api } from "~/trpc/react";
import { type Fund } from "@prisma/client";
import { EmptyContent } from "~/components/empty-content";
import { Badge } from "~/v2/components/ui/Badge";
import { Button } from "~/v2/components/ui/Button";
import { DashboardContent } from "~/layouts/Dashboard";
import { EditFundModal } from "./EditFundModal";

export default function FundsTab() {
  const [rows, setRows] = useState<GridRowsProp>([]);
  const [rowSelectionModel, setRowSelectionModel] =
    useState<GridRowSelectionModel>([]);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [modalMode, setModalMode] = useState<"create" | "edit">("edit");

  // Fetch funds data
  const { data: funds, isLoading } = api.fund.getAllFunds.useQuery();

  // Update rows when funds data changes
  useEffect(() => {
    if (funds) {
      const fundsRows: GridRowsProp = funds.map((fund: Fund) => ({
        id: fund.id,
        name: fund.name,
        description: fund.description,
        strategy: fund.strategy,
        color: fund.color,
      }));
      setRows(fundsRows);
    }
  }, [funds]);

  // Handle edit button click
  const handleEditClick = () => {
    if (rowSelectionModel.length > 0 && funds) {
      const fundId = rowSelectionModel[0];
      const fund = funds.find((f) => f.id === fundId);
      if (fund) {
        console.log("[FundsTab] Opening edit modal for fund:", fund.name);
        setSelectedFund(fund);
        setModalMode("edit");
        setIsEditModalOpen(true);
      }
    }
  };

  // Handle create button click
  const handleCreateClick = () => {
    console.log("[FundsTab] Opening create modal");
    setSelectedFund(null);
    setModalMode("create");
    setIsEditModalOpen(true);
  };

  // Handle successful fund update
  const handleFundUpdateSuccess = () => {
    // The mutation will automatically invalidate the query and refresh the data
    setRowSelectionModel([]);
  };

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Fund",
      minWidth: 200,
      flex: 1,
      renderCell: (params) => (
        <Badge
          style={{ backgroundColor: params.row.color }}
          className="text-xs font-medium"
        >
          {params.row.name}
        </Badge>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      minWidth: 250,
      flex: 1.5,
      renderCell: (params) => (
        <span className="text-xs text-gray-700">{params.row.description}</span>
      ),
    },
    {
      field: "strategy",
      headerName: "Strategy",
      minWidth: 200,
      flex: 1,
      renderCell: (params) => (
        <span className="text-xs text-gray-700">
          {params.row.strategy || "N/A"}
        </span>
      ),
    },
  ];

  return (
    <>
      <DashboardContent>
        <div className="mb-2 flex justify-end">
          <Button variant="default" onClick={handleCreateClick}>
            Create
          </Button>
        </div>
        <DataGridPremium
          rows={rows}
          columns={columns}
          loading={isLoading}
          hideFooterRowCount
          disableColumnSorting
          disableColumnFilter
          disableAggregation
          disableRowGrouping
          checkboxSelection
          rowSelectionModel={rowSelectionModel}
          onRowSelectionModelChange={(newRowSelectionModel) => {
            // Only allow single row selection
            if (newRowSelectionModel.length > 1) {
              const lastSelected =
                newRowSelectionModel[newRowSelectionModel.length - 1];
              if (lastSelected !== undefined) {
                setRowSelectionModel([lastSelected]);
              }
            } else {
              setRowSelectionModel(newRowSelectionModel);
            }
          }}
          sx={{
            border: "1px solid",
            borderColor: "#e5e7eb",
            backgroundColor: "white",
            "& .MuiDataGrid-cell:focus": {
              outline: "none",
            },
            "& .MuiDataGrid-cell": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
              display: "flex",
              alignItems: "center",
            },
            "& .MuiDataGrid-columnHeader": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
              fontSize: "12px",
            },
            "& .MuiDataGrid-row": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
            },
            "& .MuiDataGrid-cell:focus-within": {
              outline: "none",
            },
          }}
          slots={{ noRowsOverlay: EmptyContent }}
        />
      </DashboardContent>

      {/* Bottom bar for edit actions */}
      {rowSelectionModel.length > 0 && (
        <div className="bg-white border-t border-gray-200 p-3 flex items-center gap-2 shadow-[0_-2px_8px_rgba(0,0,0,0.3)]">
          <span className="flex-1"></span>
          <Button variant="default" onClick={handleEditClick}>
            Edit
          </Button>
        </div>
      )}

      {/* Edit Fund Modal */}
      <EditFundModal
        fund={selectedFund}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSuccess={handleFundUpdateSuccess}
        mode={modalMode}
      />
    </>
  );
}

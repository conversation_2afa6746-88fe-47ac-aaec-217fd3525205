import { useState, useEffect } from "react";
import {
  DataGridPremium,
  type GridColDef,
  type GridRowsProp,
  type GridRowSelectionModel,
} from "@mui/x-data-grid-premium";
import { api } from "~/trpc/react";
import { type Tag } from "@prisma/client";
import { EmptyContent } from "~/components/empty-content";
import { Badge } from "~/v2/components/ui/Badge";
import { Button } from "~/v2/components/ui/Button";
import { DashboardContent } from "~/layouts/Dashboard";
import { EditTagModal } from "./EditTagModal";

export default function TagsTab() {
  const [rows, setRows] = useState<GridRowsProp>([]);
  const [rowSelectionModel, setRowSelectionModel] =
    useState<GridRowSelectionModel>([]);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [modalMode, setModalMode] = useState<"create" | "edit">("edit");

  // Fetch tags data
  const { data: tags, isLoading } = api.tag.getAllTags.useQuery();

  // Update rows when tags data changes
  useEffect(() => {
    if (tags) {
      const tagsRows: GridRowsProp = tags.map((tag: Tag) => ({
        id: tag.id,
        name: tag.name,
        summary: tag.summary,
        color: tag.color,
      }));
      setRows(tagsRows);
    }
  }, [tags]);

  // Handle edit button click
  const handleEditClick = () => {
    if (rowSelectionModel.length > 0 && tags) {
      const tagId = rowSelectionModel[0];
      const tag = tags.find((t) => t.id === tagId);
      if (tag) {
        console.log("[TagsTab] Opening edit modal for tag:", tag.name);
        setSelectedTag(tag);
        setModalMode("edit");
        setIsEditModalOpen(true);
      }
    }
  };

  // Handle create button click
  const handleCreateClick = () => {
    console.log("[TagsTab] Opening create modal");
    setSelectedTag(null);
    setModalMode("create");
    setIsEditModalOpen(true);
  };

  // Handle successful tag update
  const handleTagUpdateSuccess = () => {
    // The mutation will automatically invalidate the query and refresh the data
    setRowSelectionModel([]);
  };

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Tag",
      minWidth: 200,
      flex: 1,
      renderCell: (params) => (
        <Badge
          style={{ backgroundColor: params.row.color }}
          className="text-xs font-medium"
        >
          {params.row.name}
        </Badge>
      ),
    },
    {
      field: "summary",
      headerName: "Summary",
      minWidth: 250,
      flex: 1.5,
      renderCell: (params) => (
        <span className="text-xs text-gray-700">{params.row.summary}</span>
      ),
    },
  ];

  return (
    <>
      <DashboardContent>
        <div className="mb-2 flex justify-end">
          <Button variant="default" onClick={handleCreateClick}>
            Create
          </Button>
        </div>
        <DataGridPremium
          rows={rows}
          columns={columns}
          loading={isLoading}
          hideFooterRowCount
          disableColumnSorting
          disableColumnFilter
          disableAggregation
          disableRowGrouping
          checkboxSelection
          rowSelectionModel={rowSelectionModel}
          onRowSelectionModelChange={(newRowSelectionModel) => {
            // Only allow single row selection
            if (newRowSelectionModel.length > 1) {
              const lastSelected =
                newRowSelectionModel[newRowSelectionModel.length - 1];
              if (lastSelected !== undefined) {
                setRowSelectionModel([lastSelected]);
              }
            } else {
              setRowSelectionModel(newRowSelectionModel);
            }
          }}
          sx={{
            border: "1px solid",
            borderColor: "#e5e7eb",
            backgroundColor: "white",
            "& .MuiDataGrid-cell:focus": {
              outline: "none",
            },
            "& .MuiDataGrid-cell": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
              display: "flex",
              alignItems: "center",
            },
            "& .MuiDataGrid-columnHeader": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
              fontSize: "12px",
            },
            "& .MuiDataGrid-row": {
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
              msUserSelect: "none",
            },
            "& .MuiDataGrid-cell:focus-within": {
              outline: "none",
            },
          }}
          slots={{ noRowsOverlay: EmptyContent }}
        />
      </DashboardContent>

      {/* Bottom bar for edit actions */}
      {rowSelectionModel.length > 0 && (
        <div className="bg-white border-t border-gray-200 p-3 flex items-center gap-2 shadow-[0_-2px_8px_rgba(0,0,0,0.3)]">
          <span className="flex-1"></span>
          <Button variant="default" onClick={handleEditClick}>
            Edit
          </Button>
        </div>
      )}

      {/* Edit Tag Modal */}
      <EditTagModal
        tag={selectedTag}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSuccess={handleTagUpdateSuccess}
        mode={modalMode}
      />
    </>
  );
}
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";
import { useNavContext } from "~/v2/contexts/NavContext";
import FundsTab from "./FundsTab";
import TagsTab from "./TagsTab";

const GeneralSettingsView = () => {
  const { currentTab } = useNavContext();
  return (
    <>
      <DashboardHeader
        title="General Settings"
        tabs={[
          {
            label: "Funds",
            value: "funds",
          },
          {
            label: "Tags",
            value: "tags",
          },
        ]}
      />
        {currentTab === "funds" && <FundsTab />}
        {currentTab === "tags" && <TagsTab />}
    </>
  );
};

export default GeneralSettingsView;

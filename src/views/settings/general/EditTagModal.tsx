"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { api } from "~/trpc/react";
import { type Tag } from "@prisma/client";
import { Input } from "~/v2/components/ui/Input";
import { Textarea } from "~/v2/components/ui/Textarea";
import { toast } from "src/components/snackbar";
import { BaseEditModal } from "./BaseEditModal";
import { FormField } from "./FormField";
import { ColorInput } from "./ColorInput";

interface EditTagModalProps {
  tag: Tag | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: "create" | "edit";
}

interface TagFormData {
  name: string;
  summary: string;
  color: string;
}

export function EditTagModal({
  tag,
  isOpen,
  onClose,
  onSuccess,
  mode,
}: EditTagModalProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<TagFormData>({
    defaultValues: {
      name: "",
      summary: "",
      color: "#5e8779",
    },
    mode: "onChange",
  });

  // Reset form when modal opens/closes or tag changes
  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && tag) {
        reset({
          name: tag.name,
          summary: tag.summary,
          color: tag.color || "#5e8779",
        });
      } else {
        reset({
          name: "",
          summary: "",
          color: "#5e8779",
        });
      }
    }
  }, [isOpen, mode, tag, reset]);

  const createMutation = api.tag.create.useMutation({
    onSuccess: () => {
      toast.success("Tag created successfully");
      onSuccess();
      onClose();
    },
    onError: (error) => {
      toast.error("Failed to create tag");
    },
  });

  const updateMutation = api.tag.update.useMutation({
    onSuccess: () => {
      toast.success("Tag updated successfully");
      onSuccess();
      onClose();
    },
    onError: (error) => {
      toast.error("Failed to update tag");
    },
  });

  const onSubmit = (data: TagFormData) => {
    if (mode === "create") {
      createMutation.mutate({
        name: data.name.trim(),
        summary: data.summary.trim(),
        color: data.color,
      });
    } else if (mode === "edit" && tag) {
      updateMutation.mutate({
        id: tag.id,
        name: data.name.trim(),
        summary: data.summary.trim(),
        color: data.color,
      });
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;
  const watchedValues = watch();

  return (
    <BaseEditModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Create New Tag" : "Edit Tag"}
      description={
        mode === "create" 
          ? "Enter the tag details below."
          : "Update the tag details below."
      }
      isLoading={isLoading}
      onSave={handleSubmit(onSubmit)}
      saveButtonText={mode === "create" ? "Create" : "Save"}
      canSave={isValid}
    >
      <FormField label="Name">
        <Input
          {...register("name", { required: "Name is required" })}
          placeholder="Enter tag name"
          disabled={isLoading}
        />
        {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>}
      </FormField>
      
      <FormField label="Summary">
        <Textarea
          {...register("summary", { required: "Summary is required" })}
          placeholder="Enter tag summary"
          disabled={isLoading}
          rows={3}
        />
        {errors.summary && <p className="text-sm text-red-500 mt-1">{errors.summary.message}</p>}
      </FormField>
      
      <FormField label="Color">
        <ColorInput
          value={watchedValues.color || "#5e8779"}
          onChange={(value) => reset({ ...watchedValues, color: value })}
          disabled={isLoading}
        />
        {errors.color && <p className="text-sm text-red-500 mt-1">{errors.color.message}</p>}
      </FormField>
    </BaseEditModal>
  );
}

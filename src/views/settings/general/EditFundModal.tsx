"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { api } from "~/trpc/react";
import { type Fund } from "@prisma/client";
import { Input } from "~/v2/components/ui/Input";
import { toast } from "src/components/snackbar";
import { BaseEditModal } from "./BaseEditModal";
import { FormField } from "./FormField";
import { ColorInput } from "./ColorInput";

interface EditFundModalProps {
  fund: Fund | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: 'create' | 'edit';
}

interface FundFormData {
  name: string;
  description: string;
  strategy: string;
  color: string;
}

export function EditFundModal({
  fund,
  isOpen,
  onClose,
  onSuccess,
  mode,
}: EditFundModalProps) {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<FundFormData>({
    defaultValues: {
      name: "",
      description: "",
      strategy: "",
      color: "#5e8779",
    },
    mode: "onChange",
  });

  // Reset form when modal opens/closes or fund changes
  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && fund) {
        reset({
          name: fund.name,
          description: fund.description || "",
          strategy: fund.strategy || "",
          color: fund.color || "#5e8779",
        });
      } else {
        reset({
          name: "",
          description: "",
          strategy: "",
          color: "#5e8779",
        });
      }
    }
  }, [isOpen, mode, fund, reset]);

  const createMutation = api.fund.create.useMutation({
    onSuccess: () => {
      toast.success("Fund created successfully");
      onSuccess();
      onClose();
    },
    onError: (error) => {
      toast.error(`Failed to create fund: ${error.message}`);
    },
  });

  const updateMutation = api.fund.update.useMutation({
    onSuccess: () => {
      toast.success("Fund updated successfully");
      onSuccess();
      onClose();
    },
    onError: (error) => {
      toast.error(`Failed to update fund: ${error.message}`);
    },
  });

  const onSubmit = (data: FundFormData) => {
    if (mode === "create") {
      createMutation.mutate({
        name: data.name.trim(),
        description: data.description.trim(),
        strategy: data.strategy.trim(),
        color: data.color,
      });
    } else if (mode === "edit" && fund) {
      updateMutation.mutate({
        id: fund.id,
        name: data.name.trim(),
        description: data.description.trim(),
        strategy: data.strategy.trim(),
        color: data.color,
      });
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;
  const watchedValues = watch();

  return (
    <BaseEditModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create Fund' : 'Edit Fund'}
      description={
        mode === 'create' 
          ? 'Enter the fund details below.'
          : 'Update the fund details below.'
      }
      isLoading={isLoading}
      onSave={handleSubmit(onSubmit)}
      saveButtonText={mode === "create" ? "Create" : "Save"}
      canSave={isValid}
    >
      <FormField label="Fund Name">
        <Input
          {...register("name", { required: "Fund name is required" })}
          placeholder="Enter fund name"
          disabled={isLoading}
        />
        {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>}
      </FormField>

      <FormField label="Description">
        <Input
          {...register("description", { required: "Fund description is required" })}
          placeholder="Enter fund description"
          disabled={isLoading}
        />
        {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>}
      </FormField>

      <FormField label="Strategy">
        <Input
          {...register("strategy")}
          placeholder="Enter fund strategy"
          disabled={isLoading}
        />
      </FormField>

      <FormField label="Color">
        <ColorInput
          value={watchedValues.color || "#5e8779"}
          onChange={(value) => reset({ ...watchedValues, color: value })}
          disabled={isLoading}
        />
        {errors.color && <p className="text-sm text-red-500 mt-1">{errors.color.message}</p>}
      </FormField>
    </BaseEditModal>
  );
}

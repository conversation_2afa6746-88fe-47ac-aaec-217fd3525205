"use client";

import { type ReactNode } from "react";
import { Dialog, DialogContent, DialogClose, DialogDescription, DialogTitle } from "~/v2/components/ui/Dialog";
import { Button } from "~/v2/components/ui/Button";
import { X } from "lucide-react";

interface BaseEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  isLoading: boolean;
  children: ReactNode;
  onSave: () => void;
  saveButtonText: string;
  canSave: boolean;
}

export function BaseEditModal({
  isOpen,
  onClose,
  title,
  description,
  isLoading,
  children,
  onSave,
  saveButtonText,
  canSave,
}: BaseEditModalProps) {
  const handleClose = () => {
    if (isLoading) return;
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        showCloseButton={false}
        className="w-[500px] p-3 flex flex-col"
      >
        <div className="flex flex-col h-full">
          <div className="flex flex-row items-center justify-between">
            <DialogTitle className="text-xl font-medium">
              {title}
            </DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                onClick={handleClose}
                className="w-8 h-8 p-0 relative -right-2 hover:bg-transparent"
                disabled={isLoading}
              >
                <X style={{ width: 24, height: 24 }} />
              </Button>
            </DialogClose>
          </div>

          <DialogDescription className="text-sm text-gray-500 mb-2">
            {description}
          </DialogDescription>

          <form onSubmit={(e) => { e.preventDefault(); onSave(); }} className="space-y-2 flex-1">
            {children}
            
            <div className="flex items-center justify-end mt-6 gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="default"
                disabled={isLoading || !canSave}
              >
                {isLoading ? "Saving..." : saveButtonText}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

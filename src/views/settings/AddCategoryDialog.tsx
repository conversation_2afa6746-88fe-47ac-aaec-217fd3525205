import React, { useEffect, useState } from "react";
import { Button } from "~/v2/components/ui/Button";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from "~/v2/components/ui/Dialog";
import { Input } from "~/v2/components/ui/Input";
import { Textarea } from "~/v2/components/ui/Textarea";

interface AddCategoryDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  parentOptions?: { id: string; name: string };
  onSubmit: (data: {
    name: string;
    description?: string;
    staleAfterDays?: number;
    parentId?: string;
  }) => void;
  loading: boolean;
  error: string | null;
}

const AddCategoryDialog: React.FC<AddCategoryDialogProps> = ({
  open,
  setOpen,
  parentOptions,
  onSubmit,
  loading,
  error,
}) => {
  const [form, setForm] = useState({
    name: "",
    description: "",
    staleAfterDays: "",
    parentId: "",
  });
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    if (!open) {
      setForm({ name: "", description: "", staleAfterDays: "", parentId: "" });
      setFormError(null);
    }
  }, [open]);

  const handleInput = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSelect = (value: string) => {
    setForm({ ...form, parentId: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    if (!form.name.trim()) {
      setFormError("Name is required");
      return;
    }
    onSubmit({
      name: form.name.trim(),
      description: form.description.trim() || undefined,
      staleAfterDays: form.staleAfterDays
        ? Number(form.staleAfterDays)
        : undefined,
      parentId: parentOptions?.id || undefined,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{parentOptions?.name ? `Add Checklist Item of ${parentOptions.name}` : "Add Category"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div>
            <label className="block mb-1 font-medium">
              Name <span className="text-red-500">*</span>
            </label>
            <Input
              name="name"
              value={form.name}
              onChange={handleInput}
              required
              disabled={loading}
            />
          </div>
          <div>
            <label className="block mb-1 font-medium">Description</label>
            <Textarea
              name="description"
              value={form.description}
              onChange={handleInput}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block mb-1 font-medium">Stale After Days</label>
            <Input
              name="staleAfterDays"
              type="number"
              min={0}
              value={form.staleAfterDays}
              onChange={handleInput}
              disabled={loading}
            />
          </div>
          {(formError || error) && (
            <div className="text-red-500 text-sm">{formError || error}</div>
          )}
          <DialogFooter>
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Save"}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={loading}>
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddCategoryDialog;

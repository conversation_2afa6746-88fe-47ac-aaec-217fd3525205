import type { ISocialLink } from "src/types/common";

import { useForm } from "react-hook-form";

import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import InputAdornment from "@mui/material/InputAdornment";

import { Field, Form } from "src/components/hook-form";
import { SocialIcon } from "src/components/iconify";
import { toast } from "src/components/snackbar";

// ----------------------------------------------------------------------

type Props = {
  socialLinks: ISocialLink;
};

export function AccountSocialLinks({ socialLinks }: Props) {
  const defaultValues = {
    facebook: socialLinks.facebook || "",
    instagram: socialLinks.instagram || "",
    linkedin: socialLinks.linkedin || "",
    twitter: socialLinks.twitter || "",
  };

  const methods = useForm({ defaultValues });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500));
      toast.success("Update success!");
      console.info("DATA", data);
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <Form methods={methods} onSubmit={onSubmit}>
      <Card sx={{ p: 3, gap: 3, display: "flex", flexDirection: "column" }}>
        {Object.keys(socialLinks).map((link) => (
          <Field.Text
            key={link}
            name={link}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SocialIcon width={24} icon={link} />
                </InputAdornment>
              ),
            }}
          />
        ))}

        <Button
          type="submit"
          variant="contained"
          loading={isSubmitting}
          sx={{ ml: "auto" }}
        >
          Save changes
        </Button>
      </Card>
    </Form>
  );
}

"use client";

import { use<PERSON><PERSON>back, useRef, useState } from "react";

import { useSetState } from "src/hooks/use-set-state";

import { type GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { type DDQStatus, type Org } from "@prisma/client";
import { EmptyContent } from "src/components/empty-content";
import { useTable } from "src/components/table";
import Loading from "~/app/dashboard/loading";
import { api } from "~/trpc/react";
import { DocumentTypeWithoutFolder, type IDDQFilters } from "~/types/ddq";
import { DDQDetails } from "../ddq-details";
import { DDQFilters } from "../ddq-filters";
import { DDQFiltersResult } from "../ddq-filters-result";
import DDQTable from "../DDQTable";

import { Grid2x2, List } from "lucide-react";
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";
import { But<PERSON> } from "~/v2/components/ui/Button";
import { useNavContext } from "~/v2/contexts/NavContext";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { cn } from "~/v2/lib/utils";

type Props = {
  org: Org;
};

const StatusFilterTabs = [
  { value: "all", label: "All" },
  { value: "NEW", label: "New" },
  { value: "APPROVED", label: "Approved" },
  { value: "PENDING", label: "Pending" },
  { value: "REVIEW", label: "In Review" },
];

export function DDQListView({ org }: Props) {
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(
    null,
  );

  const [rowSelectionModel, setRowSelectionModel] =
    useState<GridRowSelectionModel>([]);

  const { currentTab: ddqStatusFilter } = useNavContext();

  const filters = useSetState<IDDQFilters>({
    name: "",
    fileTypes: [],
    investors: [],
  });

  const documents = api.document.getAllDDQs.useQuery(
    {
      nameFilter: filters.state.name,
      fileType: filters.state.fileTypes,
      // investors: filters.state.investors,
      status:
        ddqStatusFilter === "all"
          ? undefined
          : ([ddqStatusFilter] as DDQStatus[]),
    },
    {
      enabled: !!org.id,
    },
  );

  const table = useTable({ defaultRowsPerPage: 10 });

  const [view, setView] = useState("grid");

  const canReset = !!filters.state.name || filters.state.fileTypes.length > 0;

  const handleChangeView = useCallback((newView: string) => {
    setView(newView);
  }, []);

  const renderFiltersAndViewToggle = (
    <div
      className={`flex w-full items-center justify-between pt-8 gap-[8px] ${overrideMuiTheme}`}
    >
      {/* Filters/search */}
      <div className="flex-1 min-w-0 flex-end">
        <DDQFilters
          filters={filters}
          onResetPage={table.onResetPage}
          options={{ types: Object.values(DocumentTypeWithoutFolder) }}
        />
      </div>
      {/* View toggle */}
      <div className="flex gap-[2px]">
        <Button
          variant="ghost"
          onClick={() => handleChangeView("list")}
          className={cn(
            "p-[10px] flex items-center justify-center rounded-[8px] transition-colors shadow-none cursor-pointer",
            view === "list"
              ? "bg-primary/8"
              : "bg-transparent hover:bg-gray-50",
          )}
          aria-label="List view"
        >
          <List className="w-[16px] h-[16px]" />
        </Button>
        <Button
          variant="ghost"
          onClick={() => handleChangeView("grid")}
          className={cn(
            "p-[10px] flex items-center justify-center rounded-[8px] transition-colors shadow-none cursor-pointer",
            view === "grid"
              ? "bg-primary/8"
              : "bg-transparent hover:bg-gray-50",
          )}
          aria-label="Grid view"
        >
          <Grid2x2 className="w-[16px] h-[16px]" />
        </Button>
      </div>
    </div>
  );

  // Add new ref for filter results
  const filterResultsRef = useRef<HTMLDivElement>(null);

  const renderResults = (
    <div ref={filterResultsRef} className="pt-2">
      <DDQFiltersResult
        filters={filters}
        totalResults={documents.data?.length ?? 0}
        onResetPage={table.onResetPage}
        aria-label="filter-results"
        isLoading={documents.isLoading}
      />
    </div>
  );

  const ShowAllDDQsGrid = () => {
    return documents.isLoading ? (
      <Loading />
    ) : documents.data?.length === 0 ? (
      <EmptyContent title="No documents found" sx={{ py: 10 }} />
    ) : (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-[24px]">
        {documents.data?.map((document, idx) => (
          <div key={document.id}>
            <DDQDetails
              key={document.id}
              document={document}
              isLoading={documents.isLoading}
              selected={selectedDocumentId === document.id}
              setSelectedDocumentId={setSelectedDocumentId}
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      <DashboardHeader title="DDQ Manager" tabs={StatusFilterTabs}>
        {renderFiltersAndViewToggle}
        {/* {canReset && renderResults} */}
      </DashboardHeader>
      <DashboardContent>
        {view === "grid" && <ShowAllDDQsGrid />}
        {view === "list" && (
          <DDQTable
            rowSelectionModel={rowSelectionModel}
            setRowSelectionModel={setRowSelectionModel}
            documents={documents.data}
            loading={documents.isLoading}
          />
        )}
      </DashboardContent>
    </>
  );
}

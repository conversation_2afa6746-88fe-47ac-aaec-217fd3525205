import type { UseSetStateReturn } from "src/hooks/use-set-state";

import { useCallback } from "react";

import Chip from "@mui/material/Chip";

import { fDateRangeShortLabel } from "src/utils/format-time";

import {
  chipProps,
  FiltersBlock,
  FiltersResult,
} from "src/components/filters-result";
import { DocumentTypeWithoutFolder, IDDQFilters } from "~/types/ddq";

type Props = {
  totalResults: number;
  onResetPage: () => void;
  filters: UseSetStateReturn<IDDQFilters>;
  isLoading?: boolean;
};

export function DDQFiltersResult({
  filters,
  onResetPage,
  totalResults,
  isLoading,
}: Props) {
  const handleRemoveKeyword = useCallback(() => {
    onResetPage();
    filters.setState({ name: "" });
  }, [filters, onResetPage]);

  const handleRemoveTypes = useCallback(
    (inputValue: DocumentTypeWithoutFolder) => {
      const newValue = filters.state.fileTypes.filter(
        (item) => item !== inputValue,
      );

      onResetPage();
      filters.setState({ fileTypes: newValue });
    },
    [filters, onResetPage],
  );

  const handleReset = useCallback(() => {
    onResetPage();
    filters.onResetState();
  }, [filters, onResetPage]);

  return (
    <FiltersResult
      totalResults={totalResults}
      onReset={handleReset}
      isLoading={isLoading}
    >
      <FiltersBlock label="Types:" isShow={!!filters.state.fileTypes.length}>
        {filters.state.fileTypes.map((item) => (
          <Chip
            {...chipProps}
            key={item}
            label={item}
            onDelete={() => handleRemoveTypes(item)}
            sx={{ textTransform: "capitalize" }}
          />
        ))}
      </FiltersBlock>

      <FiltersBlock label="Keyword:" isShow={!!filters.state.name}>
        <Chip
          {...chipProps}
          label={filters.state.name}
          onDelete={handleRemoveKeyword}
        />
      </FiltersBlock>
    </FiltersResult>
  );
}

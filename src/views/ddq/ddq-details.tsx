import { DDQStatus } from "@prisma/client";
import { EllipsisVertical } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";
import { FileThumbnail } from "~/components/file-thumbnail";
import { Iconify } from "~/components/iconify";
import type { DDQMetadataType } from "~/lib/types";
import { type GetDDQsType } from "~/server/api/routers/document";
import { api } from "~/trpc/react";
import { fDateTime, formatStr } from "~/utils/format-time";
import { Calendar } from "~/v2/components/ui/Calendar";
import {
  Card,
  CardAction,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/v2/components/ui/Card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import { Progress } from "~/v2/components/ui/Progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/v2/components/ui/Select";
import { Separator } from "~/v2/components/ui/Separator";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/v2/components/ui/Tooltip";
import { Typography } from "~/v2/components/ui/Typography";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { cn, getFilename } from "~/v2/lib/utils";
import { ViewSharepointDocument, SharepointDocumentHandler } from "~/views/data-room/FileExplorer";
import { Box, IconButton, Menu, MenuItem, Modal } from "@mui/material";
import { useGetAssignedUsersPerDocument } from "~/components/virgil/OfficeAddIn/UserCollaboration/useGetAssignedUsersPerDocument";
import { Initials } from "~/components/virgil/OfficeAddIn/UserCollaboration/initials";
import { Skeleton } from "~/v2/components/ui/Skeleton";

export type MockDDQData = {
  status: DDQStatus;
  progress: number;
  dueDate: string;
  investor: string;
  stakeholders: string[];
  unassignedQuestions: number;
};

type Props = {
  document: GetDDQsType;
  selected: boolean;
  setSelectedDocumentId: (id: string) => void;
  isLoading: boolean;
};

export function DDQDetails({
  document,
  isLoading,
  selected,
  setSelectedDocumentId,
}: Props) {
  const documentSummary = document?.summary ?? "No summary provided";

  const updateDDQ = api.document.updateDDQ.useMutation();

  const baseName = getFilename(document.name);

  const ddqMetadata: DDQMetadataType | undefined =
    typeof document?.DDQMetadata?.metadata === "string"
      ? (JSON.parse(document?.DDQMetadata?.metadata) as DDQMetadataType)
      : (document?.DDQMetadata?.metadata as DDQMetadataType | undefined);

  const { users: assignedUsers, isLoading: isAssignedLoading } = useGetAssignedUsersPerDocument({ documentId: document.id });

  // Add state for date picker
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Context menu state for actions
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [openDocument, setOpenDocument] = useState(false);

  // Handlers for the actions
  const handleOpenDocument = () => {
    setOpenDocument(true);
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Only show context menu if the document is a Sharepoint document
    if (document && document.source === "SHAREPOINT") {
      setContextMenu(
        contextMenu === null
          ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
          : null,
      );
    }
  };

  const handleClose = () => {
    setContextMenu(null);
  };

  const DDQCardHeader = () => {
    return (
      <CardHeader className="pt-[12px] px-[16px] flex items-end justify-between">
        <CardTitle className="overflow-hidden">
          <div className="flex items-start gap-[4px] w-full overflow-hidden">
            <FileThumbnail
              file={document.name}
              sx={{ width: 16, height: 16, minWidth: 16, minHeight: 16 }}
            />
            <Tooltip>
              <TooltipTrigger className="overflow-hidden">
                <Typography
                  noWrap
                  className="min-w-0 max-w-[100%] overflow-hidden text-ellipsis whitespace-nowrap block font-medium text-sm leading-[20px]"
                >
                  {baseName}
                </Typography>
              </TooltipTrigger>
              <TooltipContent>
                <p>{document.name}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </CardTitle>
        <CardAction>
          {document.source === "SHAREPOINT" && (
            <>
              <IconButton
                size="small"
                onClick={handleContextMenu}
                sx={{ padding: 0.5 }}
              >
                <Iconify icon="eva:more-vertical-fill" width={16} />
              </IconButton>
              <Menu
                open={contextMenu !== null}
                onClose={handleClose}
                anchorReference="anchorPosition"
                anchorPosition={
                  contextMenu !== null
                    ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
                    : undefined
                }
                slotProps={{
                  root: {
                    onContextMenu: (event: React.MouseEvent) => {
                      event.preventDefault();
                      handleClose();
                    },
                  },
                }}
              >
                <MenuItem
                  onClick={() => {
                    handleOpenDocument();
                    handleClose();
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Iconify icon="mdi:file-outline" />
                    <Typography className="!text-[10px]">Open File</Typography>
                  </Box>
                </MenuItem>
                <MenuItem
                  onClick={() => {
                    handlePreview();
                    handleClose();
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Iconify icon="mdi:magnify" />
                    <Typography className="!text-[10px]">Preview</Typography>
                  </Box>
                </MenuItem>
              </Menu>
            </>
          )}
        </CardAction>
      </CardHeader>
    );
  };

  const DDQCardContent = () => {
    const totalQuestions = ddqMetadata?.totalNumberOfQuestions ?? 0;
    const answeredQuestions = 0; // TODO: get answered questions from the document (?)

    return (
      <CardContent className="px-[16px]">
        {/* Summary */}
        <div className="min-h-[48px] flex items-start">
          <Typography className="text-text-secondary font-sans text-xs font-normal leading-4 line-clamp-3">
            {documentSummary}
          </Typography>
        </div>

        {/* Progress */}
        <div className="flex flex-col gap-[8px] py-[8px]">
          <Progress
            value={
              totalQuestions ? (answeredQuestions / totalQuestions) * 100 : 0
            }
          />
          <Typography className="text-text-secondary font-sans text-xs font-normal leading-4">
            Current Progress: {answeredQuestions}/{totalQuestions} questions
            answered
          </Typography>
        </div>

        <div className="grid grid-cols-3 gap-[8px] pt-[8px] w-full">
          {/* Due Date */}
          <div>
            <div className="font-semibold text-[12px] leading-[16px]">
              Due Date
            </div>
            <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <PopoverTrigger asChild>
                <div
                  className="text-text-secondary text-[12px] leading-[16px] mt-[4px] cursor-pointer hover:text-foreground/80"
                  onClick={() => setIsDatePickerOpen(true)}
                >
                  {document.dueDate
                    ? fDateTime(document.dueDate, formatStr.split.date)
                    : "No due date"}
                </div>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto overflow-hidden p-0"
                align="start"
              >
                <div className={overrideMuiTheme}>
                  <Calendar
                    mode="single"
                    selected={
                      document.dueDate ? new Date(document.dueDate) : undefined
                    }
                    captionLayout="dropdown"
                    onSelect={(date) => {
                      updateDDQ.mutate({
                        id: document.id,
                        dueDate: date ? new Date(date) : undefined,
                      });
                      setIsDatePickerOpen(false);
                    }}
                  />
                </div>
              </PopoverContent>
            </Popover>
          </div>
          {/* Investor. Uncomment when implemented in backend */}
          {/* <div>
            <div className="font-semibold text-[12px] leading-[16px]">
              Investor
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="text-text-secondary text-[12px] leading-[16px] mt-[4px] truncate max-w-[180px]"
                  style={{
                    cursor: ddqMetadata?.investorAndFundName?.investor
                      ? "pointer"
                      : "default",
                  }}
                  tabIndex={ddqMetadata?.investorAndFundName?.investor ? 0 : -1}
                >
                  {ddqMetadata?.investorAndFundName?.investor || "-"}
                </div>
              </TooltipTrigger>
              {ddqMetadata?.investorAndFundName?.investor && (
                <TooltipContent>
                  <span>{ddqMetadata.investorAndFundName.investor}</span>
                </TooltipContent>
              )}
            </Tooltip>
          </div> */}
          {/* Collaborators */}
          <div>
            <div className="font-semibold text-[12px] leading-[16px]">
              Collaborators
            </div>
            {isAssignedLoading ? (
              <Typography className="text-text-secondary text-[12px] leading-[16px] mt-[4px]">
                Loading...
              </Typography>
            ) : (
              <div className="flex items-center h-[24px] mt-[4px]">
                {(assignedUsers || [])
                  .slice(0, 3)
                  .map((user, idx) => (
                    <Tooltip key={user.id}>
                      <TooltipTrigger asChild>
                        <span
                          key={user.id}
                          className={cn(
                            "cursor-pointer inline-flex items-center justify-center w-[24px] h-[24px] rounded-full bg-muted border-[2px] border-white text-foreground text-[12px] font-normal",
                            idx === 0 ? "ml-0" : "ml-[-8px]",
                          )}
                        >
                          {Initials({ name: user.name ?? '' })}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="top" align="center" className="z-1600">
                        {user.name || 'Unknown User'}
                      </TooltipContent>
                    </Tooltip>
                  )
                  )}
                {Array.isArray(assignedUsers) &&
                  assignedUsers.length > 3 && (
                    <span className="ml-[4px] text-[12px] text-text-secondary font-normal">
                      +{assignedUsers.length - 3}
                    </span>
                  )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    );
  };

  const DDQCardFooter = () => {
    const statusMap = {
      [DDQStatus.NEW]: "NEW",
      [DDQStatus.PENDING]: "IN PROGRESS",
      [DDQStatus.REVIEW]: "IN REVIEW",
      [DDQStatus.APPROVED]: "APPROVED",
    };

    return (
      <CardFooter className="px-[16px] pb-[8px]">
        <div className="flex items-center justify-between w-full">
          {document.ddqStatus && (
            <Select
              onValueChange={(value) => {
                updateDDQ.mutate(
                  { id: document.id, ddqStatus: value as DDQStatus },
                  {
                    onSuccess: () => {
                      toast.success("DDQ status updated");
                    },
                    onError: () => {
                      toast.error("Failed to update DDQ status");
                    },
                  },
                );
              }}
            >
              <SelectTrigger
                size="custom"
                className={cn(
                  "h-[20px] p-[4px] gap-[7px] border-none rounded-[4px] shadow-none !text-[10px] !leading-[16px] !font-semibold",
                  // document.ddqStatus === DDQStatus.NEW &&
                  //   "bg-gray-100 !text-neutral-700 [&_svg]:!text-neutral-700 [&_svg]:!opacity-100",
                  document.ddqStatus === DDQStatus.PENDING &&
                  "bg-orange-50 !text-orange-800 [&_svg]:!text-orange-800 [&_svg]:!opacity-100",
                  document.ddqStatus === DDQStatus.REVIEW &&
                  "bg-yellow-100 !text-yellow-800 [&_svg]:!text-yellow-800 [&_svg]:!opacity-100",
                  document.ddqStatus === DDQStatus.APPROVED &&
                  "bg-green-50 !text-green-800 [&_svg]:!text-green-800 [&_svg]:!opacity-100",
                )}
              >
                <SelectValue
                  // placeholder={statusMap[document.ddqStatus] ?? statusMap.NEW}
                  placeholder={
                    statusMap[document.ddqStatus] ??
                    statusMap[DDQStatus.PENDING]
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {Object.values(DDQStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {statusMap[status]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          <Typography className="text-text-secondary text-[12px] leading-[20px]">
            {`Updated ${fDateTime(document.updatedAt, formatStr.split.date)}`}
          </Typography>
        </div>
      </CardFooter>
    );
  };

  return (
    <>
      <Card
        className={`${overrideMuiTheme} p-0 gap-[8px] rounded-sm shadow-sm border-none`}
      >
        <DDQCardHeader />
        <Separator className="my-0" />
        <DDQCardContent />
        <Separator className="my-0" />
        <DDQCardFooter />
      </Card>

      {/* Modals for document actions */}
      <Modal
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        aria-labelledby="preview-modal-title"
        aria-describedby="preview-modal-description"
      >
        <ViewSharepointDocument documentId={document.id} modal={true} />
      </Modal>
      <SharepointDocumentHandler
        row={document as any}
        openDocument={openDocument}
        setOpenDocument={setOpenDocument}
      />
    </>
  );
}

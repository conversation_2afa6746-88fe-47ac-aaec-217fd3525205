import type { UseSetStateReturn } from "src/hooks/use-set-state";

import { useCallback, useEffect, useState } from "react";

import Image from "next/image";
import { fileThumb } from "src/components/file-thumbnail";
import { Iconify } from "src/components/iconify";
import { useDebounce } from "src/hooks/use-debounce";
import type { DocumentTypeWithoutFolder, IDDQFilters } from "~/types/ddq";
import {
  MultiSelect,
  MultiSelectContent,
  MultiSelectItem,
  MultiSelectList,
  MultiSelectTrigger,
  MultiSelectValues,
} from "~/v2/components/composit/MultiSelect/MultiSelect";
import { Input } from "~/v2/components/ui/Input";
import { fileThumbnailClasses } from "../../components/file-thumbnail/classes";
import { ChevronsUpDown } from "lucide-react";

type Props = {
  onResetPage: () => void;
  filters: UseSetStateReturn<IDDQFilters>;
  options: {
    types: DocumentTypeWithoutFolder[];
  };
};

export function DDQFilters({ filters, options, onResetPage }: Props) {
  const [searchValue, setSearchValue] = useState(filters.state.name);
  // const [selectedInvestors, setSelectedInvestors] = useState<string[]>(
  //   filters.state.investors,
  // );

  const debouncedSearchValue = useDebounce(searchValue, 500);

  useEffect(() => {
    if (debouncedSearchValue !== filters.state.name) {
      onResetPage();
      filters.setState({ name: debouncedSearchValue });
    }
  }, [debouncedSearchValue, filters, onResetPage]);

  const handleFilterQuestion = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
    },
    [],
  );

  const handleChangeFileType = useCallback((newValue: string[]) => {
    filters.setState({ fileTypes: newValue as DocumentTypeWithoutFolder[] });
  }, []);

  return (
    <div className="flex flex-1 flex-col md:flex-row justify-between items-end md:items-center md:space-y-0 md:space-x-1 w-full">
      <div className="relative w-full md:w-80 flex-1">
        <Iconify
          icon="eva:search-fill"
          className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground pointer-events-none"
        />
        <Input
          value={searchValue}
          onChange={handleFilterQuestion}
          placeholder="Search by file name"
          className="pl-10 bg-white"
        />
      </div>

      <div className="flex items-center">
        <MultiSelect
          options={options.types.map((type) => ({
            value: type,
            label: type,
          }))}
          selected={filters.state.fileTypes}
          onClose={handleChangeFileType}
        >
          <MultiSelectTrigger>
            <MultiSelectValues placeholder="File types" />
          </MultiSelectTrigger>
          <MultiSelectContent>
            <MultiSelectList>
              {options.types.map((type) => (
                <MultiSelectItem key={type} value={type}>
                  <div className="flex items-center gap-2">
                    <Image
                      alt={type}
                      src={fileThumb(type)}
                      width={16}
                      height={16}
                      className={`${fileThumbnailClasses.icon}`}
                    />
                    <span>{type}</span>
                  </div>
                </MultiSelectItem>
              ))}
            </MultiSelectList>
          </MultiSelectContent>
        </MultiSelect>
      </div>
    </div>
  );
}

import { Box, IconButton, <PERSON>u, MenuItem, Modal, type SelectChangeEvent, useTheme } from "@mui/material";

import { DDQStatus, Tag, type Document } from "@prisma/client";

import {
  DataGridPremium,
  type DataGridPremiumProps,
  type GridColDef,
  type GridRenderCellParams,
  type GridRowModel,
  type GridRowSelectionModel,
  type GridRowsProp,
  gridFilteredDescendantCountLookupSelector,
  useGridApiContext,
  useGridApiRef,
  useGridSelector,
} from "@mui/x-data-grid-premium";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import { ChevronDownIcon } from "lucide-react";
import { useEffect, useMemo, useState, useRef, useLayoutEffect } from "react";
import { toast } from "sonner";
import { EmptyContent } from "~/components/empty-content";
import { FileThumbnail } from "~/components/file-thumbnail";
import { Iconify } from "~/components/iconify";
import type { DDQMetadataType } from "~/lib/types";
import { type GetDDQsType } from "~/server/api/routers/document";
import { api } from "~/trpc/react";
import { fDateTime, formatStr } from "~/utils/format-time";
import { ViewSharepointDocument, SharepointDocumentHandler } from "~/views/data-room/FileExplorer";
import { Button } from "~/v2/components/ui/Button";
import { Calendar } from "~/v2/components/ui/Calendar";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/v2/components/ui/Select";
import { Typography } from "~/v2/components/ui/Typography";
import { cn, getFilename } from "~/v2/lib/utils";
import { Badge } from "../../v2/components/ui/Badge";

type Props = {
  documents?: GetDDQsType[];
  loading?: boolean;
  rowSelectionModel: GridRowSelectionModel;
  setRowSelectionModel: (model: GridRowSelectionModel) => void;
};

function DDQDueDateSelectEditInputCell(props: GridRenderCellParams) {
  const { id, value, field } = props;
  const apiRef = useGridApiContext();

  const handleChange = async (value: Date | undefined) => {
    await apiRef.current.setEditCellValue({
      id,
      field,
      value,
    });
    apiRef.current.stopCellEditMode({ id, field });
  };

  console.log("value", value);

  return (
    <DatePicker
      value={dayjs(value)}
      onChange={(date) => handleChange(date?.toDate())}
      slotProps={{
        textField: {
          size: "small",
          sx: { width: "100%" },
        },
      }}
    />
  );
}

function DDQStatusSelectEditInputCell(props: GridRenderCellParams) {
  const { id, value, field, row } = props;
  const apiRef = useGridApiContext();
  const theme = useTheme();
  const updateDDQ = api.document.updateDDQ.useMutation();

  // Typesafe ddqMetadata extraction (matches grid logic)
  const ddqMetadata =
    typeof row?.DDQMetadata?.metadata === "string"
      ? JSON.parse(row.DDQMetadata.metadata)
      : row?.DDQMetadata?.metadata;

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
      }}
    >
      <Select
        value={value}
      >
        <MenuItem value={DDQStatus.PENDING}>Pending</MenuItem>
        <MenuItem value={DDQStatus.REVIEW}>In Review</MenuItem>
        <MenuItem value={DDQStatus.APPROVED}>Approved</MenuItem>
      </Select>
    </div>
  );
}

const renderDDQStatusSelectEditInputCell: GridColDef["renderCell"] = (
  params,
) => {
  return <DDQStatusSelectEditInputCell {...params} />;
};

const getTreeDataPath: DataGridPremiumProps["getTreeDataPath"] = (
  row: Document,
) => {
  const arr = row.name.split("/");
  if (arr.length > 1) {
    arr.shift();
  }
  return arr;
};

function CustomGridTreeDataGroupingCell(props: GridRenderCellParams) {
  const { id, field, rowNode } = props;
  const apiRef = useGridApiContext();
  const filteredDescendantCountLookup = useGridSelector(
    apiRef,
    gridFilteredDescendantCountLookupSelector,
  );
  const filteredDescendantCount =
    filteredDescendantCountLookup[rowNode.id] ?? 0;

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (rowNode.type !== "group") {
      return;
    }

    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };

  return (
    <Box
      sx={{ ml: rowNode.depth * 4, cursor: "pointer" }}
      onClick={handleClick}
    >
      <div>
        {filteredDescendantCount > 0 ? (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FileThumbnail file="folder" sx={{ width: 20, height: 20 }} />{" "}
            <span style={{ marginLeft: 2, marginRight: 8 }}>
              {/* @ts-ignore */}
              <b>{rowNode.groupingKey}</b>
            </span>
          </Box>
        ) : (
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <FileThumbnail
              file={String(props.row.name)}
              sx={{ width: 20, height: 20 }}
            />
            <span style={{ marginLeft: 2, marginRight: 8 }}>
              <b>{getFilename(props.row.name)}</b>
            </span>
          </Box>
        )}
      </div>
    </Box>
  );
}

const groupingColDef: DataGridPremiumProps["groupingColDef"] = {
  headerName: "Files",
  minWidth: 450,
  renderCell: (params) => <CustomGridTreeDataGroupingCell {...params} />,
};

function ActionsCell(props: GridRenderCellParams & {
  onOpenDocument?: (rowId: string) => void;
  onPreview?: (rowId: string) => void;
}) {
  const { row, onOpenDocument, onPreview } = props;
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);

  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Only show context menu if the row is a Sharepoint document
    if (row && row.source === "SHAREPOINT") {
      setContextMenu(
        contextMenu === null
          ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
          : null,
      );
    }
  };

  const handleClose = () => {
    setContextMenu(null);
  };

  // Only render the button for Sharepoint documents
  if (row.source !== "SHAREPOINT") {
    return null;
  }

  return (
    <>
      <IconButton
        size="small"
        onClick={handleClick}
        sx={{ padding: 0.5 }}
      >
        <Iconify icon="eva:more-vertical-fill" width={16} />
      </IconButton>
      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        slotProps={{
          root: {
            onContextMenu: (event: React.MouseEvent) => {
              event.preventDefault();
              handleClose();
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            onOpenDocument?.(row.id);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:file-outline" />
            <Typography className="!text-[10px]">Open File</Typography>
          </Box>
        </MenuItem>
        <MenuItem
          onClick={() => {
            onPreview?.(row.id);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:magnify" />
            <Typography className="!text-[10px]">Preview</Typography>
          </Box>
        </MenuItem>
      </Menu>
    </>
  );
}

const createActionsCellRenderer = (onOpenDocument: (rowId: string) => void, onPreview: (rowId: string) => void) => {
  return (params: GridRenderCellParams) => {
    return <ActionsCell {...params} onOpenDocument={onOpenDocument} onPreview={onPreview} />;
  };
};

// TagSummary: always show the first tag (if any), then '+N' badge if more
function TagSummary({ tags = [] }: { tags?: Tag[] }) {
  if (tags.length === 0) return null;
  return (
    <div className="leading-[16px] mt-[8px] flex flex-row items-center gap-[4px] min-w-0 max-w-full overflow-hidden">
      <Badge variant="outline" className="text-xs">
        {tags[0]?.name}
      </Badge>
      {tags.length > 1 && (
        <Badge variant="outline" className="text-xs">+{tags.length - 1}</Badge>
      )}
    </div>
  );
}

export default function DDQTable({
  documents,
  loading,
  rowSelectionModel,
  setRowSelectionModel,
}: Props) {
  const [rows, setRows] = useState<GridRowsProp>(documents || []);
  const apiRef = useGridApiRef();

  // for the due date picker - track open state per row
  const [openDatePickers, setOpenDatePickers] = useState<
    Record<string, boolean>
  >({});

  // Context menu state for actions
  const [selectedRow, setSelectedRow] = useState<string | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [openDocument, setOpenDocument] = useState(false);

  // Handlers for the actions column
  const handleOpenDocument = (rowId: string) => {
    setSelectedRow(rowId);
    setOpenDocument(true);
  };

  const handlePreview = (rowId: string) => {
    setSelectedRow(rowId);
    setPreviewOpen(true);
  };

  const updateDDQ = api.document.updateDDQ.useMutation();

  useEffect(() => {
    const rowIds = apiRef.current.getAllRowIds();
    const newRowIds = documents?.map((doc) => doc.id);

    const difference = rowIds?.filter(
      (id) => !newRowIds?.includes(id as string),
    );

    try {
      // Remove rows that have been deleted
      if (difference.length > 0) {
        apiRef.current.updateRows(
          difference.map((id) => ({
            id: id,
            _action: "delete",
          })),
        );
      }
    } catch (error) {
      console.error("Error updating rows", error);
    }

    // This can be made more efficient by only updating the rows that have changed
    const updatedRows =
      documents?.map((doc) => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        size: doc.size,
        status: doc.status,
        ddqStatus: doc.ddqStatus,
        dueDate: doc.dueDate,
        investor: doc.DDQMetadata?.metadata
          ? typeof doc.DDQMetadata?.metadata === "string"
            ? JSON.parse(doc.DDQMetadata?.metadata)?.investorAndFundName
              ?.investor
            : (doc.DDQMetadata?.metadata as any)?.investorAndFundName?.investor
          : null,
        DDQMetadata: doc.DDQMetadata,
        updatedAt: doc.updatedAt,
        source: doc.source,
      })) || [];

    apiRef.current.updateRows(updatedRows);
  }, [documents]);

  const processRowUpdate = async (
    newRow: GridRowModel,
    oldRow: GridRowModel,
  ) => {
    console.log(newRow, oldRow);

    return new Promise((resolve, reject) => {
      updateDDQ.mutate(
        {
          id: newRow.id,
          dueDate: newRow.dueDate ? new Date(newRow.dueDate) : undefined,
          ddqStatus: newRow.ddqStatus,
        },
        {
          onSuccess: (data) => {
            resolve(data);
          },
          onError: (error) => {
            console.error(error);
            resolve(oldRow);
          },
        },
      );
    });
  };

  const parseDDQMetadata = (ddqMetadata: any): DDQMetadataType | undefined => {
    return typeof ddqMetadata === "string"
      ? (JSON.parse(ddqMetadata) as DDQMetadataType)
      : (ddqMetadata as DDQMetadataType | undefined);
  };

  const columns: GridColDef[] = useMemo(
    () => [
      {
        field: "id",
      },
      {
        field: "name",
        headerName: "Name",
        width: 250,
        flex: 1,
        renderCell: (params) => {
          // Extract metadata and summary
          const row = params.row as GetDDQsType;
          const ddqMetadata = parseDDQMetadata(row.DDQMetadata?.metadata);
          const totalQuestions = ddqMetadata?.totalNumberOfQuestions ?? 0;
          const answeredQuestions = 0; // TODO: get answered questions from the document (?)
          const summary =
            params.row.summary || ddqMetadata?.summary || "No summary provided2";
          const fileName = getFilename(params.row.name);
          return (
            <div className="flex flex-row items-center w-full min-w-0 py-[8px]">
              <FileThumbnail
                file={fileName}
                sx={{ width: 20, height: 20, minWidth: 20, minHeight: 20 }}
              />
              <div className="flex flex-col items-start w-full min-w-0 ml-1.5">
                {/* File name with ellipsis truncation */}
                <span
                  className="mr-2 font-medium text-[12px] text-foreground min-w-0 max-w-full w-full overflow-hidden text-ellipsis whitespace-nowrap inline-block leading-[20px]"
                  title={fileName}
                >
                  {fileName}
                </span>
                {/* Progress text */}
                <Typography className="text-[12px] text-muted-foreground leading-[20px]">
                  {answeredQuestions}/{totalQuestions} answered
                </Typography>
                {/* Summary, truncated */}
                <Typography
                  className="text-[12px] text-muted-foreground leading-[20px] max-w-full w-full overflow-hidden text-ellipsis whitespace-nowrap block"
                  title={summary}
                >
                  {summary}
                </Typography>
              </div>
            </div>
          );
        },
      },
      {
        field: "ddqStatus",
        headerName: "Status",
        width: 200,
        renderCell: (params) => {
          const tags = params.row.tags;
          const status = params.row.ddqStatus as DDQStatus;
          const statusMap = {
            [DDQStatus.NEW]: "NEW",
            [DDQStatus.PENDING]: "IN PROGRESS",
            [DDQStatus.REVIEW]: "IN REVIEW",
            [DDQStatus.APPROVED]: "APPROVED",
          };
          return (
            <div className="pt-[14px] pb-[14px]">
              <Select
                onValueChange={(value) => {
                  updateDDQ.mutate(
                    { id: params.row.id, ddqStatus: value as DDQStatus },
                    {
                      onSuccess: () => {
                        toast.success("DDQ status updated");
                      },
                      onError: () => {
                        toast.error("Failed to update DDQ status");
                      },
                    },
                  );
                }}
              >
                <SelectTrigger
                  size="custom"
                  className={cn(
                    "h-[20px] p-[4px] gap-[7px] border-none rounded-[4px] shadow-none !text-[10px] !leading-[16px] !font-semibold [&>svg]:w-[16px] [&>svg]:h-[16px]",
                    // status === DDQStatus.NEW &&
                    //   "bg-gray-100 !text-neutral-700 [&_svg]:!text-neutral-700 [&_svg]:!opacity-100",
                    status === DDQStatus.PENDING &&
                    "bg-orange-50 !text-orange-800 [&_svg]:!text-orange-800 [&_svg]:!opacity-100",
                    status === DDQStatus.REVIEW &&
                    "bg-yellow-100 !text-yellow-800 [&_svg]:!text-yellow-800 [&_svg]:!opacity-100",
                    status === DDQStatus.APPROVED &&
                    "bg-green-50 !text-green-800 [&_svg]:!text-green-800 [&_svg]:!opacity-100",
                  )}
                >
                  <SelectValue
                    // placeholder={statusMap[status] ?? statusMap.NEW}
                    placeholder={
                      statusMap[status] ?? statusMap[DDQStatus.PENDING]
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(DDQStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {statusMap[status]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <TagSummary tags={tags ?? []} />
            </div>
          );
        },
      },
      {
        field: "investor",
        headerName: "Investor",
        width: 150,
      },
      {
        field: "dueDate",
        headerName: "Due Date",
        // editable: true,
        // renderEditCell: renderDDQDueDateSelectEditInputCell,
        width: 180,
        renderCell: (params) => {
          const rowId = params.row.id as string;
          const isOpen = openDatePickers[rowId] || false;

          return (
            <div className="flex flex-col justify-center h-full text-left">
              <Popover
                open={isOpen}
                onOpenChange={(open) => {
                  setOpenDatePickers((prev) => ({
                    ...prev,
                    [rowId]: open,
                  }));
                }}
              >
                <PopoverTrigger asChild>
                  <div
                    className="text-[12px] leading-[20px] text-foreground font-medium cursor-pointer hover:text-foreground/80"
                    onClick={() => {
                      setOpenDatePickers((prev) => ({
                        ...prev,
                        [rowId]: true,
                      }));
                    }}
                  >
                    {params.row.dueDate
                      ? fDateTime(params.row.dueDate, formatStr.split.date)
                      : "No due date"}
                  </div>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto overflow-hidden p-0"
                  align="start"
                >
                  <div className={overrideMuiTheme}>
                    <Calendar
                      mode="single"
                      selected={
                        params.row.dueDate
                          ? new Date(params.row.dueDate)
                          : undefined
                      }
                      captionLayout="dropdown"
                      onSelect={(date) => {
                        // when a due date is selected, call the mutation to update the due date
                        updateDDQ.mutate(
                          {
                            id: params.row.id,
                            dueDate: date ? new Date(date) : undefined,
                          },
                          {
                            onSuccess: () => {
                              toast.success("Due date updated");
                            },
                            onError: () => {
                              toast.error("Failed to update due date");
                            },
                          },
                        );
                        setOpenDatePickers((prev) => ({
                          ...prev,
                          [rowId]: false,
                        }));
                      }}
                    />
                  </div>
                </PopoverContent>
              </Popover>

              {params.row.updatedAt && (
                <div className="text-[12px] leading-[20px] text-text-secondary mt-[4px]">
                  Last modified:{" "}
                  {fDateTime(params.row.updatedAt, formatStr.split.date)}
                </div>
              )}
            </div>
          );
        },
      },
      {
        field: "collaborators",
        headerName: "Collaborators",
        width: 150,
        renderCell: (params) => {
          const ddqMetadata =
            typeof params.row.DDQMetadata?.metadata === "string"
              ? JSON.parse(params.row.DDQMetadata?.metadata)
              : params.row.DDQMetadata?.metadata;
          return (
            <div className="flex items-center justify-end h-full w-full">
              {(ddqMetadata?.listOfIndividualAuthors || [])
                .slice(0, 3)
                .map((name: string, idx: number) => {
                  const initials = name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase();
                  return (
                    <span
                      key={name}
                      className={cn(
                        "inline-flex items-center justify-center w-[24px] h-[24px] rounded-full bg-muted border-[2px] border-white text-foreground text-[12px] font-normal",
                        idx === 0 ? "ml-0" : "ml-[-8px]",
                      )}
                    >
                      {initials}
                    </span>
                  );
                })}
              {Array.isArray(ddqMetadata?.listOfIndividualAuthors) &&
                ddqMetadata.listOfIndividualAuthors.length > 3 && (
                  <span className="ml-[4px] text-[12px] text-text-secondary font-normal">
                    +{ddqMetadata.listOfIndividualAuthors.length - 3}
                  </span>
                )}
            </div>
          );
        },
      },
      {
        field: "actions",
        headerName: "Actions",
        width: 80,
        sortable: false,
        filterable: false,
        renderCell: createActionsCellRenderer(handleOpenDocument, handlePreview),
      },
    ],
    [updateDDQ, handleOpenDocument, handlePreview],
  );

  return (
    <div className="flex-1">
      {/* {rows.length > 0 ? ( */}
      <DataGridPremium
        columnHeaderHeight={38}
        apiRef={apiRef}
        checkboxSelection
        disableRowSelectionOnClick
        rows={rows}
        columns={columns}
        loading={loading}
        rowHeight={76}
        initialState={{
          // rowGrouping: { model: ["name"] },
          columns: {
            columnVisibilityModel: {
              id: false,
            },
          },
        }}
        // groupingColDef={groupingColDef}
        onRowSelectionModelChange={(newRowSelectionModel) => {
          setRowSelectionModel(newRowSelectionModel);
        }}
        rowSelectionModel={rowSelectionModel}
        rowSelectionPropagation={{
          parents: true,
          descendants: true,
        }}
        sx={{
          "--DataGrid-cellOffsetMultiplier": 0.1,
          border: "1px solid grey.200",
          backgroundColor: "white",
          flex: 1,
          "& .MuiDataGrid-cell:focus": {
            outline: "none",
          },
          "& .MuiDataGrid-cell": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
          "& .MuiDataGrid-columnHeader": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
            fontSize: "12px",
          },
          "& .MuiDataGrid-row": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
        }}
        slots={{ noRowsOverlay: EmptyContent }}
        processRowUpdate={processRowUpdate}
      />

      {/* Modals for document actions */}
      <Modal
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        aria-labelledby="preview-modal-title"
        aria-describedby="preview-modal-description"
      >
        <ViewSharepointDocument documentId={selectedRow ?? ""} modal={true} />
      </Modal>
      {selectedRow && (
        <SharepointDocumentHandler
          row={rows.find((r) => r.id === selectedRow) as GridRowModel}
          openDocument={openDocument}
          setOpenDocument={setOpenDocument}
        />
      )}
    </div>
  );
}

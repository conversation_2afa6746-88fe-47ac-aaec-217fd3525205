"use client";

import Button from "@mui/material/Button";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";

import { SignOutButton as ClerkSignOutButton } from "@clerk/nextjs";
import { MotionContainer } from "src/components/animate";
import { Logo } from "~/components/logo";

export function UserNotInOrg() {
  return (
    <Container component={MotionContainer}>
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
          gap: 15,
        }}
      >
        <Logo />
        <Typography variant="h4" sx={{ mb: 0, mt: 1 }}>
          Your user is not a member of this organization.
        </Typography>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Please contact your administrator to get access.
        </Typography>
        <ClerkSignOutButton>
          <Button variant="soft" size="large" color="error">
            Logout
          </Button>
        </ClerkSignOutButton>
      </div>
    </Container>
  );
}

import { useCallback } from "react";

import Avatar from "@mui/material/Avatar";
import AvatarGroup, { avatarGroupClasses } from "@mui/material/AvatarGroup";
import Badge from "@mui/material/Badge";
import IconButton from "@mui/material/IconButton";
import ListItemText from "@mui/material/ListItemText";
import Stack from "@mui/material/Stack";

import { useResponsive } from "src/hooks/use-responsive";

import { fToNow } from "src/utils/format-time";

import { usePopover } from "src/components/custom-popover";
import { Iconify } from "src/components/iconify";

import { ChatHeaderSkeleton } from "./chat-skeleton";

import { Tooltip } from "@mui/material";
import { ParticipantStatus } from "@prisma/client";
import { ChatParticipantGetType } from "~/server/api/routers/chat";
import type { UseNavCollapseReturn } from "./hooks/use-collapse-nav";

// ----------------------------------------------------------------------

type Props = {
  loading: boolean;
  participants: ChatParticipantGetType[];
  collapseNav: UseNavCollapseReturn;
  handleClearChatHistory: () => void;
};

export function ChatHeaderDetail({
  collapseNav,
  participants,
  loading,
  handleClearChatHistory,
}: Props) {
  const popover = usePopover();

  const lgUp = useResponsive("up", "lg");

  const group = participants.length > 1;

  const singleParticipant = {
    ...participants[0],
    lastActivity: new Date(),
  };

  const { collapseDesktop, onCollapseDesktop, onOpenMobile } = collapseNav;

  const handleToggleNav = useCallback(() => {
    if (lgUp) {
      onCollapseDesktop();
    } else {
      onOpenMobile();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lgUp]);

  const renderGroup = (
    <AvatarGroup
      max={3}
      sx={{ [`& .${avatarGroupClasses.avatar}`]: { width: 32, height: 32 } }}
    >
      {participants.map((participant) => (
        <Avatar
          key={participant.id ?? undefined}
          alt={participant.user.name ?? undefined}
          src={participant.user.image ?? undefined}
        />
      ))}
    </AvatarGroup>
  );

  const renderSingle = (
    <Stack direction="row" alignItems="center" spacing={2}>
      <Badge
        variant={"standard"}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Avatar
          src={singleParticipant?.user?.image ?? undefined}
          alt={singleParticipant?.user?.name ?? undefined}
        />
      </Badge>

      <ListItemText
        primary={singleParticipant?.user?.name}
        secondary={
          singleParticipant?.status === ParticipantStatus.OFFLINE
            ? fToNow(singleParticipant?.lastActivity)
            : singleParticipant?.status
        }
        secondaryTypographyProps={{
          component: "span",
          ...(singleParticipant?.status !== ParticipantStatus.OFFLINE && {
            textTransform: "capitalize",
          }),
        }}
      />
    </Stack>
  );

  if (loading) {
    return <ChatHeaderSkeleton />;
  }

  return (
    <>
      {group ? renderGroup : renderSingle}

      <Stack direction="row" flexGrow={1} justifyContent="flex-end">
        <Tooltip title="Clear chat history">
          <IconButton onClick={handleClearChatHistory}>
            <Iconify icon={"ri:restart-fill"} />
          </IconButton>
        </Tooltip>
        <IconButton onClick={handleToggleNav}>
          <Iconify
            icon={
              !collapseDesktop
                ? "ri:sidebar-unfold-fill"
                : "ri:sidebar-fold-fill"
            }
          />
        </IconButton>

        {/* <IconButton onClick={popover.onOpen}>
          <Iconify icon="eva:more-vertical-fill" />
        </IconButton> */}
      </Stack>

      {/* <CustomPopover
        open={popover.open}
        anchorEl={popover.anchorEl}
        onClose={popover.onClose}
      >
        <MenuList>
          <MenuItem
            onClick={() => {
              popover.onClose();
            }}
          >
            <Iconify icon="solar:bell-off-bold" />
            Hide notifications
          </MenuItem>

          <MenuItem
            onClick={() => {
              popover.onClose();
            }}
          >
            <Iconify icon="solar:forbidden-circle-bold" />
            Block
          </MenuItem>

          <MenuItem
            onClick={() => {
              popover.onClose();
            }}
          >
            <Iconify icon="solar:danger-triangle-bold" />
            Report
          </MenuItem>

          <Divider sx={{ borderStyle: "dashed" }} />

          <MenuItem
            onClick={() => {
              popover.onClose();
            }}
            sx={{ color: "error.main" }}
          >
            <Iconify icon="solar:trash-bin-trash-bold" />
            Delete
          </MenuItem>
        </MenuList>
      </CustomPopover> */}
    </>
  );
}

import { useCallback } from "react";

import Avatar from "@mui/material/Avatar";
import AvatarGroup from "@mui/material/AvatarGroup";
import Badge from "@mui/material/Badge";
import Box from "@mui/material/Box";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";

import { useRouter } from "src/routes/hooks";

import { useResponsive } from "src/hooks/use-responsive";

import { fToNow } from "src/utils/format-time";

import { ConversationGetType } from "~/server/api/routers/chat";
import { useNavItem } from "./hooks/use-nav-item";
import { useUser } from "@clerk/clerk-react";

// ----------------------------------------------------------------------

type Props = {
  selected: boolean;
  collapse: boolean;
  onCloseMobile: () => void;
  conversation: ConversationGetType;
  setSelectedConversationId: (conversationId: string) => void;
};

export function ChatNavItem({
  selected,
  collapse,
  conversation,
  onCloseMobile,
  setSelectedConversationId,
}: Props) {

  const { user } = useUser();

  const mdUp = useResponsive("up", "md");

  const router = useRouter();

  const {
    group,
    displayName,
    displayText,
    participants,
    lastActivity,
    hasOnlineInGroup,
  } = useNavItem({ conversation, currentUserId: `${user?.id}` });

  const singleParticipant = participants[0];

  const { name, image: avatarUrl } = singleParticipant?.user ?? {};

  const handleClickConversation = useCallback(async () => {
    try {
      if (!mdUp) {
        onCloseMobile();
      }

      // await clickConversation(conversation.id);

      setSelectedConversationId(conversation.id);

      // router.push(`${paths.dashboard.chat}?id=${conversation.id}`);
    } catch (error) {
      console.error(error);
    }
  }, [conversation.id, mdUp, onCloseMobile, router]);

  const renderGroup = (
    <Badge
      variant={hasOnlineInGroup ? "online" : "invisible"}
      anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
    >
      <AvatarGroup variant="compact" sx={{ width: 48, height: 48 }}>
        {participants.slice(0, 2).map((participant) => (
          <Avatar
            key={participant.id}
            alt={participant.user.name ?? undefined}
            src={participant.user.image ?? undefined}
          />
        ))}
      </AvatarGroup>
    </Badge>
  );

  const renderSingle = (
    <Badge
      key={singleParticipant?.user?.id}
      variant="standard"
      anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
    >
      <Avatar
        alt={name ?? undefined}
        src={avatarUrl ?? undefined}
        sx={{ width: 48, height: 48 }}
      />
    </Badge>
  );

  return (
    <Box component="li" sx={{ display: "flex" }}>
      <ListItemButton
        onClick={handleClickConversation}
        sx={{
          py: 1.5,
          px: 2.5,
          gap: 2,
          ...(selected && { bgcolor: "action.selected" }),
        }}
      >
        <Badge color="error" overlap="circular" badgeContent={0}>
          {group ? renderGroup : renderSingle}
        </Badge>

        {!collapse && (
          <>
            <ListItemText
              primary={displayName}
              primaryTypographyProps={{
                noWrap: true,
                component: "span",
                variant: "subtitle2",
              }}
              secondary={displayText}
              secondaryTypographyProps={{
                noWrap: true,
                component: "span",
                variant: 0 ? "subtitle2" : "body2",
                color: 0 ? "text.primary" : "text.secondary",
              }}
            />

            <Stack alignItems="flex-end" sx={{ alignSelf: "stretch" }}>
              <Typography
                noWrap
                variant="body2"
                component="span"
                sx={{ mb: 1.5, fontSize: 12, color: "text.disabled" }}
              >
                {fToNow(lastActivity)}
              </Typography>

              {!!0 && (
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    bgcolor: "info.main",
                    borderRadius: "50%",
                  }}
                />
              )}
            </Stack>
          </>
        )}
      </ListItemButton>
    </Box>
  );
}

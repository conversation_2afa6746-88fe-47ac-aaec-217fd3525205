import { useCallback, useEffect, useState } from "react";

import Box from "@mui/material/Box";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import Stack from "@mui/material/Stack";
import { useTheme } from "@mui/material/styles";
import TextField from "@mui/material/TextField";

import { useRouter } from "src/routes/hooks";

import { useResponsive } from "src/hooks/use-responsive";

import { Iconify } from "src/components/iconify";
import { Scrollbar } from "src/components/scrollbar";

import { ChatNavAccount } from "./chat-nav-account";
import { ChatNavItem } from "./chat-nav-item";
import { ChatNavSearchResults } from "./chat-nav-search-results";
import { ChatNavItemSkeleton } from "./chat-skeleton";
import { ToggleButton } from "./styles";

import {
  ChatParticipantGetType,
  ConversationGetType,
} from "~/server/api/routers/chat";
import type { UseNavCollapseReturn } from "./hooks/use-collapse-nav";

// ----------------------------------------------------------------------

const NAV_WIDTH = 320;

const NAV_COLLAPSE_WIDTH = 96;

type Props = {
  loading: boolean;
  selectedConversationId: string;
  contacts: ChatParticipantGetType[];
  collapseNav: UseNavCollapseReturn;
  conversations: ConversationGetType[];
  setSelectedConversationId: (conversationId: string) => void;
};

export function ChatNav({
  loading,
  contacts,
  conversations,
  collapseNav,
  selectedConversationId,
  setSelectedConversationId,
}: Props) {
  const theme = useTheme();

  const router = useRouter();

  const mdUp = useResponsive("up", "md");

  const {
    openMobile,
    onOpenMobile,
    onCloseMobile,
    onCloseDesktop,
    collapseDesktop,
    onCollapseDesktop,
  } = collapseNav;

  const [searchContacts, setSearchContacts] = useState<{
    query: string;
    results: ChatParticipantGetType[];
  }>({ query: "", results: [] });

  useEffect(() => {
    if (!mdUp) {
      onCloseDesktop();
    }
  }, [onCloseDesktop, mdUp]);

  const handleToggleNav = useCallback(() => {
    if (mdUp) {
      onCollapseDesktop();
    } else {
      onCloseMobile();
    }
  }, [mdUp, onCloseMobile, onCollapseDesktop]);

  const handleClickCompose = useCallback(() => {
    if (!mdUp) {
      onCloseMobile();
    }
    // router.push(paths.dashboard.chat);
  }, [mdUp, onCloseMobile, router]);

  const handleSearchContacts = useCallback(
    (inputValue: string) => {
      setSearchContacts((prevState) => ({ ...prevState, query: inputValue }));

      if (inputValue) {
        const results = contacts.filter((contact) =>
          contact.user.name?.toLowerCase().includes(inputValue),
        );

        setSearchContacts((prevState) => ({ ...prevState, results }));
      }
    },
    [contacts],
  );

  const handleClickAwaySearch = useCallback(() => {
    setSearchContacts({ query: "", results: [] });
  }, []);

  const handleClickResult = useCallback(
    (result: ChatParticipantGetType) => {
      handleClickAwaySearch();

      // router.push(`${paths.dashboard.chat}?id=${result.id}`);
    },
    [handleClickAwaySearch, router],
  );

  const renderLoading = <ChatNavItemSkeleton />;

  const renderList = (
    <nav>
      <Box component="ul">
        {conversations.map((conversation) => (
          <ChatNavItem
            key={conversation.id}
            collapse={collapseDesktop}
            conversation={conversation}
            selected={conversation.id === selectedConversationId}
            onCloseMobile={onCloseMobile}
            setSelectedConversationId={setSelectedConversationId}
          />
        ))}
      </Box>
    </nav>
  );

  const renderListResults = (
    <ChatNavSearchResults
      query={searchContacts.query}
      results={searchContacts.results}
      onClickResult={handleClickResult}
    />
  );

  const renderSearchInput = (
    <ClickAwayListener onClickAway={handleClickAwaySearch}>
      <TextField
        fullWidth
        value={searchContacts.query}
        onChange={(event) => handleSearchContacts(event.target.value)}
        placeholder="Search contacts..."
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="eva:search-fill" sx={{ color: "text.disabled" }} />
            </InputAdornment>
          ),
        }}
        sx={{ mt: 2.5 }}
      />
    </ClickAwayListener>
  );

  const renderContent = (
    <>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="center"
        sx={{ p: 2.5, pb: 0 }}
      >
        {!collapseDesktop && (
          <>
            <ChatNavAccount />
            <Box sx={{ flexGrow: 1 }} />
          </>
        )}

        <IconButton onClick={handleToggleNav}>
          <Iconify
            icon={
              collapseDesktop
                ? "eva:arrow-ios-forward-fill"
                : "eva:arrow-ios-back-fill"
            }
          />
        </IconButton>

        {!collapseDesktop && (
          <IconButton onClick={handleClickCompose}>
            <Iconify width={24} icon="solar:user-plus-bold" />
          </IconButton>
        )}
      </Stack>

      <Box sx={{ p: 2.5, pt: 0 }}>
        {/*!collapseDesktop && renderSearchInput*/}
      </Box>

      {loading ? (
        renderLoading
      ) : (
        <Scrollbar sx={{ pb: 1 }}>
          {searchContacts.query && !!conversations.length
            ? renderListResults
            : renderList}
        </Scrollbar>
      )}
    </>
  );

  return (
    <>
      <ToggleButton onClick={onOpenMobile} sx={{ display: { md: "none" } }}>
        <Iconify width={16} icon="solar:users-group-rounded-bold" />
      </ToggleButton>

      <Stack
        sx={{
          minHeight: 0,
          flex: "1 1 auto",
          width: NAV_WIDTH,
          display: { xs: "none", md: "flex" },
          borderRight: `solid 1px ${theme.vars.palette.divider}`,
          transition: theme.transitions.create(["width"], {
            duration: theme.transitions.duration.shorter,
          }),
          ...(collapseDesktop && { width: NAV_COLLAPSE_WIDTH }),
        }}
      >
        {renderContent}
      </Stack>

      <Drawer
        open={openMobile}
        onClose={onCloseMobile}
        slotProps={{ backdrop: { invisible: true } }}
        PaperProps={{ sx: { width: NAV_WIDTH } }}
      >
        {renderContent}
      </Drawer>
    </>
  );
}

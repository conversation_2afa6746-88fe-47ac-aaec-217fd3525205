import { ParticipantStatus } from "@prisma/client";
import { RAGResponseWithCitations } from "~/lib/types";
import { ConversationGetType } from "~/server/api/routers/chat";
import { isValidJSON } from "~/utils/json";
// ----------------------------------------------------------------------

type Props = {
  currentUserId: string;
  conversation: ConversationGetType;
};

export function useNavItem({ currentUserId, conversation }: Props) {
  const { messages, participants } = conversation;

  const participantsInConversation = participants.filter(
    (participant) => participant.id !== currentUserId,
  );

  const lastMessage = messages.sort(
    (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
  )[messages.length - 1];

  const group = participantsInConversation.length > 1;

  const displayName = participantsInConversation
    .map((participant) => participant.user.name)
    .join(", ");

  const hasOnlineInGroup = group
    ? participantsInConversation
        .map((item) => item.status)
        .includes(ParticipantStatus.ONLINE)
    : false;

  let displayText = "";

  if (lastMessage) {
    const sender = lastMessage.createdById === currentUserId ? "You: " : "";

    const { answer, citations } = isValidJSON(lastMessage.body)
      ? (JSON.parse(lastMessage.body) as RAGResponseWithCitations)
      : { answer: lastMessage.body, citations: []};

    const message =
      lastMessage.contentType === "image" ? "Sent a photo" : answer;

    displayText = `${sender}${message}`;
  }

  return {
    group,
    displayName,
    displayText,
    participants: participantsInConversation,
    lastActivity: lastMessage?.createdAt,
    hasOnlineInGroup,
  };
}

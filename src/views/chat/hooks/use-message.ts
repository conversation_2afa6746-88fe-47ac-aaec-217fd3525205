import { type ChatMessage } from "@prisma/client";
import { type ChatParticipantGetType } from "~/server/api/routers/chat";

// ----------------------------------------------------------------------

type Props = {
  currentUserId: string;
  message: ChatMessage;
  participants: ChatParticipantGetType[];
};

export function useMessage({ message, participants, currentUserId }: Props) {
  const sender = participants.find(
    (participant) => participant.userId === message.createdById,
  );

  const senderDetails =
    message.createdById === currentUserId
      ? {
          type: "me",
          avatarUrl: sender?.user.image,
          firstName: sender?.user.name?.split(" ")[0],
        }
      : {
          type: "other",
          avatarUrl: sender?.user.image,
          firstName: sender?.user.name?.split(" ")[0],
        };

  const me = senderDetails.type === "me";

  const hasImage = message.contentType === "image";

  return { hasImage, me, senderDetails };
}

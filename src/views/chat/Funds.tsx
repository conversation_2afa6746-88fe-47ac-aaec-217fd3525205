import { Collapse } from "@mui/material";
import { useState } from "react";
import Select, { type MultiValue } from "react-select";
import { useBoolean } from "src/hooks/use-boolean";
import { api } from "~/trpc/react";
import { CollapseButton } from "./styles";
type Props = {
  fundIds: string[];
  setFundIds: (fundIds: string[]) => void;
};

export function Funds({ fundIds, setFundIds }: Props) {
  const { data: funds, isLoading, error } = api.fund.getAllFunds.useQuery();
  const options =
    funds?.map((fund) => ({ value: fund.id, label: fund.name })) || [];
  const [value, setValue] = useState<string[]>(fundIds ?? []);
  const collapse = useBoolean(true);

  const handleChange = (
    newValue: MultiValue<{ value: string; label: string }>,
  ) => {
    const selectedFundIds: string[] = newValue?.map((v) => v.value) || [];
    setValue(selectedFundIds);
    setFundIds(selectedFundIds);
  };

  return (
    <>
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Filter by Funds`}
      </CollapseButton>

      <Collapse in={collapse.value}>
        <Select
          value={options.filter((option) => value.includes(option.value))}
          isMulti
          isClearable={false}
          placeholder="Select funds"
          isLoading={isLoading}
          name="funds"
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={handleChange}
          options={options}
          styles={{
            container: (base) => ({
              ...base,
              padding: 2,
              borderRadius: 2,
              marginTop: 2,
            }),
          }}
        />
      </Collapse>
    </>
  );
  //   style={{
  //     padding: 2,
  //     border: "1px solid #ddd",
  //     borderRadius: 2,
  //     marginTop: 2,
  //   }}
  // >
  //   <Select
  //     value={options.filter((option) => value.includes(option.value))}
  //     isMulti
  //     isClearable={false}
  //     placeholder="Select tags"
  //     isLoading={isLoading}
  //     name="tags"
  //     className="basic-multi-select"
  //     classNamePrefix="select"
  //     onChange={handleChange}
  //     options={options}
  //   />
  // </div>
}

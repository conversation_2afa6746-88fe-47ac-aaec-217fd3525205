import { Collapse } from "@mui/material";
import { useState } from "react";
import Select, { type SingleValue } from "react-select";
import { useBoolean } from "src/hooks/use-boolean";
import { ChatModel } from "~/lib/types";
import { type ModelParameters } from "./ChatModelParametersSelector";
import { CollapseButton } from "./styles";

type Props = {
  modelParameters: ModelParameters;
  setModelParameters: (modelParameters: ModelParameters) => void;
};

export function ChatModelSelector({
  modelParameters,
  setModelParameters,
}: Props) {
  const options = Object.entries(ChatModel).map(([key, value]) => ({
    value: value,
    label: value,
  }));

  const [value, setValue] = useState<string | undefined>(modelParameters.model);
  const collapse = useBoolean(true);

  const handleChange = (
    newValue: SingleValue<{ value: string; label: string }>,
  ) => {
    const selectedModel = newValue?.value;

    setValue(selectedModel);
    setModelParameters({
      ...modelParameters,
      model: selectedModel as ChatModel,
    });
  };

  return (
    <>
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Select Chat Model`}
      </CollapseButton>

      <Collapse in={collapse.value}>
        <Select
          value={options.filter((option) => value === option.value)}
          isClearable={false}
          placeholder="Select model"
          isLoading={false}
          name="model"
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={handleChange}
          options={options}
          styles={{
            container: (base) => ({
              ...base,
              padding: 2,
              borderRadius: 2,
              marginTop: 2,
            }),
          }}
        />
      </Collapse>
    </>
  );
}

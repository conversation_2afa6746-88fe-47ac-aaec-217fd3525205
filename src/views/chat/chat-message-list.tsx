import LinearProgress from "@mui/material/LinearProgress";
import Stack from "@mui/material/Stack";

import { Lightbox, useLightBox } from "src/components/lightbox";
import { Scrollbar } from "src/components/scrollbar";

import Box from "@mui/material/Box";
import Fade from "@mui/material/Fade";
import Typography from "@mui/material/Typography";
import { ChatMessageStatus, type User } from "@prisma/client";
import { TransitionGroup } from "react-transition-group";
import {
  type ChatMessageGetType,
  type ChatParticipantGetType,
} from "~/server/api/routers/chat";
import { ChatMessageItem } from "./chat-message-item";
import { useMessagesScroll } from "./hooks/use-messages-scroll";

// ----------------------------------------------------------------------

type Props = {
  loading: boolean;
  messages: ChatMessageGetType[];
  participants: ChatParticipantGetType[];
  userDetails: User | undefined;
  responseLoading: boolean;
  messageStatus: ChatMessageStatus | undefined;
};

export const statusMap = {
  [ChatMessageStatus.READY]: "Ready",
  [ChatMessageStatus.RETRIEVING]: "Retrieving",
  [ChatMessageStatus.GENERATING_CITATIONS]: "Generating citations",
  [ChatMessageStatus.VALIDATING_CITATIONS]: "Validating citations",
  [ChatMessageStatus.GENERATING_ANSWER]: "Generating answer",
  [ChatMessageStatus.RETRY]: "Retrying",
  [ChatMessageStatus.ERROR]: "Error",
};

export function ChatMessageList({
  messages = [],
  participants,
  loading,
  userDetails,
  responseLoading,
  messageStatus,
}: Props) {
  const { messagesEndRef } = useMessagesScroll(messages);

  const slides = messages
    .filter((message) => message.contentType === "image")
    .map((message) => ({ src: message.body }));

  const lightbox = useLightBox(slides);

  if (loading) {
    return (
      <Stack sx={{ flex: "1 1 auto", position: "relative" }}>
        <LinearProgress
          color="inherit"
          sx={{
            top: 0,
            left: 0,
            width: 1,
            height: 2,
            borderRadius: 0,
            position: "absolute",
          }}
        />
      </Stack>
    );
  }

  return (
    <>
      <Scrollbar
        ref={messagesEndRef}
        sx={{ px: 3, pt: 5, pb: 3, flex: "1 1 auto" }}
      >
        <TransitionGroup>
          {messages.map((message, idx) => (
            <ChatMessageItem
              key={message.id}
              message={message}
              participants={participants}
              onOpenLightbox={() => lightbox.onOpen(message.body)}
              userDetails={userDetails}
              feedback={idx > 0}
            />
          ))}
        </TransitionGroup>

        <Fade in={responseLoading} timeout={1000}>
          <Box
            sx={{
              position: "relative",
              bottom: 0,
              left: 0,
              right: 0,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {messageStatus && statusMap[messageStatus]}
            </Typography>
            <LinearProgress
              sx={{
                display: "flex",
                justifyContent: "center",
                mt: 2,
                padding: "1px",
              }}
            />
          </Box>
        </Fade>
      </Scrollbar>

      <Lightbox
        slides={slides}
        open={lightbox.open}
        close={lightbox.onClose}
        index={lightbox.selected}
      />
    </>
  );
}

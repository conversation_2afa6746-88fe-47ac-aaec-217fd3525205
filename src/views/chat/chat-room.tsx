import CircularProgress from "@mui/material/CircularProgress";
import Drawer from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import { useTheme } from "@mui/material/styles";

import { Scrollbar } from "src/components/scrollbar";

import { ChatRoomSkeleton } from "./chat-skeleton";

import { Divider, Typography } from "@mui/material";
import { DocumentStatus } from "@prisma/client";
import { useEffect, useState } from "react";
import { getRuntime } from "~/lib/runtime";
import {
  type ChatMessageGetType,
  type ChatParticipantGetType,
} from "~/server/api/routers/chat";
import { api } from "~/trpc/react";
import {
  ChatModelParametersSelector,
  type ModelParameters,
} from "./ChatModelParametersSelector";
import { ChatModelSelector } from "./ChatModelSelector";
import { ChatResponseStyleSelector } from "./ChatResponseStyleSelector";
import { Funds } from "./Funds";
import type { UseNavCollapseReturn } from "./hooks/use-collapse-nav";
import { Categories } from "./Categories";
import { Tags } from "./Tags";
import { env } from "~/env";
// ----------------------------------------------------------------------

const NAV_WIDTH = 360;

const NAV_DRAWER_WIDTH = 400;

type Props = {
  loading: boolean;
  participants: ChatParticipantGetType[];
  collapseNav: UseNavCollapseReturn;
  messages: ChatMessageGetType[];
  tagIds: string[];
  setTagIds: (tagIds: string[]) => void;
  categoryIds: string[];
  setCategoryIds: (categoryIds: string[]) => void;
  fundIds: string[];
  setFundIds: (fundIds: string[]) => void;
  modelParameters: ModelParameters;
  setModelParameters: (modelParameters: ModelParameters) => void;
};

export function ChatRoom({
  collapseNav,
  participants,
  messages,
  loading,
  tagIds,
  fundIds,
  setTagIds,
  categoryIds,
  setCategoryIds,
  setFundIds,
  modelParameters,
  setModelParameters,
}: Props) {
  const [enabled, setEnabled] = useState(false);
  const { data: documents, isLoading } =
    api.document.getFilteredDocumentsCount.useQuery(
      {
        status: [DocumentStatus.READY],
        tagIds,
        fundIds,
        categoryIds,
      },
      {
        enabled: enabled,
      },
    );

  useEffect(() => {
    setEnabled(!!tagIds || !!fundIds || !!categoryIds);
  }, [tagIds, fundIds, categoryIds, enabled]);

  const theme = useTheme();

  const { collapseDesktop, openMobile, onCloseMobile } = collapseNav;

  const attachments =
    messages
      .filter((msg) => msg.attachments)
      .map((msg) => msg.attachments)
      .flat(1) || [];

  const renderContent = loading ? (
    <ChatRoomSkeleton />
  ) : (
    <Scrollbar>
      <div style={{ padding: 2 }}>
        {env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true" && (
          <Categories
            categoryIds={categoryIds}
            setCategoryIds={setCategoryIds}
          />
        )}
        <Tags tagIds={tagIds} setTagIds={setTagIds} />
        <Funds fundIds={fundIds} setFundIds={setFundIds} />
        {getRuntime() === "dev" && (
          <ChatModelSelector
            modelParameters={modelParameters}
            setModelParameters={setModelParameters}
          />
        )}
        {getRuntime() === "dev" && (
          <ChatResponseStyleSelector
            modelParameters={modelParameters}
            setModelParameters={setModelParameters}
          />
        )}
        {getRuntime() === "dev" && (
          <ChatModelParametersSelector
            modelParameters={modelParameters}
            setModelParameters={setModelParameters}
          />
        )}
        <Divider variant="middle" sx={{ m: 2 }} />
        {/* <ChatRoomAttachments attachments={attachments} /> */}
        <Stack direction="row" spacing={2} sx={{ p: 2 }}>
          {isLoading ? (
            <div style={{ display: "flex", alignItems: "center", gap: 4 }}>
              <CircularProgress size={20} />
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  fontWeight: 600,
                  pl: 2,
                }}
              >
                Loading documents...
              </Typography>
            </div>
          ) : (
            <Stack>
              {documents === 0 ? (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontWeight: 600,
                  }}
                >
                  No documents match your criteria.
                </Typography>
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontWeight: 600,
                  }}
                >
                  {documents} Data Room documents match your criteria.
                </Typography>
              )}
            </Stack>
          )}
        </Stack>
      </div>
    </Scrollbar>
  );

  return (
    <>
      <Stack
        sx={{
          minHeight: 0,
          flex: "1 1 auto",
          width: NAV_WIDTH,
          display: { xs: "none", lg: "flex" },
          borderLeft: `solid 1px ${theme.vars.palette.divider}`,
          transition: theme.transitions.create(["width"], {
            duration: theme.transitions.duration.shorter,
          }),
          ...(collapseDesktop && { width: 0 }),
        }}
      >
        {!collapseDesktop && renderContent}
      </Stack>

      <Drawer
        anchor="right"
        open={openMobile}
        onClose={onCloseMobile}
        slotProps={{ backdrop: { invisible: true } }}
        PaperProps={{ sx: { width: NAV_DRAWER_WIDTH } }}
      >
        {renderContent}
      </Drawer>
    </>
  );
}

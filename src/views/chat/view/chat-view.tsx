"use client";
import { useUser } from "@clerk/nextjs";
import { LinearProgress, Stack } from "@mui/material";
import {
  type ChatMessage,
  ChatMessageStatus,
  type ChatParticipant,
  ParticipantStatus,
} from "@prisma/client";
import { useCallback, useEffect, useState } from "react";
import { EmptyContent } from "src/components/empty-content";
import { CONFIG } from "src/config-global";
import Loading from "~/app/dashboard/loading";
import { env } from "~/env";
import { DashboardContent } from "~/layouts/Dashboard/DashboardContent";
import { ChatModel, ResponseStyle } from "~/lib/types";
import {
  type ChatMessageGetType,
  type ChatParticipantGetType,
  type ConversationGetType,
} from "~/server/api/routers/chat";
import { api } from "~/trpc/react";
import { ChatHeaderDetail } from "../chat-header-detail";
import { ChatMessageInput } from "../chat-message-input";
import { ChatMessageList } from "../chat-message-list";
import { ChatNav } from "../chat-nav";
import { ChatRoom } from "../chat-room";
import { type ModelParameters } from "../ChatModelParametersSelector";
import { useCollapseNav } from "../hooks/use-collapse-nav";
import { Layout } from "../layout";

// ----------------------------------------------------------------------
type ChatViewProps = {
  initialTagIds?: string[];
  initialFundIds?: string[];
  initialCategoryIds?: string[];
};
export function ChatView({
  initialTagIds,
  initialFundIds,
  initialCategoryIds,
}: ChatViewProps) {
  const { user } = useUser();

  const contacts: ChatParticipantGetType[] = [];

  const [selectedConversationId, setSelectedConversationId] =
    useState<string>("");

  const [recipients, setRecipients] = useState<ChatParticipant[]>([]);
  const [focus, setFocus] = useState<boolean>(false);
  const [messages, setMessages] = useState<ChatMessageGetType[]>([]);
  const [newMessage, setNewMessage] = useState<ChatMessage>();
  const [loading, setLoading] = useState<boolean>(false);
  const [messagesLoading, setMessagesLoading] = useState<boolean>(true);
  const [messageStatus, setMessageStatus] = useState<ChatMessageStatus>();
  const [tagIds, setTagIds] = useState<string[]>(initialTagIds ?? []);
  const [categoryIds, setCategoryIds] = useState<string[]>(
    initialCategoryIds ?? [],
  );
  const [fundIds, setFundIds] = useState<string[]>(initialFundIds ?? []);

  const [modelParameters, setModelParameters] = useState<ModelParameters>({
    model: ChatModel.CLAUDE_40_LATEST,
    responseStyle: ResponseStyle.NORMAL,
    temperature: 0,
    topP: 0,
    thinking_mode: false,
    citation_verification_mode: false,
    answer_reflexion_mode: false,
    agentic_mode_langgraph: env.NEXT_PUBLIC_AGENTIC_MODE_LANGGRAPH === "true",
  });

  const { data: conversations, isLoading: conversationsLoading } =
    api.chat.getAll.useQuery();

  const { data: conversation, isLoading: conversationLoading } =
    api.chat.get.useQuery(
      {
        conversationId: selectedConversationId,
      },
      {
        enabled: !!selectedConversationId,
      },
    );

  const { data: userDetails } = api.user.get.useQuery();
  const clearHistory = api.chat.clear.useMutation();

  const roomNav = useCollapseNav();

  const conversationsNav = useCollapseNav();

  const participants: ChatParticipantGetType[] = conversation
    ? conversation.participants.filter(
        (participant: ChatParticipant) => participant.id !== `${user?.id}`,
      )
    : [];

  const handleAddRecipients = useCallback((selected: ChatParticipant[]) => {
    setRecipients(selected);
  }, []);

  const handleClearChatHistory = useCallback(() => {
    setMessages([]);

    clearHistory.mutate(
      { conversationId: selectedConversationId },
      {
        onSuccess: () => {},
      },
    );
  }, [selectedConversationId]);

  useEffect(() => {
    setRecipients([
      {
        id: "1",
        userId: "",
        conversationId: "1",
        status: ParticipantStatus.ONLINE,
      },
    ]);
  }, []);

  useEffect(() => {
    if (messages.length === 0 && conversation) {
      setMessages(
        conversation?.messages.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
        ) ?? [],
      );
      setMessagesLoading(false);
    }
  }, [conversation]);

  useEffect(() => {
    if (conversations && conversations[0]) {
      setSelectedConversationId(conversations[0].id);
    }
  }, [conversations]);

  useEffect(() => {
    if (newMessage) {
      if (messages.find((message) => message.id === newMessage.id)) {
        const newMessages = messages.map((message) => {
          if (message.id === newMessage.id) {
            return newMessage;
          }

          return message;
        });

        setMessages(newMessages as ChatMessageGetType[]);
      } else {
        setMessages([...messages, newMessage as ChatMessageGetType]);
      }
    }
  }, [newMessage]);

  if (conversationsLoading) {
    return <Loading />;
  }

  const flexProps = {
    flex: "1 1 auto",
    display: "flex",
    flexDirection: "column",
  };

  return (
    <>
      <DashboardContent>
        <Layout
          sx={{
            minHeight: 0,
            flex: "1 1 0",
            borderRadius: 2,
            position: "relative",
            bgcolor: "#fcfcfc",
            boxShadow: (theme) => theme.customShadows.card,
          }}
          slots={{
            header: selectedConversationId && (
              <ChatHeaderDetail
                collapseNav={roomNav}
                participants={participants}
                loading={conversationLoading}
                handleClearChatHistory={handleClearChatHistory}
              />
            ),
            nav: (
              <ChatNav
                contacts={contacts}
                conversations={conversations as ConversationGetType[]}
                loading={conversationsLoading}
                selectedConversationId={selectedConversationId}
                setSelectedConversationId={setSelectedConversationId}
                collapseNav={conversationsNav}
              />
            ),
            main: (
              <>
                {selectedConversationId ? (
                  messages.length > 0 ? (
                    <ChatMessageList
                      messages={messages}
                      participants={participants}
                      loading={conversationLoading}
                      userDetails={userDetails}
                      responseLoading={loading}
                      messageStatus={messageStatus}
                    />
                  ) : messagesLoading ? (
                    <Stack sx={{ flex: "1 1 auto", position: "relative" }}>
                      <LinearProgress
                        color="inherit"
                        sx={{
                          top: 0,
                          left: 0,
                          width: 1,
                          height: 2,
                          borderRadius: 0,
                          position: "absolute",
                        }}
                      />
                    </Stack>
                  ) : (
                    <EmptyContent
                      // imgUrl={`${CONFIG.site.basePath}/assets/icons/empty/ic-chat-active.svg`}
                      title="Chat history cleared"
                      description="The new conversation will contain no previous context."
                    />
                  )
                ) : (
                  <EmptyContent
                    imgUrl={`${CONFIG.site.basePath}/assets/icons/empty/ic-chat-active.svg`}
                    title="Good morning!"
                    description="Write something awesome..."
                  />
                )}

                <ChatMessageInput
                  recipients={recipients}
                  onAddRecipients={handleAddRecipients}
                  messages={messages}
                  setMessages={setMessages}
                  selectedConversationId={selectedConversationId}
                  disabled={!recipients.length && !selectedConversationId}
                  handleOnFocus={(event) => {
                    setFocus(!focus);
                  }}
                  userDetails={userDetails}
                  setNewMessage={setNewMessage}
                  status={messageStatus ?? ChatMessageStatus.GENERATING_ANSWER}
                  tagIds={tagIds}
                  categoryIds={categoryIds}
                  fundIds={fundIds}
                  modelParameters={modelParameters}
                  setModelParameters={setModelParameters}
                />
              </>
            ),
            details: selectedConversationId && (
              <ChatRoom
                collapseNav={roomNav}
                participants={participants}
                loading={conversationLoading}
                messages={messages}
                tagIds={tagIds}
                setTagIds={setTagIds}
                categoryIds={categoryIds}
                setCategoryIds={setCategoryIds}
                fundIds={fundIds}
                setFundIds={setFundIds}
                modelParameters={modelParameters}
                setModelParameters={setModelParameters}
              />
            ),
          }}
        />
      </DashboardContent>
    </>
  );
}

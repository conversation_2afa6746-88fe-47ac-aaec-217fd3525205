import { Collapse } from "@mui/material";
import { useState } from "react";
import Select, { type MultiValue } from "react-select";
import { useBoolean } from "src/hooks/use-boolean";
import { api } from "~/trpc/react";
import { CollapseButton } from "./styles";
type Props = {
  tagIds: string[];
  setTagIds: (tagIds: string[]) => void;
};

export function Tags({ tagIds, setTagIds }: Props) {
  const { data: tags, isLoading, error } = api.tag.getAllTags.useQuery();
  const options =
    tags?.map((tag) => ({ value: tag.id, label: tag.name })) || [];
  const [value, setValue] = useState<string[]>(tagIds ?? []);
  const collapse = useBoolean(true);

  const handleChange = (
    newValue: MultiValue<{ value: string; label: string }>,
  ) => {
    const selectedTagIds: string[] = newValue?.map((v) => v.value) || [];
    setValue(selectedTagIds);
    setTagIds(selectedTagIds);
  };

  return (
    <>
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Filter by Tags`}
      </CollapseButton>

      <Collapse in={collapse.value}>
        <Select
          value={options.filter((option) => value.includes(option.value))}
          isMulti
          isClearable={false}
          placeholder="Select tags"
          isLoading={isLoading}
          name="tags"
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={handleChange}
          options={options}
          styles={{
            container: (base) => ({
              ...base,
              padding: 2,
              borderRadius: 2,
              marginTop: 2,
            }),
          }}
        />
      </Collapse>
    </>
  );
  //   style={{
  //     padding: 2,
  //     border: "1px solid #ddd",
  //     borderRadius: 2,
  //     marginTop: 2,
  //   }}
  // >
  //   <Select
  //     value={options.filter((option) => value.includes(option.value))}
  //     isMulti
  //     isClearable={false}
  //     placeholder="Select tags"
  //     isLoading={isLoading}
  //     name="tags"
  //     className="basic-multi-select"
  //     classNamePrefix="select"
  //     onChange={handleChange}
  //     options={options}
  //   />
  // </div>
}

import {
  Checkbox,
  Collapse,
  Input,
  Slider,
  Stack,
  Typography,
} from "@mui/material";
import { useState } from "react";
import { useBoolean } from "src/hooks/use-boolean";
import { type ChatModel, type ResponseStyle } from "~/lib/types";
import { CollapseButton } from "./styles";

export type ModelParameters = {
  model: ChatModel;
  responseStyle: ResponseStyle;
  temperature: number;
  topP: number; // Range: 1e-9 to 1
  agentic_mode_langgraph: boolean;
  thinking_mode: boolean;
  citation_verification_mode: boolean;
  answer_reflexion_mode: boolean;
};

const clampTopP = (value: number) => Math.min(Math.max(value, 1e-9), 1);
const formatTopP = (value: number) => value.toExponential(2);

type Props = {
  modelParameters: ModelParameters;
  setModelParameters: (modelParameters: ModelParameters) => void;
};

export function ChatModelParametersSelector({
  modelParameters,
  setModelParameters,
}: Props) {
  const [value, setValue] = useState<number>(modelParameters.temperature);
  const collapse = useBoolean(true);

  const handleChange = (event: Event, newValue: number | number[]) => {
    const selectedTemperature = newValue as number;

    setValue(selectedTemperature);
    setModelParameters({
      ...modelParameters,
      temperature: selectedTemperature,
    });
  };

  return (
    <div
      style={{
        padding: 1,
      }}
    >
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Set Model Parameters`}
      </CollapseButton>

      <Collapse
        in={collapse.value}
        style={{
          padding: 2,
          border: "1px solid #e0e0e0",
          borderRadius: 3,
          marginTop: 2,
        }}
      >
        <Stack direction="column" spacing={2} gap={1}>
          <Typography
            variant="body2"
            sx={{
              p: 1,
            }}
          >
            Temperature: {modelParameters.temperature}
          </Typography>
          <Stack
            spacing={1}
            direction="row"
            sx={{ alignItems: "center", pl: 1, pr: 1, pt: 0, pb: 0 }}
          >
            <Slider
              aria-label="Volume"
              value={value}
              onChange={handleChange}
              step={0.1}
              min={0}
              max={1}
              marks={[
                { value: 0, label: "0" },
                { value: 0.5, label: "0.5" },
                { value: 1, label: "1" },
              ]}
            />
          </Stack>
          {/* <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: "100%",
            }}
          >
            <Label
              variant="outlined"
              color="secondary"
              sx={{
                gap: 0.2,
              }}
            >
              <Iconify icon="oui:temperature" width={14} />
              {modelParameters.temperature}
            </Label>
          </div> */}

          <Typography
            variant="body2"
            sx={{
              p: 1,
            }}
          >
            Top P
          </Typography>
          <Stack
            spacing={1}
            direction="row"
            sx={{ alignItems: "center", pl: 1, pr: 1, pt: 0, pb: 0 }}
          >
            <Input
              value={modelParameters.topP.toExponential(0)}
              inputProps={{
                min: 1e-9,
                max: 1,
                // Prevent invalid inputs
                onKeyDown: (e) => {
                  // Allow e for scientific notation
                  if (e.key === "-" || e.key === "+") {
                    e.preventDefault();
                  }
                },
              }}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                // Clamp value between 1e-9 and 1
                const clampedValue = Math.min(Math.max(value, 1e-9), 1);
                setModelParameters({
                  ...modelParameters,
                  topP: clampedValue,
                });
              }}
              title="Top-P (nucleus sampling) controls diversity. Range: 1e-9 to 1"
              sx={{
                width: "120px",
                "& input": {
                  textAlign: "center",
                },
              }}
            />
            {/* Slider with logarithmic scale */}
            <Slider
              value={Math.log10(modelParameters.topP)}
              onChange={(_, value) =>
                setModelParameters({
                  ...modelParameters,
                  topP: Math.pow(10, value as number),
                })
              }
              min={-9} // log10(1e-9)
              max={0} // log10(1)
              step={0.1}
              scale={(x) => Math.pow(10, x)} // Convert from log scale to actual value
              valueLabelFormat={(x) => Math.pow(10, x).toExponential(0)}
              valueLabelDisplay="auto"
              marks={[
                { value: -9, label: "1e-9" },
                { value: -6, label: "1e-6" },
                { value: -3, label: "1e-3" },
                { value: 0, label: "1" },
              ]}
              sx={{
                width: "200px",
                ml: 2,
                "& .MuiSlider-markLabel": {
                  fontSize: "0.75rem",
                },
              }}
            />
          </Stack>
          <Typography variant="body2" sx={{ p: 1 }}>
            Agentic Mode LangGraph:{" "}
            <Checkbox
              checked={modelParameters.agentic_mode_langgraph}
              onChange={(e) =>
                setModelParameters({
                  ...modelParameters,
                  agentic_mode_langgraph: e.target.checked,
                })
              }
            />
          </Typography>
          <Typography variant="body2" sx={{ p: 1 }}>
            Thinking Mode:{" "}
            <Checkbox
              checked={modelParameters.thinking_mode}
              onChange={(e) =>
                setModelParameters({
                  ...modelParameters,
                  thinking_mode: e.target.checked,
                })
              }
            />
          </Typography>
          <Typography variant="body2" sx={{ p: 1 }}>
            Citation Verification Mode:{" "}
            <Checkbox
              checked={modelParameters.citation_verification_mode}
              onChange={(e) =>
                setModelParameters({
                  ...modelParameters,
                  citation_verification_mode: e.target.checked,
                })
              }
            />
          </Typography>
          <Typography variant="body2" sx={{ p: 1 }}>
            Answer Reflexion Mode:{" "}
            <Checkbox
              checked={modelParameters.answer_reflexion_mode}
              onChange={(e) =>
                setModelParameters({
                  ...modelParameters,
                  answer_reflexion_mode: e.target.checked,
                })
              }
            />
          </Typography>
        </Stack>
      </Collapse>
    </div>
  );
}

import Avatar from "@mui/material/Avatar";
import Box from "@mui/material/Box";
import ListItemButton from "@mui/material/ListItemButton";
import Typography from "@mui/material/Typography";

import { SearchNotFound } from "src/components/search-not-found";
import { ChatParticipantGetType } from "~/server/api/routers/chat";

// ----------------------------------------------------------------------

type Props = {
  query: string;
  results: ChatParticipantGetType[];
  onClickResult: (contact: ChatParticipantGetType) => void;
};

export function ChatNavSearchResults({ query, results, onClickResult }: Props) {
  const totalResults = results.length;

  const notFound = !totalResults && !!query;

  const renderNotFound = (
    <SearchNotFound
      query={query}
      sx={{
        p: 3,
        mx: "auto",
        width: `calc(100% - 40px)`,
        bgcolor: "background.neutral",
      }}
    />
  );

  const renderResults = (
    <nav>
      <Box component="ul">
        {results.map((result) => (
          <Box key={result.id} component="li" sx={{ display: "flex" }}>
            <ListItemButton
              onClick={() => onClickResult(result)}
              sx={{ gap: 2, py: 1.5, px: 2.5, typography: "subtitle2" }}
            >
              <Avatar
                alt={result.user.name ?? undefined}
                src={result.user.image ?? undefined}
              />
              {result.user.name}
            </ListItemButton>
          </Box>
        ))}
      </Box>
    </nav>
  );

  return (
    <>
      <Typography variant="h6" sx={{ px: 2.5, mb: 2 }}>
        Contacts ({totalResults})
      </Typography>

      {notFound ? renderNotFound : renderResults}
    </>
  );
}

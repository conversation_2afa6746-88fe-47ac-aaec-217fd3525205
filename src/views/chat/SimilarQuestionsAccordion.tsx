import { Box, Collapse, Divider, IconButton } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { type ReactNode, useState } from "react";
import { Iconify } from "~/components/iconify";

export default function SimilarQuestionsAccordion(props: {
  children: ReactNode;
}) {
  const [isOpen, setIsOpen] = useState(true);
  const theme = useTheme();

  return (
    <>
      <Box
        display="flex"
        justifyContent="end"
        height="60px"
        alignItems="center"
        onClick={() => setIsOpen(!isOpen)}
        sx={{ cursor: "pointer" }}
      >
        <Box
          sx={{
            fontWeight: "600",
            textAlign: "right",
            color: theme.vars.palette.text.secondary,
          }}
        >
          {isOpen ? "Response Library" : "Hide Response Library"}
        </Box>
        <IconButton
          sx={{
            // transform: `${isOpen && "rotate(180deg)"}`,
            transition: "transform .2s ease-out",
          }}
        >
          {isOpen ? (
            <Iconify
              icon="mdi:chevron-left"
              color={theme.palette.text.secondary}
            />
          ) : (
            <Iconify
              icon="mdi:chevron-down"
              color={theme.palette.text.secondary}
            />
          )}
        </IconButton>
      </Box>
      {!isOpen && (
        <Divider sx={{ width: "100%", opacity: 0.1, height: "0.5px" }} />
      )}
      <Collapse in={!isOpen} sx={{ textAlign: "left" }}>
        <Box paddingBottom="24px" width="100%">
          {props.children}
        </Box>
      </Collapse>
    </>
  );
}

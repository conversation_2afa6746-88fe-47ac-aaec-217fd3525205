"use client";

import { useCallback, useState } from "react";

import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid2";
import Typography from "@mui/material/Typography";

import { paths } from "src/routes/paths";

import { useBoolean } from "src/hooks/use-boolean";

import { _files, _folders } from "src/_mock";
import { CONFIG } from "src/config-global";
import { DashboardContent } from "src/layouts/Dashboard";

import { Iconify } from "src/components/iconify";
import { Scrollbar } from "src/components/scrollbar";
import { UploadBox } from "src/components/upload";

import { FileDataActivity } from "../../../data-room/FileDataActivity";
import { FileManagerFolderItem } from "../../../data-room/FileManagerFolderItem";
import { FileManagerNewFolderDialog } from "../../../data-room/FileManagerNewFolderDialog";
import { FileManagerPanel } from "../../../data-room/FileManagerPanel";
import { FileRecentItem } from "../../../data-room/FileRecentItem";
import { FileStorageOverview } from "../../../data-room/FileStorageOverview";
import { FileUpgrade } from "../../../data-room/FileUpgrade";
import { FileWidget } from "../../../data-room/FileWidget";

// ----------------------------------------------------------------------

const GB = 1000000000 * 24;

// ----------------------------------------------------------------------

export function OverviewFileView() {
  const [folderName, setFolderName] = useState("");

  const [files, setFiles] = useState<(File | string)[]>([]);

  const upload = useBoolean();

  const newFolder = useBoolean();

  const handleChangeFolderName = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setFolderName(event.target.value);
    },
    [],
  );

  const handleCreateNewFolder = useCallback(() => {
    newFolder.onFalse();
    setFolderName("");
    console.info("CREATE NEW FOLDER");
  }, [newFolder]);

  const handleDrop = useCallback(
    (acceptedFiles: File[]) => {
      setFiles([...files, ...acceptedFiles]);
    },
    [files],
  );

  const renderStorageOverview = (
    <FileStorageOverview
      total={GB}
      chart={{ series: 76 }}
      data={[
        {
          name: "Images",
          usedStorage: GB / 2,
          filesCount: 223,
          icon: (
            <Box
              component="img"
              src={`${CONFIG.site.basePath}/assets/icons/files/ic-img.svg`}
            />
          ),
        },
        {
          name: "Media",
          usedStorage: GB / 5,
          filesCount: 223,
          icon: (
            <Box
              component="img"
              src={`${CONFIG.site.basePath}/assets/icons/files/ic-video.svg`}
            />
          ),
        },
        {
          name: "Documents",
          usedStorage: GB / 5,
          filesCount: 223,
          icon: (
            <Box
              component="img"
              src={`${CONFIG.site.basePath}/assets/icons/files/ic-document.svg`}
            />
          ),
        },
        {
          name: "Other",
          usedStorage: GB / 10,
          filesCount: 223,
          icon: (
            <Box
              component="img"
              src={`${CONFIG.site.basePath}/assets/icons/files/ic-file.svg`}
            />
          ),
        },
      ]}
    />
  );

  return (
    <>
      <DashboardContent>
        <Grid container spacing={3}>
          <Grid size={12} sx={{ display: { xs: "block", sm: "none" } }}>
            {renderStorageOverview}
          </Grid>

          <Grid size={6}>
            <FileWidget
              title="Dropbox"
              value={GB / 10}
              total={GB}
              icon={`${CONFIG.site.basePath}/assets/icons/app/ic-app-dropbox.svg`}
            />
          </Grid>

          <Grid size={6}>
            <FileWidget
              title="Drive"
              value={GB / 5}
              total={GB}
              icon={`${CONFIG.site.basePath}/assets/icons/app/ic-app-drive.svg`}
            />
          </Grid>

          <Grid size={6}>
            <FileWidget
              title="OneDrive"
              value={GB / 2}
              total={GB}
              icon={`${CONFIG.site.basePath}/assets/icons/app/ic-app-onedrive.svg`}
            />
          </Grid>

          <Grid size={6}>
            <FileDataActivity
              title="Data activity"
              chart={{
                series: [
                  {
                    name: "Weekly",
                    categories: [
                      "Week 1",
                      "Week 2",
                      "Week 3",
                      "Week 4",
                      "Week 5",
                    ],
                    data: [
                      { name: "Images", data: [20, 34, 48, 65, 37] },
                      { name: "Media", data: [10, 34, 13, 26, 27] },
                      { name: "Documents", data: [10, 14, 13, 16, 17] },
                      { name: "Other", data: [5, 12, 6, 7, 8] },
                    ],
                  },
                  {
                    name: "Monthly",
                    categories: [
                      "Jan",
                      "Feb",
                      "Mar",
                      "Apr",
                      "May",
                      "Jun",
                      "Jul",
                      "Aug",
                      "Sep",
                    ],
                    data: [
                      {
                        name: "Images",
                        data: [10, 34, 13, 56, 77, 88, 99, 77, 45, 12, 43, 34],
                      },
                      {
                        name: "Media",
                        data: [10, 34, 13, 56, 77, 88, 99, 77, 45, 12, 43, 34],
                      },
                      {
                        name: "Documents",
                        data: [10, 34, 13, 56, 77, 88, 99, 77, 45, 12, 43, 34],
                      },
                      {
                        name: "Other",
                        data: [10, 34, 13, 56, 77, 88, 99, 77, 45, 12, 43, 34],
                      },
                    ],
                  },
                  {
                    name: "Yearly",
                    categories: [
                      "2018",
                      "2019",
                      "2020",
                      "2021",
                      "2022",
                      "2023",
                    ],
                    data: [
                      { name: "Images", data: [24, 34, 32, 56, 77, 48] },
                      { name: "Media", data: [24, 34, 32, 56, 77, 48] },
                      { name: "Documents", data: [24, 34, 32, 56, 77, 48] },
                      { name: "Other", data: [24, 34, 32, 56, 77, 48] },
                    ],
                  },
                ],
              }}
            />

            <Box sx={{ mt: 5 }}>
              <FileManagerPanel
                title="Folders"
                link={paths.dashboard.dataRoom}
                onOpen={newFolder.onTrue}
              />

              <Scrollbar sx={{ mb: 3, minHeight: 186 }}>
                <Box sx={{ gap: 3, display: "flex" }}>
                  {_folders.map((folder) => (
                    <FileManagerFolderItem
                      key={folder.id}
                      // @ts-ignore
                      folder={folder}
                      onDelete={() => console.info("DELETE", folder.id)}
                      sx={{
                        ...(_folders.length > 3 && {
                          width: 240,
                          flexShrink: 0,
                        }),
                      }}
                    />
                  ))}
                </Box>
              </Scrollbar>

              <FileManagerPanel
                title="Recent files"
                link={paths.dashboard.dataRoom}
                onOpen={upload.onTrue}
              />

              <Box sx={{ gap: 2, display: "flex", flexDirection: "column" }}>
                {_files.slice(0, 5).map((file) => (
                  <FileRecentItem
                    key={file.id}
                    // @ts-ignore
                    file={file}
                    onDelete={() => console.info("DELETE", file.id)}
                  />
                ))}
              </Box>
            </Box>
          </Grid>

          <Grid size={6}>
            <Box sx={{ gap: 3, display: "flex", flexDirection: "column" }}>
              <UploadBox
                onDrop={handleDrop}
                placeholder={
                  <Box
                    sx={{
                      gap: 0.5,
                      display: "flex",
                      alignItems: "center",
                      color: "text.disabled",
                      flexDirection: "column",
                    }}
                  >
                    <Iconify icon="eva:cloud-upload-fill" width={40} />
                    <Typography variant="body2">Upload file</Typography>
                  </Box>
                }
                sx={{
                  py: 2.5,
                  width: "auto",
                  height: "auto",
                  borderRadius: 1.5,
                }}
              />

              <Box sx={{ display: { xs: "none", sm: "block" } }}>
                {renderStorageOverview}
              </Box>

              <FileUpgrade />
            </Box>
          </Grid>
        </Grid>
      </DashboardContent>

      <FileManagerNewFolderDialog
        open={upload.value}
        onClose={upload.onFalse}
        orgId={""}
        orgSlug={""}
      />

      <FileManagerNewFolderDialog
        open={newFolder.value}
        onClose={newFolder.onFalse}
        title="New Folder"
        folderName={folderName}
        onChangeFolderName={handleChangeFolderName}
        onCreate={handleCreateNewFolder}
        orgId={""}
        orgSlug={""}
      />
    </>
  );
}

"use client";

import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";

import { DashboardContent } from "src/layouts/Dashboard";

import { useAuth } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Button, Typography } from "@mui/material";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Field, Form } from "src/components/hook-form";
import { z as zod } from "zod";
import Loading from "~/app/dashboard/portfolio/loading";
import { ConfirmDialog } from "~/components/custom-dialog";
import { toast } from "~/components/snackbar";
import { useBoolean } from "~/hooks/use-boolean";
import { api } from "~/trpc/react";
import { ActivePortfolio } from "../active-portfolio";
import { PortfolioWidgetSummary } from "../portfolio-widget-summary";

// ----------------------------------------------------------------------

export const NewCompanySchema = zod.object({
  companyName: zod.string().min(1, { message: "Company name is required" }),
  contactPersonEmail: zod
    .string()
    .min(1, { message: "Contact person email is required" })
    .email({ message: "Contact person email must be a valid email address" }),
});

export type NewCompanySchemaType = zod.infer<typeof NewCompanySchema>;

export function OverviewPortfolioView() {
  const theme = useTheme();
  const invite = useBoolean();
  const [companyName, setCompanyName] = useState("");
  const [contactPersonEmail, setContactPersonEmail] = useState("");

  const { isLoaded, orgId } = useAuth();
  const inviteOrganization = api.organization.invite.useMutation();
  const org = api.organization.get.useQuery(
    { clerkId: orgId ?? "" },
    { enabled: !!orgId },
  );

  const methods = useForm<NewCompanySchemaType>({
    resolver: zodResolver(NewCompanySchema),
    defaultValues: {
      companyName: "",
      contactPersonEmail: "",
    },
  });

  const {
    reset,
    watch,
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const values = watch();

  const onSubmit = handleSubmit((data) => {
    try {
      inviteOrganization.mutate(
        {
          name: data.companyName,
          email: data.contactPersonEmail,
        },
        {
          onSuccess: () => {
            reset();
            invite.onFalse();
            toast.success("Organization invited successfully");
          },
          onError: (error) => {
            toast.error("Something went wrong");
            console.error(error);
          },
        },
      );
    } catch (error) {
      toast.error("Something went wrong");
      console.error(error);
    }
  });

  if (!isLoaded) return <Loading />;

  const portfolio = org.data?.borrowers.map((org, index) => {
    return {
      id: org.id,
      companyName: org.name,
      risk: "25%",
      notional: "$15,000",
      coupon: "25%",
      maturity: "12 Months",
      type: index % 10 !== 0 ? "Secured" : "Unsecured",
      pd: "25%",
      lgd: "25%",
    };
  });

  console.log(org.data);
  console.log("PORTFOLIO", portfolio);

  return (
    <DashboardContent>
      <Grid container spacing={3}>
        <Grid size={12}>
          <Box
            sx={{
              backgroundColor: theme.vars.palette.grey[200],
              padding: theme.spacing(2),
              borderRadius: theme.spacing(1),
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="h6">
              Don&apos;t see a company you&apos;re looking for? Invite them to
              join Virgil AI
            </Typography>
            <Button color="primary" variant="contained" onClick={invite.onTrue}>
              Invite a Company
            </Button>
          </Box>
        </Grid>
        <Grid size={3}>
          <PortfolioWidgetSummary
            title="Average Concentration"
            percent={2.6}
            total={86}
            chart={{
              categories: [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
              ],
              series: [15, 18, 12, 51, 68, 11, 39, 37],
            }}
          />
        </Grid>
        <Grid size={3}>
          <PortfolioWidgetSummary
            title="Loan Delinquency Ratio"
            percent={-16.2}
            total={33}
            chart={{
              colors: [theme.vars.palette.info.main],
              categories: [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
              ],
              series: [20, 41, 63, 33, 28, 35, 50, 46],
            }}
          />
        </Grid>
        <Grid size={3}>
          <PortfolioWidgetSummary
            title="Average Maturity"
            percent={-16.2}
            total={33}
            chart={{
              colors: [theme.vars.palette.error.main],
              categories: [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
              ],
              series: [18, 19, 31, 8, 16, 37, 12, 33],
            }}
          />
        </Grid>
        <Grid size={3}>
          <PortfolioWidgetSummary
            title="Average Coupon"
            percent={-16.2}
            total={33}
            chart={{
              colors: [theme.vars.palette.error.main],
              categories: [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
              ],
              series: [18, 19, 31, 8, 16, 37, 12, 33],
            }}
          />
        </Grid>

        <Grid size={12}>
          <ActivePortfolio
            title="Active Portfolio"
            subheader="$4,500,000,000"
            tableData={portfolio ?? []}
            headLabel={[
              { id: "company", label: "Company" },
              { id: "risk", label: "Risk" },
              { id: "notional", label: "Notional" },
              { id: "coupon", label: "Coupon" },
              { id: "maturity", label: "Maturity" },
              { id: "type", label: "Type" },
              { id: "pd", label: "PD" },
              { id: "lgd", label: "LGD" },
              { id: "action", label: "" },
            ]}
          />
        </Grid>
      </Grid>

      <ConfirmDialog
        open={invite.value}
        onClose={invite.onFalse}
        title="Invite a Company"
        content={
          <Box sx={{}}>
            <Typography variant="body1">
              Enter the company details below. The contact person will receive
              an email with a link to join Virgil AI as a member of the newly
              created company.
            </Typography>
            <Form methods={methods} onSubmit={onSubmit}>
              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid size={6}>
                  <Field.Text name="companyName" label="Company Name" />
                </Grid>
                <Grid size={6}>
                  <Field.Text
                    name="contactPersonEmail"
                    label="Contact Person Email"
                  />
                </Grid>
              </Grid>
            </Form>
          </Box>
        }
        action={
          <Button
            type="submit"
            variant="contained"
            loading={isSubmitting}
            onClick={onSubmit}
          >
            Invite
          </Button>
        }
      />
    </DashboardContent>
  );
}

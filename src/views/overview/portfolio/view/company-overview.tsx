"use client";

import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

import { useTabs } from "src/hooks/use-tabs";
import { DashboardContent } from "src/layouts/Dashboard";

import { Typography } from "@mui/material";
import { type Org } from "@prisma/client";
import { ChatView } from "~/views/chat/view";
import { DataRoomView } from "~/views/data-room/view";
import { FiltersProvider } from "~/views/data-room/FundsAndTagsFilter/FiltersContext";

// ----------------------------------------------------------------------

type Props = {
  org: Org;
};

const TABS = [
  { value: "profile", label: "Profile" },
  { value: "financials", label: "Financials" },
  {
    value: "industry",
    label: "Industry",
  },
  { value: "news", label: "News" },
  { value: "alt-data", label: "Alt Data" },
  { value: "integrations", label: "Integrations" },
  { value: "files", label: "Files" },
  { value: "chat", label: "Chat" },
];

export function CompanyOverview({ org }: Props) {
  const theme = useTheme();
  const tabs = useTabs("profile");

  return (
    <DashboardContent>
      <Grid container spacing={3}>
        <Grid size={12}>
          <Typography variant="h2">{`${org.name}`}</Typography>
          <Typography variant="h6">{`orgdomain.com`}</Typography>
        </Grid>
      </Grid>
      <Tabs
        value={tabs.value}
        onChange={tabs.onChange}
        sx={{ mb: { xs: 3, md: 5 } }}
      >
        {TABS.map((tab) => (
          <Tab key={tab.value} label={tab.label} value={tab.value} />
        ))}
      </Tabs>

      {tabs.value === "files" && (
        <FiltersProvider>
          <DataRoomView org={org} />
        </FiltersProvider>
      )}
      {tabs.value === "chat" && <ChatView />}
    </DashboardContent>
  );
}

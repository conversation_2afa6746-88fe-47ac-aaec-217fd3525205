import React, { createContext, useContext, useCallback, useEffect, useState, ReactNode, useMemo } from "react";
import { DDQTable } from "~/lib/features/documentDDQSlice";

interface DDQTableSelectContextType {
    selectedIds: Set<string>;
    setSelectedIds: React.Dispatch<React.SetStateAction<Set<string>>>;
    isAllSelected: boolean;
    handleSelectAll: () => void;
    handleSelectSingle: (id: string) => void;
    totalSelected: number;
    totalCount: number;
}

const DDQTableSelectContext = createContext<DDQTableSelectContextType | undefined>(undefined);

interface DDQTableSelectProviderProps {
    children: ReactNode;
    tables: DDQTable[];
}

export const DDQTableSelectProvider: React.FC<DDQTableSelectProviderProps> = ({
    children,
    tables,
}) => {
    const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
    const [isAllSelected, setIsAllSelected] = useState(false);
    const [totalSelected, setTotalSelected] = useState(0);

    const handleSelectAll = () => {
        if (tables.every((t) => selectedIds.has(t.id))) {
            setSelectedIds((prev) =>
                new Set([...prev].filter((id) => !tables.map((t) => t.id).includes(id))),
            );
        } else {
            setSelectedIds((prev) => new Set([...prev, ...tables.map((t) => t.id)]));
        }
    };

    const handleSelectSingle = useCallback(
        (id: string) => {
            setSelectedIds((prev) =>
                prev.has(id)
                    ? new Set([...prev].filter((prevId) => prevId !== id))
                    : new Set([...prev, id]),
            );
        },
        [setSelectedIds],
    );

    // Reset when questions change (page change)
    useEffect(() => {
        setTotalSelected(
            tables.filter((t) => selectedIds.has(t.id)).length,
        );
        setIsAllSelected(tables.every((t) => selectedIds.has(t.id)));
    }, [tables, selectedIds]);

    const value: DDQTableSelectContextType = useMemo(() => ({
        selectedIds,
        setSelectedIds,
        isAllSelected,
        handleSelectAll,
        handleSelectSingle,
        totalSelected,
        totalCount: tables.length,
    }), [selectedIds, setSelectedIds, isAllSelected, handleSelectAll, handleSelectSingle, totalSelected, tables]);

    return (
        <DDQTableSelectContext.Provider value={value}>
            {children}
        </DDQTableSelectContext.Provider>
    );
};

export const useDDQTableSelect = (): DDQTableSelectContextType => {
    const context = useContext(DDQTableSelectContext);
    if (context === undefined) {
        throw new Error("useDDQTableSelect must be used within a DDQTableSelectProvider");
    }
    return context;
}; 
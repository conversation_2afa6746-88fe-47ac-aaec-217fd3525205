import * as React from 'react';
import { But<PERSON> } from '~/v2/components/ui';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from '~/v2/components/ui/DropdownMenu';
import { Typography } from '~/v2/components/ui/Typography';
import { Loader2, CheckCircle, XCircle, X, Send } from 'lucide-react';
import { GenericIcon } from '~/components/virgil/Icons/GenericIcon';
import { SplitButtonProps } from './types';
import { Textarea } from '~/v2/components/ui/Textarea';

type Props<TData, TError, TVariables, TContext> = SplitButtonProps<TData, TError, TVariables, TContext>;

export default function SplitButtonWithPrompt<TData, TError, TVariables, TContext>({
    inputConfig,
    buttonLabel,
    popupTitle,
    mutationConfig,
    pendingText,
    buttonIcon,
    ...buttonProps
}: Props<TData, TError, TVariables, TContext>) {
    const [open, setOpen] = React.useState(false);
    const [inputValue, setInputValue] = React.useState('');
    const { ...restBtnProps } = buttonProps;

    const handleToggle = (e: React.MouseEvent<HTMLElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setOpen((prevOpen) => !prevOpen);
        mutationConfig.mutation.reset();
    };

    const handleClose = () => {
        setOpen(false);
        mutationConfig.mutation.reset();
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        event.stopPropagation();
        setInputValue(event.target.value);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        event.stopPropagation();
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            mutationConfig.onSubmit(inputValue.trim());
        }
        if (event.key === 'Escape') {
            event.preventDefault();
            handleClose();
        }
    };

    const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        e.stopPropagation();
        mutationConfig.onSubmit(inputValue.trim());
    };

    const handleUndo = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        event.stopPropagation();
        mutationConfig.onUndo?.();
    };

    const handleTryAgain = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        event.stopPropagation();
        mutationConfig.onTryAgain?.(inputValue.trim());
    };

    const handleCancel = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        event.stopPropagation();
        mutationConfig.onCancel?.();
    };

    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button
                    aria-controls={open ? 'split-button-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-label={buttonLabel}
                    aria-haspopup="menu"
                    onClick={handleToggle}
                    title={buttonLabel}
                    {...restBtnProps}
                >
                    {buttonIcon || buttonLabel}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                align="start"
                className="w-80 p-4 space-y-2 z-1000"
                onInteractOutside={handleClose}
            >
                <div className="flex items-start justify-between">
                    <Typography variant="boldBody">
                        {popupTitle || "\u00A0"}
                    </Typography>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClose}
                        className="h-6 w-6 p-0"
                    >
                        <GenericIcon icon={X} />
                    </Button>
                </div>

                {mutationConfig.mutation.status === "idle" && (
                    <div className="space-y-2">
                        <div className="relative">
                            <Textarea
                                placeholder={inputConfig.placeholder}
                                value={inputValue}
                                rows={2}
                                onChange={handleInputChange}
                                onKeyDown={handleKeyDown}
                                className="pr-10 w-full rounded-md border bg-transparent px-3 py-2 text-xs shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none max-h-16"
                                autoFocus
                            />
                            <Button
                                size="sm"
                                variant="ghost"
                                onClick={handleSubmit}
                                disabled={!inputValue?.trim()?.length || mutationConfig.mutation.isPending}
                                className="absolute right-2 top-1 h-6 w-6 p-0 text-primary"
                            >
                                <Send className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                )}

                {mutationConfig.mutation.status === "pending" && (
                    <div className="flex items-center justify-between space-x-2 py-2">
                        <div className="flex items-center space-x-2">
                            <GenericIcon icon={Loader2} className="animate-spin" />
                            <Typography variant="caption" className="text-sm text-muted-foreground">
                                {pendingText || "\u00A0"}
                            </Typography>
                        </div>
                        <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                    </div>
                )}

                {mutationConfig.mutation.status === "success" && (
                    <div className="flex items-center justify-between space-x-2 py-2">
                        <div className="flex items-center space-x-2">
                            <GenericIcon icon={CheckCircle} className="text-green-500" />
                            <Typography variant="caption" className="text-sm text-green-600">Done!</Typography>
                        </div>
                        <div className="flex space-x-2">
                            {mutationConfig.onUndo && (
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={handleUndo}
                                >
                                    Undo
                                </Button>
                            )}
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={handleTryAgain}
                            >
                                Try Again
                            </Button>
                        </div>
                    </div>
                )}

                {mutationConfig.mutation.status === "error" && (
                    <div className="flex items-center justify-between space-x-2 py-2">
                        <div className="flex items-center space-x-2">
                            <GenericIcon icon={XCircle} className="text-red-500" />
                            <Typography variant="caption" className="text-sm text-red-600">Something went wrong!</Typography>
                        </div>
                        <div className="flex space-x-2">
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={handleUndo}
                            >
                                Undo
                            </Button>
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={handleTryAgain}
                            >
                                Try Again
                            </Button>
                        </div>
                    </div>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
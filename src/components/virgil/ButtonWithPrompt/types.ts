import { UseMutationResult } from "@tanstack/react-query";
import { ButtonProps } from "~/v2/components/ui/Button";

export interface InputConfig {
  placeholder?: string;
}

export interface MutationProps<TData, TError, TVariables, TContext> {
  mutation: UseMutationResult<TData, TError, TVariables, TContext>;
  onCancel?: () => void;
  onUndo?: () => void;
  onTryAgain?: (value: string) => void;
  onSubmit: (value: string) => void;
}

export interface SplitButtonProps<TData, TError, TVariables, TContext>
  extends Omit<ButtonProps, "onClick"> {
  pendingText?: string;
  inputConfig: InputConfig;
  buttonLabel: string;
  popupTitle?: string;
  mutationConfig: MutationProps<TData, TError, TVariables, TContext>;
  buttonIcon?: React.ReactNode;
}

import type { Meta, StoryObj } from '@storybook/nextjs';
import ButtonWithPrompt from './ButtonWithPrompt';
import SendIcon from '@mui/icons-material/Send';
import ErrorIcon from '@mui/icons-material/Error';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const useMockMutation = (status: 'idle' | 'pending' | 'success' | 'error' = 'idle') => {
    return {
        status,
        mutate: () => { },
        reset: () => { },
        data: undefined,
        error: undefined,
        isError: status === 'error',
        isSuccess: status === 'success',
        isLoading: status === 'pending',
    } as any;
};

const meta: Meta<typeof ButtonWithPrompt> = {
    title: 'Components/ButtonWithPrompt',
    component: ButtonWithPrompt,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ButtonWithPrompt>;

export const Default: Story = {
    args: {
        buttonLabel: 'Click Me',
        popupTitle: 'How would you like to regenerate this response? Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation(),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const WithoutPopupTitle: Story = {
    args: {
        buttonLabel: 'Click Me',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation(),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const Disabled: Story = {
    args: {
        buttonLabel: 'Click Me',
        disabled: true,
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation(),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const Loading: Story = {
    args: {
        buttonLabel: 'Click Me',
        pendingText: 'Generating...',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation('pending'),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const CustomVariant: Story = {
    args: {
        buttonLabel: 'Click Me',
        variant: 'default',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation(),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const ErrorState: Story = {
    args: {
        buttonLabel: 'Click Me',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation('error'),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const SuccessState: Story = {
    args: {
        buttonLabel: 'Click Me',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation('success'),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const WithCustomTitle: Story = {
    args: {
        buttonLabel: 'Click Me',
        popupTitle: 'Custom Title with Instructions',
        inputConfig: {
            placeholder: 'Type something...',
        },
        mutationConfig: {
            mutation: useMockMutation(),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

export const SuccessScreen: Story = {
    args: {
        buttonLabel: 'Regenerate',
        variant: 'outline',
        inputConfig: {
            placeholder: 'Tell Virgil how to improve.',
        },
        mutationConfig: {
            mutation: useMockMutation('success'),
            onSubmit: (value) => console.log('Submitted:', value),
            onCancel: () => console.log('Canceled'),
            onUndo: () => console.log('Undone'),
            onTryAgain: (value) => console.log('Try Again:', value),
        },
    },
};

import React from "react";
import { Meta, StoryFn } from "@storybook/nextjs";
import { AnsweredLabel } from "./AnsweredLabel";
import { InsufficientDataLabel } from "./InsufficientDataLabel";
import { NoResponseLabel } from "./NoResponseLabel";
import { ResponseLibraryResponseLabel } from "./ResponseLibraryResponseLabel";
import { AIGeneratedResponseLabel } from "./AIGeneratedResponseLabel";
import { ApprovedStatusLabel } from "./ApprovedStatusLabel";
import { PendingReviewStatusLabel } from "./PendingReviewStatusLabel";
import { RejectedStatusLabel } from "./RejectedStatusLabel";
import { DraftStatusLabel } from "./DraftStatusLabel";

// Meta information for the story
export default {
    title: "Components/Labels",
    component: AnsweredLabel,
    parameters: {
        layout: "padded",
    },
} as Meta;

// All Labels Showcase
export const AllLabelsShowcase: StoryFn = () => (
    <div className="space-y-6">
        <div>
            <h3 className="text-lg font-semibold mb-4">All Label Components</h3>
            <div className="flex flex-wrap gap-4">
                <AnsweredLabel />
                <InsufficientDataLabel />
                <NoResponseLabel />
                <ResponseLibraryResponseLabel />
                <AIGeneratedResponseLabel />
            </div>
            <div className="mt-6">
                <h3 className="text-lg font-semibold mb-4">Status Labels</h3>
                <div className="flex flex-wrap gap-4">
                    <DraftStatusLabel />
                    <PendingReviewStatusLabel />
                    <ApprovedStatusLabel />
                    <RejectedStatusLabel />
                </div>
            </div>
        </div>
    </div>
);

AllLabelsShowcase.storyName = "All Labels Showcase";

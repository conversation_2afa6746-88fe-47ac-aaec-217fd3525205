import { Bell } from "lucide-react"
import { GenericLabel } from "./GenericLabel"
import { cn } from "~/v2/lib/utils"

export const InsufficientDataLabel = ({ className, label = "Missing Data" }: { className?: string, label?: string }) => {
    return (
        <GenericLabel
            label={label}
            icon={Bell}
            className={cn("text-orange-600 bg-orange-100", className)}
        />
    )
}
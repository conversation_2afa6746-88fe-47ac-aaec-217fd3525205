import { Download } from "lucide-react"
import { GenericLabel } from "./GenericLabel"
import { cn } from "~/v2/lib/utils"

export const AnsweredLabel = ({ className, label = "ANSWERED" }: { className?: string, label?: string }) => {
    return (
        <GenericLabel
            label={label}
            icon={Download}
            className={cn("bg-neutral-100 text-black", className)}
        />
    )
}
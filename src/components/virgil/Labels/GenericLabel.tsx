import { LucideIcon } from "lucide-react"
import { cn } from "~/v2/lib/utils"

interface GenericLabelProps {
    className?: string
    label: string
    icon?: LucideIcon
}

export const GenericLabel = ({
    className,
    label,
    icon: Icon
}: GenericLabelProps) => {
    return (
        <span className={cn(
            "flex items-center py-0.5 px-1 rounded-[4px] text-[10px] leading-[12px] font-semibold",
            className
        )}>
            {Icon && (
                <Icon
                    width={14}
                    height={14}
                    className="mr-0.5"
                />
            )}
            {label}
        </span>
    )
}

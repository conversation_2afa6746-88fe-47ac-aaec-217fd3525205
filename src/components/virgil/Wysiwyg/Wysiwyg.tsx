import "./styles.css";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeHighlightNode, CodeNode } from "@lexical/code";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { $convertFromMarkdownString } from "@lexical/markdown";
import { EditorState, LexicalEditor } from "lexical";
import { PLAYGROUND_TRANSFORMERS } from "./transformers/playgroundTransformers";
import Editor from "./Editor";
import { TableContext } from "./plugins/TablePlugin";
import { ToolbarContext } from "./context/ToolbarContext";
import VirgilEditorTheme from './themes/VirgilEditorTheme';


const editorConfig = {
  namespace: "virgil",
  // The editor theme
  theme: VirgilEditorTheme,
  // Handling of errors during update
  onError(error: Error) {
    throw error;
  },
  // Any custom nodes go here
  nodes: [
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    CodeNode,
    CodeHighlightNode,
    TableNode,
    TableCellNode,
    TableRowNode,
    AutoLinkNode,
    LinkNode
  ]
};
export type WysiwygProps = {
  markdownString: string;
  onChange?: (editorState: EditorState, editor: LexicalEditor) => void;
  editorStateRef: React.RefObject<LexicalEditor | null>;
  editorConentClass?: string;
}

export default function Wysiwyg({ markdownString, onChange, editorStateRef, editorConentClass }: WysiwygProps) {
  return (
    <LexicalComposer initialConfig={{
      ...editorConfig,
      editorState: () => $convertFromMarkdownString(markdownString, PLAYGROUND_TRANSFORMERS)
    }}>
      <TableContext>
        <ToolbarContext>
          <Editor editorStateRef={editorStateRef} editorConentClass={editorConentClass} />
        </ToolbarContext>
      </TableContext>
    </LexicalComposer>
  );
}

import React from "react";
import Wysiwyg from "./Wysiwyg"; // Adjust the path based on your project structure
import { Meta, StoryFn } from "@storybook/nextjs";
import { EditorState, LexicalEditor } from "lexical";
import { WysiwygProps } from "./Wysiwyg";
import { $convertToMarkdownString } from "@lexical/markdown";
import { PLAYGROUND_TRANSFORMERS } from "./transformers/playgroundTransformers";
// Meta information for the story
export default {
    title: "Components/Wysiwyg", // This defines the category and name in Storybook
    component: Wysiwyg,
} as Meta;

// Template for rendering the Wysiwyg component
const Template: StoryFn<WysiwygProps> = (args: WysiwygProps) => <Wysiwyg {...args} />;

// Default story
export const Default = Template.bind({});
const editorStateRef = React.createRef<LexicalEditor>();
Default.args = {
    markdownString: `
# Header 1
## Header 2
### Header 3
#### Header 4
##### Header 5
###### Header 6

Bullet list
- List item 1
- List item 2
- List item 3

Numbered list
1. List item 1
2. List item 2
3. List item 3

Link
[Google](https://www.google.com)


Table
| Header 1 | Header 2 | Header 3 |
| -------- | -------- | -------- |
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
    `,
    onChange: (editorState: EditorState, editor: LexicalEditor) => {
        editorState.read(() => {
            const markdown = $convertToMarkdownString(PLAYGROUND_TRANSFORMERS);
            console.log("This is how you access the markdown", markdown);

            console.log(editorStateRef.current?.getEditorState().toJSON());
        })
    },
    editorStateRef: editorStateRef
};


/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 */
.<PERSON>EditorTheme__ltr {
  text-align: left;
}
.VirgilEditorTheme__rtl {
  text-align: right;
}
.VirgilEditorTheme__paragraph {
  margin: 0;
  position: relative;
}
.VirgilEditorTheme__quote {
  margin: 0;
  margin-left: 20px;
  margin-bottom: 10px;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}
.VirgilEditorTheme__h1 {
  font-size: 30px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #333;
}
.VirgilEditorTheme__h2 {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 12px 0;
  color: #444;
}
.VirgilEditorTheme__h3 {
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #555;
}
.VirgilEditorTheme__indent {
  --lexical-indent-base-value: 40px;
}
.VirgilEditorTheme__textBold {
  font-weight: bold;
}
.<PERSON>EditorTheme__paragraph mark {
  background-color: unset;
}
.VirgilEditorTheme__textHighlight {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
}
.<PERSON>EditorTheme__textItalic {
  font-style: italic;
}
.VirgilEditorTheme__textUnderline {
  text-decoration: underline;
}

.VirgilEditorTheme__textStrikethrough {
  text-decoration: line-through;
}

.VirgilEditorTheme__textUnderlineStrikethrough {
  text-decoration: underline line-through;
}

.VirgilEditorTheme__tabNode {
  position: relative;
  text-decoration: none;
}

.VirgilEditorTheme__tabNode.VirgilEditorTheme__textUnderline::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0.15em;
  border-bottom: 0.1em solid currentColor;
}

.VirgilEditorTheme__tabNode.VirgilEditorTheme__textStrikethrough::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0.69em;
  border-top: 0.1em solid currentColor;
}

.VirgilEditorTheme__tabNode.VirgilEditorTheme__textUnderlineStrikethrough::before,
.VirgilEditorTheme__tabNode.VirgilEditorTheme__textUnderlineStrikethrough::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
}

.VirgilEditorTheme__tabNode.VirgilEditorTheme__textUnderlineStrikethrough::before {
  top: 0.69em;
  border-top: 0.1em solid currentColor;
}

.VirgilEditorTheme__tabNode.VirgilEditorTheme__textUnderlineStrikethrough::after {
  bottom: 0.05em;
  border-bottom: 0.1em solid currentColor;
}

.VirgilEditorTheme__textSubscript {
  font-size: 0.8em;
  vertical-align: sub !important;
}
.VirgilEditorTheme__textSuperscript {
  font-size: 0.8em;
  vertical-align: super;
}
.VirgilEditorTheme__textCode {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}
.VirgilEditorTheme__textLowercase {
  text-transform: lowercase;
}
.VirgilEditorTheme__textUppercase {
  text-transform: uppercase;
}
.VirgilEditorTheme__textCapitalize {
  text-transform: capitalize;
}
.VirgilEditorTheme__hashtag {
  background-color: rgba(88, 144, 255, 0.15);
  border-bottom: 1px solid rgba(88, 144, 255, 0.3);
}
.VirgilEditorTheme__link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}
.VirgilEditorTheme__link:hover {
  text-decoration: underline;
  cursor: pointer;
}
.VirgilEditorTheme__blockCursor {
  display: block;
  pointer-events: none;
  position: absolute;
}
.VirgilEditorTheme__blockCursor:after {
  content: '';
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: CursorBlink 1.1s steps(2, start) infinite;
}
@keyframes CursorBlink {
  to {
    visibility: hidden;
  }
}
.VirgilEditorTheme__code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  overflow-x: auto;
  position: relative;
  tab-size: 2;
}
.VirgilEditorTheme__code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}
.VirgilEditorTheme__tableScrollableWrapper {
  overflow-x: auto;
  margin: 0px 25px 30px 0px;
}
.VirgilEditorTheme__tableScrollableWrapper > .VirgilEditorTheme__table {
  /* Remove the table's vertical margin and put it on the wrapper */
  margin-top: 0;
  margin-bottom: 0;
}
.VirgilEditorTheme__tableAlignmentCenter {
  margin-left: auto;
  margin-right: auto;
}
.VirgilEditorTheme__tableAlignmentRight {
  margin-left: auto;
}
.VirgilEditorTheme__table {
  border-collapse: collapse;
  border-spacing: 0;
  overflow-y: scroll;
  overflow-x: scroll;
  table-layout: fixed;
  width: fit-content;
  margin-top: 25px;
  margin-bottom: 30px;
}
.VirgilEditorTheme__tableScrollableWrapper.VirgilEditorTheme__tableFrozenRow {
  /* position:sticky needs overflow:clip or visible
     https://github.com/w3c/csswg-drafts/issues/865#issuecomment-350585274 */
  overflow-x: clip;
}
.VirgilEditorTheme__tableFrozenRow tr:nth-of-type(1) > td {
  overflow: clip;
  background-color: #ffffff;
  position: sticky;
  z-index: 2;
  top: 44px;
}
.VirgilEditorTheme__tableFrozenRow tr:nth-of-type(1) > th {
  overflow: clip;
  background-color: #f2f3f5;
  position: sticky;
  z-index: 2;
  top: 44px;
}
.VirgilEditorTheme__tableFrozenRow tr:nth-of-type(1) > th:after,
.VirgilEditorTheme__tableFrozenRow tr:nth-of-type(1) > td:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-bottom: 1px solid #bbb;
}
.VirgilEditorTheme__tableFrozenColumn tr > td:first-child {
  background-color: #ffffff;
  position: sticky;
  z-index: 2;
  left: 0;
}
.VirgilEditorTheme__tableFrozenColumn tr > th:first-child {
  background-color: #f2f3f5;
  position: sticky;
  z-index: 2;
  left: 0;
}
.VirgilEditorTheme__tableFrozenColumn tr > :first-child::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  border-right: 1px solid #bbb;
}
.VirgilEditorTheme__tableRowStriping tr:nth-child(even) {
  background-color: #f2f5fb;
}
.VirgilEditorTheme__tableSelection *::selection {
  background-color: transparent;
}
.VirgilEditorTheme__tableSelected {
  outline: 2px solid rgb(60, 132, 244);
}
.VirgilEditorTheme__tableCell {
  border: 1px solid #bbb;
  /* width: 75px; */
  vertical-align: top;
  text-align: start;
  /* padding: 6px 8px; */
  position: relative;
  outline: none;
  overflow: auto;
}
/*
  A firefox workaround to allow scrolling of overflowing table cell
  ref: https://bugzilla.mozilla.org/show_bug.cgi?id=1904159
*/
.VirgilEditorTheme__tableCell > * {
  overflow: inherit;
}
.VirgilEditorTheme__tableCellResizer {
  position: absolute;
  right: -4px;
  height: 100%;
  width: 8px;
  cursor: ew-resize;
  z-index: 10;
  top: 0;
}
.VirgilEditorTheme__tableCellHeader {
  background-color: #f2f3f5;
  text-align: start;
}
.VirgilEditorTheme__tableCellSelected {
  caret-color: transparent;
}
.VirgilEditorTheme__tableCellSelected::after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: highlight;
  mix-blend-mode: multiply;
  content: '';
  pointer-events: none;
}
.VirgilEditorTheme__tableAddColumns {
  position: absolute;
  background-color: #eee;
  height: 100%;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
}
.VirgilEditorTheme__tableAddColumns:after {
  background-image: url(/assets/wysiwyg/icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
.VirgilEditorTheme__tableAddColumns:hover,
.VirgilEditorTheme__tableAddRows:hover {
  background-color: #c9dbf0;
}
.VirgilEditorTheme__tableAddRows {
  position: absolute;
  width: calc(100% - 25px);
  background-color: #eee;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
}
.VirgilEditorTheme__tableAddRows:after {
  background-image: url(/assets/wysiwyg/icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
@keyframes table-controls {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.VirgilEditorTheme__tableCellResizeRuler {
  display: block;
  position: absolute;
  width: 1px;
  background-color: rgb(60, 132, 244);
  height: 100%;
  top: 0;
}
.VirgilEditorTheme__tableCellActionButtonContainer {
  display: block;
  right: 5px;
  top: 6px;
  position: absolute;
  z-index: 4;
  width: 20px;
  height: 20px;
}
.VirgilEditorTheme__tableCellActionButton {
  background-color: #eee;
  display: block;
  border: 0;
  border-radius: 20px;
  width: 20px;
  height: 20px;
  color: #222;
  cursor: pointer;
}
.VirgilEditorTheme__tableCellActionButton:hover {
  background-color: #ddd;
}
.VirgilEditorTheme__characterLimit {
  display: inline;
  background-color: #ffbbbb !important;
}
.VirgilEditorTheme__ol1,
.VirgilEditorTheme__ol2,
.VirgilEditorTheme__ol3,
.VirgilEditorTheme__ol4,
.VirgilEditorTheme__ol5,
.VirgilEditorTheme__ul {
  padding-left: 20px;
  margin: 0 0 16px 0;
  list-style-position: inside;
}
.VirgilEditorTheme__listItem {
  margin: 0 0 8px 0;
  line-height: 1.6;
}
.VirgilEditorTheme__listItem::marker {
  color: var(--listitem-marker-color);
  background-color: var(--listitem-marker-background-color);
  font-family: var(--listitem-marker-font-family);
  font-size: var(--listitem-marker-font-size);
}
.VirgilEditorTheme__listItemChecked,
.VirgilEditorTheme__listItemUnchecked {
  position: relative;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 24px;
  padding-right: 24px;
  list-style-type: none;
  outline: none;
}
.VirgilEditorTheme__listItemChecked {
  text-decoration: line-through;
}
.VirgilEditorTheme__listItemUnchecked:before,
.VirgilEditorTheme__listItemChecked:before {
  content: '';
  width: 16px;
  height: 16px;
  top: 2px;
  left: 0;
  cursor: pointer;
  display: block;
  background-size: cover;
  position: absolute;
}
.VirgilEditorTheme__listItemUnchecked[dir='rtl']:before,
.VirgilEditorTheme__listItemChecked[dir='rtl']:before {
  left: auto;
  right: 0;
}
.VirgilEditorTheme__listItemUnchecked:focus:before,
.VirgilEditorTheme__listItemChecked:focus:before {
  box-shadow: 0 0 0 2px #a6cdfe;
  border-radius: 2px;
}
.VirgilEditorTheme__listItemUnchecked:before {
  border: 1px solid #999;
  border-radius: 2px;
}
.VirgilEditorTheme__listItemChecked:before {
  border: 1px solid rgb(61, 135, 245);
  border-radius: 2px;
  background-color: #3d87f5;
  background-repeat: no-repeat;
}
.VirgilEditorTheme__listItemChecked:after {
  content: '';
  cursor: pointer;
  border-color: #fff;
  border-style: solid;
  position: absolute;
  display: block;
  top: 6px;
  width: 3px;
  left: 7px;
  right: 7px;
  height: 6px;
  transform: rotate(45deg);
  border-width: 0 2px 2px 0;
}
.VirgilEditorTheme__nestedListItem {
  list-style-type: none;
}
.VirgilEditorTheme__nestedListItem:before,
.VirgilEditorTheme__nestedListItem:after {
  display: none;
}
.VirgilEditorTheme__tokenComment {
  color: slategray;
}
.VirgilEditorTheme__tokenPunctuation {
  color: #999;
}
.VirgilEditorTheme__tokenProperty {
  color: #905;
}
.VirgilEditorTheme__tokenSelector {
  color: #690;
}
.VirgilEditorTheme__tokenOperator {
  color: #9a6e3a;
}
.VirgilEditorTheme__tokenAttr {
  color: #07a;
}
.VirgilEditorTheme__tokenVariable {
  color: #e90;
}
.VirgilEditorTheme__tokenFunction {
  color: #dd4a68;
}
.VirgilEditorTheme__mark {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
  padding-bottom: 2px;
}
.VirgilEditorTheme__markOverlap {
  background: rgba(255, 212, 0, 0.3);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.VirgilEditorTheme__mark.selected {
  background: rgba(255, 212, 0, 0.5);
  border-bottom: 2px solid rgba(255, 212, 0, 1);
}
.VirgilEditorTheme__markOverlap.selected {
  background: rgba(255, 212, 0, 0.7);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.VirgilEditorTheme__embedBlock {
  user-select: none;
}
.VirgilEditorTheme__layoutContainer {
  display: grid;
  gap: 10px;
  margin: 10px 0;
}
.VirgilEditorTheme__layoutItem {
  border: 1px dashed #ddd;
  padding: 8px 16px;
  min-width: 0;
  max-width: 100%;
}
.VirgilEditorTheme__autocomplete {
  color: #ccc;
}
.VirgilEditorTheme__hr {
  padding: 2px 2px;
  border: none;
  margin: 1em 0;
  cursor: pointer;
}
.VirgilEditorTheme__hr:after {
  content: '';
  display: block;
  height: 2px;
  background-color: #ccc;
  line-height: 2px;
}
.VirgilEditorTheme__hr.VirgilEditorTheme__hrSelected {
  outline: 2px solid rgb(60, 132, 244);
  user-select: none;
}

.VirgilEditorTheme__specialText {
  background-color: yellow;
  font-weight: bold;
}

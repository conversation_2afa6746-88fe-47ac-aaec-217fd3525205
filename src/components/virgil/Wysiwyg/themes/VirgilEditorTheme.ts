/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type { EditorThemeClasses } from "lexical";

import "./VirgilEditorTheme.css";

const theme: EditorThemeClasses = {
  autocomplete: "VirgilEditorTheme__autocomplete",
  blockCursor: "VirgilEditorTheme__blockCursor",
  characterLimit: "VirgilEditorTheme__characterLimit",
  code: "VirgilEditorTheme__code",
  codeHighlight: {
    atrule: "VirgilEditorTheme__tokenAttr",
    attr: "VirgilEditorTheme__tokenAttr",
    boolean: "VirgilEditorTheme__tokenProperty",
    builtin: "VirgilEditorTheme__tokenSelector",
    cdata: "VirgilEditorTheme__tokenComment",
    char: "VirgilEditorTheme__tokenSelector",
    class: "VirgilE<PERSON>orTheme__tokenFunction",
    "class-name": "VirgilE<PERSON>orTheme__tokenFunction",
    comment: "VirgilEditorTheme__tokenComment",
    constant: "VirgilEditorTheme__tokenProperty",
    deleted: "VirgilEditorTheme__tokenProperty",
    doctype: "VirgilEditorTheme__tokenComment",
    entity: "VirgilEditorTheme__tokenOperator",
    function: "VirgilEditorTheme__tokenFunction",
    important: "VirgilEditorTheme__tokenVariable",
    inserted: "VirgilEditorTheme__tokenSelector",
    keyword: "VirgilEditorTheme__tokenAttr",
    namespace: "VirgilEditorTheme__tokenVariable",
    number: "VirgilEditorTheme__tokenProperty",
    operator: "VirgilEditorTheme__tokenOperator",
    prolog: "VirgilEditorTheme__tokenComment",
    property: "VirgilEditorTheme__tokenProperty",
    punctuation: "VirgilEditorTheme__tokenPunctuation",
    regex: "VirgilEditorTheme__tokenVariable",
    selector: "VirgilEditorTheme__tokenSelector",
    string: "VirgilEditorTheme__tokenSelector",
    symbol: "VirgilEditorTheme__tokenProperty",
    tag: "VirgilEditorTheme__tokenProperty",
    url: "VirgilEditorTheme__tokenOperator",
    variable: "VirgilEditorTheme__tokenVariable",
  },
  embedBlock: {
    base: "VirgilEditorTheme__embedBlock",
    focus: "VirgilEditorTheme__embedBlockFocus",
  },
  hashtag: "VirgilEditorTheme__hashtag",
  heading: {
    h1: "VirgilEditorTheme__h1",
    h2: "VirgilEditorTheme__h2",
    h3: "VirgilEditorTheme__h3",
    h4: "VirgilEditorTheme__h4",
    h5: "VirgilEditorTheme__h5",
    h6: "VirgilEditorTheme__h6",
  },
  hr: "VirgilEditorTheme__hr",
  hrSelected: "VirgilEditorTheme__hrSelected",
  image: "editor-image",
  indent: "VirgilEditorTheme__indent",
  inlineImage: "inline-editor-image",
  layoutContainer: "VirgilEditorTheme__layoutContainer",
  layoutItem: "VirgilEditorTheme__layoutItem",
  link: "VirgilEditorTheme__link",
  list: {
    checklist: "VirgilEditorTheme__checklist",
    listitem: "VirgilEditorTheme__listItem",
    listitemChecked: "VirgilEditorTheme__listItemChecked",
    listitemUnchecked: "VirgilEditorTheme__listItemUnchecked",
    nested: {
      listitem: "VirgilEditorTheme__nestedListItem",
    },
    olDepth: [
      "VirgilEditorTheme__ol1",
      "VirgilEditorTheme__ol2",
      "VirgilEditorTheme__ol3",
      "VirgilEditorTheme__ol4",
      "VirgilEditorTheme__ol5",
    ],
    ul: "VirgilEditorTheme__ul",
  },
  ltr: "VirgilEditorTheme__ltr",
  mark: "VirgilEditorTheme__mark",
  markOverlap: "VirgilEditorTheme__markOverlap",
  paragraph: "VirgilEditorTheme__paragraph",
  quote: "VirgilEditorTheme__quote",
  rtl: "VirgilEditorTheme__rtl",
  specialText: "VirgilEditorTheme__specialText",
  tab: "VirgilEditorTheme__tabNode",
  table: "VirgilEditorTheme__table",
  tableAddColumns: "VirgilEditorTheme__tableAddColumns",
  tableAddRows: "VirgilEditorTheme__tableAddRows",
  tableAlignment: {
    center: "VirgilEditorTheme__tableAlignmentCenter",
    right: "VirgilEditorTheme__tableAlignmentRight",
  },
  tableCell: "VirgilEditorTheme__tableCell",
  tableCellActionButton: "VirgilEditorTheme__tableCellActionButton",
  tableCellActionButtonContainer:
    "VirgilEditorTheme__tableCellActionButtonContainer",
  tableCellHeader: "VirgilEditorTheme__tableCellHeader",
  tableCellResizer: "VirgilEditorTheme__tableCellResizer",
  tableCellSelected: "VirgilEditorTheme__tableCellSelected",
  tableFrozenColumn: "VirgilEditorTheme__tableFrozenColumn",
  tableFrozenRow: "VirgilEditorTheme__tableFrozenRow",
  tableRowStriping: "VirgilEditorTheme__tableRowStriping",
  tableScrollableWrapper: "VirgilEditorTheme__tableScrollableWrapper",
  tableSelected: "VirgilEditorTheme__tableSelected",
  tableSelection: "VirgilEditorTheme__tableSelection",
  text: {
    bold: "VirgilEditorTheme__textBold",
    capitalize: "VirgilEditorTheme__textCapitalize",
    code: "VirgilEditorTheme__textCode",
    highlight: "VirgilEditorTheme__textHighlight",
    italic: "VirgilEditorTheme__textItalic",
    lowercase: "VirgilEditorTheme__textLowercase",
    strikethrough: "VirgilEditorTheme__textStrikethrough",
    subscript: "VirgilEditorTheme__textSubscript",
    superscript: "VirgilEditorTheme__textSuperscript",
    underline: "VirgilEditorTheme__textUnderline",
    underlineStrikethrough: "VirgilEditorTheme__textUnderlineStrikethrough",
    uppercase: "VirgilEditorTheme__textUppercase",
  },
};

export default theme;

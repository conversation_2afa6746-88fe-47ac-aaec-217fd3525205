import "./styles.css";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import ToolbarPlugin from "./plugins/ToolbarPlugin/index";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { TRANSFORMERS } from "@lexical/markdown";
import ListMaxIndentLevelPlugin from "./plugins/ListMaxIndentLevelPlugin";
import AutoLinkPlugin from "./plugins/AutoLinkPlugin";
import { EditorState, LexicalEditor } from "lexical";
import { LexicalErrorBoundaryProps } from "@lexical/react/LexicalErrorBoundary";
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";
import { EditorRefPlugin } from "@lexical/react/LexicalEditorRefPlugin";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useState } from "react";

function Placeholder() {
  return <div className="editor-placeholder">Enter text here...</div>;
}

const ErrorBoundary: React.FC<LexicalErrorBoundaryProps> = ({ children }: { children: React.ReactNode }) => {
  return <>Error loading editor</>;
};

export type EditorProps = {
  editorStateRef: React.RefObject<LexicalEditor | null>;
  editorConentClass?: string;
  onChange?: (editorState: EditorState, editor: LexicalEditor) => void;
}

export default function Editor({ editorStateRef, editorConentClass, onChange }: EditorProps) {
  const [editor] = useLexicalComposerContext();
  const [activeEditor, setActiveEditor] = useState(editor); // TODO: remove this
  const [isLinkEditMode, setIsLinkEditMode] = useState(false); // TODO: remove this
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);
  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };

  return (
    <div className="wysiwyg-virgil editor-container">
      <ToolbarPlugin
        editor={editor}
        activeEditor={activeEditor}
        setActiveEditor={setActiveEditor}
        setIsLinkEditMode={setIsLinkEditMode}
      />
      <div className="editor-inner">
        <EditorRefPlugin editorRef={editorStateRef} />
        <RichTextPlugin
          contentEditable={
            <div className="editor-scroller">
              <div className={`editor ${editorConentClass ? editorConentClass : ''}`} ref={onRef}>
                <ContentEditable />
              </div>
            </div>
          }
          ErrorBoundary={ErrorBoundary}
        />
        <HistoryPlugin />
        <AutoFocusPlugin />
        <ListPlugin />
        <LinkPlugin />
        <AutoLinkPlugin />
        <ListMaxIndentLevelPlugin maxDepth={7} />
        <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
        <TabIndentationPlugin />
        {onChange && <OnChangePlugin onChange={onChange} />}
        <TablePlugin />
        {/* <TableCellActionMenuPlugin
          anchorElem={floatingAnchorElem}
          cellMerge={true}
        /> */}
        {/* @ts-ignore */}
        {/* <TableHoverActionsPlugin anchorElem={floatingAnchorElem} /> */}
      </div>
    </div>
  );
}

import React, { useCallback, useState } from "react";
import { AnswerGenerationType, ResponseStatus } from "@prisma/client";
import { cn } from "~/v2/lib/utils";
import { Typography } from "~/v2/components/ui/Typography";
import { SourceResponseLabel } from "../OfficeAddIn/IndexView/SingleDDQWithData/SourceResponseLabel";
import { toast } from "~/components/snackbar";
import { ReasonTooltip } from "../OfficeAddIn/ReasonTooltip";
import { Markdown } from "../OfficeAddIn/Markdown";
import { NoResponseLabel } from "../Labels/NoResponseLabel";
import { StatusResponseLabel } from "../OfficeAddIn/IndexView/SingleDDQWithData/StatusResponseLabel";
import { InsufficientDataLabel } from "~/components/virgil/Labels/InsufficientDataLabel";
import { AnsweredLabel } from "~/components/virgil/Labels/AnsweredLabel";
import { isResponseValid } from "../OfficeAddIn/isResponseValid";

type QuestionDetailsProps = {
    responseReason: string;
    responseText?: string | null;
    insufficientData: boolean;
    isAnswered: boolean;
    isGenerating: boolean;
    responseStatus?: ResponseStatus;
    answerGenerationType?: AnswerGenerationType;
    maxHeight?: number;
    displayAnsweredStatus?: boolean;
}

export const ResponseDetails = ({
    responseReason,
    responseStatus,
    responseText,
    answerGenerationType,
    insufficientData,
    isAnswered,
    isGenerating,
    maxHeight = 300,
    displayAnsweredStatus = false,
}: QuestionDetailsProps) => {
    const [isMarkdownHovered, setIsMarkdownHovered] = useState(false);

    const onMouseEnter = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        setIsMarkdownHovered(true);
    }, [setIsMarkdownHovered]);
    const onMouseLeave = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        setIsMarkdownHovered(false);
    }, [setIsMarkdownHovered]);
    const onMarkdownClick = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        if (responseText) {
            navigator.clipboard.writeText(responseText);
        }
        toast.success("Copied to clipboard", {
            position: "bottom-right",
            duration: 1000,
        });
    }, [responseText]);
    return <div className="space-y-[8px]">
        <div className="flex items-center space-x-2">
            {isAnswered && displayAnsweredStatus && (
                <AnsweredLabel />
            )}
            {insufficientData && (
                <InsufficientDataLabel />
            )}
            {!isResponseValid(responseText) && (
                <NoResponseLabel />
            )}
            {isResponseValid(responseText) && !isAnswered && (
                <>
                    <ReasonTooltip reasonText={responseReason}>
                        <SourceResponseLabel answerGenerationType={answerGenerationType} />
                    </ReasonTooltip>
                    <StatusResponseLabel status={responseStatus} />
                    <Typography
                        variant="caption"
                        onClick={onMarkdownClick}
                        className={cn(
                            "cursor-pointer",
                            isMarkdownHovered ? "opacity-100" : "opacity-0",
                            "transition-opacity duration-500 ease-in-out",
                            "hover:opacity-100 hover:underline",
                            "underline"
                        )}
                    >
                        Click to copy
                    </Typography>
                </>
            )}

        </div>
        <div className="overflow-y-auto" style={{ maxHeight: maxHeight }}>
            <Markdown
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                onClick={onMarkdownClick}
                markdown={responseText ?? ""}
                className={`add-in${isGenerating ? " fade" : ""}`}
            />
        </div>
    </div>;
};
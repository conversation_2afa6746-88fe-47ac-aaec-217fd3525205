import { Badge } from "~/v2/components/ui/Badge";
import { ResponseStatus } from "@prisma/client";

const ResponseStatusLabel = ({ responseStatus }: { responseStatus: ResponseStatus }) => {
    return (
        <Badge
            variant={
                responseStatus === ResponseStatus.APPROVED
                    ? "default"
                    : responseStatus === ResponseStatus.REJECTED
                        ? "destructive"
                        : responseStatus === ResponseStatus.PENDING_APPROVAL
                            ? "secondary"
                            : "outline"
            }
        >
            {responseStatus.toUpperCase().replace("_", " ")}
        </Badge>
    );
};

export default ResponseStatusLabel;
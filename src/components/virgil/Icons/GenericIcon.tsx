import { LucideIcon } from "lucide-react"
import { cn } from "~/v2/lib/utils"

interface GenericIconProps {
    className?: string
    icon: LucideIcon
    size?: number
}

export const GenericIcon = ({
    className,
    icon: Icon,
    size = 16
}: GenericIconProps) => {
    return (
        <span className={cn(
            "p-0.5 rounded-[var(--border-radius-icon)]",
            "flex",
            className
        )}>
            <Icon
                width={size}
                height={size}
            />
        </span>
    )
}

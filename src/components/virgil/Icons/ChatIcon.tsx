import React from "react";
import { CONFIG } from 'src/config-global';
import { GenericIcon } from "./GenericIcon";
import { SvgColor } from "~/components/svg-color";

export const ChatIcon = ({
    width = 16,
    color = 'primary.main',
}: {
    color?: string
    width?: number
}) => {
    return <SvgColor
        src={`${CONFIG.site.basePath}/assets/icons/navbar/response-icon.svg`}
        sx={{
            width: width,
            color: color,
        }}
    />
}

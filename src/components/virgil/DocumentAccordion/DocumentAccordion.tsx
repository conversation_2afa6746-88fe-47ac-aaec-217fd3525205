import React, { useState } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  IconButton,
  Link,
  Button,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import DescriptionIcon from "@mui/icons-material/Description";
import { FileThumbnail } from "~/components/file-thumbnail";
import { useBaseUrl } from "../hooks/useBaseUrl";
import { useSelector } from "react-redux";
import { selectAddInMode } from "~/lib/features/addInSelectors";
import { openDocumentPreview } from "../OfficeAddIn/addInUtils/common";

interface DocumentAccordionProps {
  documentId: string;
  fileName: string;
  content: string;
  sourceLink: string;
  pageNumber: number;
}

const DocumentAccordion: React.FC<DocumentAccordionProps> = ({
  documentId,
  fileName,
  content,
  sourceLink,
  pageNumber,
}) => {
  const orgDomain = useBaseUrl();
  const mode = useSelector(selectAddInMode);

  return (
    <Accordion
      sx={{
        boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
        borderRadius: "8px",
        marginBottom: 2,
        "&:before": {
          display: "none",
        },
      }}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <FileThumbnail
          file={fileName}
          sx={{ width: 20, height: 20, marginRight: 1 }}
          slotProps={{
            icon: {
              color: "red",
            },
          }}
        />
        <Typography
          fontSize={13}
          fontWeight="bold"
          sx={{
            wordBreak: "break-word",
            overflowWrap: "break-word",
          }}
        >
          {fileName}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Typography variant="body2" sx={{ width: "100%" }}>
          {content}
        </Typography>
        <div
          style={{ width: "100%", display: "flex", justifyContent: "flex-end" }}
        >
          {pageNumber > 0 && (
            <Typography
              variant="caption"
              sx={{ marginTop: 1, marginRight: "auto" }}
            >
              Page {pageNumber}
            </Typography>
          )}
          <Button
            size="small"
            sx={{ marginTop: 1, marginLeft: "auto", color: "primary.main" }}
            variant="text"
            onClick={() => {
              openDocumentPreview({
                domain: orgDomain,
                documentId,
                previewHeight: 80,
                previewWidth: 50,
                mode,
              });
            }}
          >
            View Source
          </Button>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default DocumentAccordion;

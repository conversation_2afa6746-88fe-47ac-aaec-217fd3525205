import { useMemo, useState } from "react";
import { QuestionStatusType } from "@prisma/client";
import { useUpdateQuestionStatusBatch } from "./useUpdateQuestionStatusBatch";
import { ActionButton } from "~/v2/components/composit/ActionButton";
import { toast } from "~/components/snackbar";
import { useDDQSelect } from "./useDDQSelect";
// import { useInsertAll } from "./useInsertAll";

export const useSelectAction = () => {
  const [loading, setLoading] = useState(false);
  const { selectedIds, setSelectedIds } = useDDQSelect();

  // const handleInsertAll = useInsertAll({
  //   selectedIds: Array.from(selectedIds), onSuccess: (count: number) => {
  //     toast.success(`${count} responses inserted`);
  //   }, onError: (errorCount: number) => {
  //     toast.error(`Error inserting ${errorCount} responses`);
  //   }
  // });
  const batchUpdateQuestionStatus = useUpdateQuestionStatusBatch({
    onMutate: () => {
      setLoading(true);
    },
    onSuccess: (count: number) => {
      toast.success(`${JSON.stringify(count)} questions updated`);
      setLoading(false);
      setSelectedIds(new Set());
    },
    onError: (errorCount: number) => {
      toast.error(`Error updating ${errorCount} questions`);
      setLoading(false);
    }
  });

  const SplitButtonMemo = useMemo(() => {
    return <ActionButton
      label="Bulk Actions"
      menuClassName="z-1000"
      disabled={loading}
      options={[
        // {
        //   label: `Insert (${selectedIds.size})`, onClick: async (e: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
        //     e.preventDefault();
        //     const response = await handleInsertAll(e);

        //     await batchUpdateQuestionStatus.mutateAsync({
        //       ids: response?.insertedQuestionIds || [],
        //       status: QuestionStatusType.ANSWERED
        //     })
        //   }
        // },
        {
          label: `Mark as answered (${selectedIds.size})`, action: async () => {
            await batchUpdateQuestionStatus.mutateAsync({
              ids: Array.from(selectedIds),
              status: QuestionStatusType.ANSWERED
            })
          }
        },
        {
          label: `Mark as new (${selectedIds.size})`, action: async () => {
            await batchUpdateQuestionStatus.mutateAsync({
              ids: Array.from(selectedIds),
              status: QuestionStatusType.NEW
            })
          }
        }
      ]}
    />
  }, [loading, selectedIds]);

  return {
    SplitButtonMemo
  };
};

import { useSelector } from "react-redux";
import { api } from "~/trpc/react";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { selectSelectedTagIds, selectSelectedFundIds } from "~/lib/features/documentDDQSelector";

export const useRagResponse = (questionText: string, enabled: boolean) => {
    const documentId = useSelector(selectDocumentId);
    const selectedTagIds = useSelector(selectSelectedTagIds);
    const selectedFundIds = useSelector(selectSelectedFundIds);

    const { data, isLoading, isSuccess, refetch } = api.rag.generateRAGResponseQuery.useQuery({
        ragPrompt: questionText,
        documentId: documentId,
        tagIds: selectedTagIds,
        fundIds: selectedFundIds,
    }, {
        enabled: !!documentId && !!(questionText?.trim()) && enabled,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60,
    });

    const responseText = data?.response?.answer ?? "";

    return { data, isLoading, isSuccess, responseText, refetch, citations: data?.response?.citations ?? [] };
};

import { QuestionStatusType, QuestionCategory } from "@prisma/client";
import { useQueryClient } from "@tanstack/react-query";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { useSelector } from "react-redux";

import { api } from "~/trpc/react";

const useUpdateQuestionStatusBatch = ({
  onSuccess,
  onError,
  onMutate,
}: {
  onSuccess: (data: any) => void;
  onError: (error: any) => void;
  onMutate: () => void;
}) => {
  const queryClient = useQueryClient();
  const documentId = useSelector(selectDocumentId);
  const batchUpdateQuestionStatus = api.question.batchUpdateStatus.useMutation({
    onMutate: async (data) => {
      onMutate();
      // Get all queries that match the base key
      const infiniteQueries = queryClient.getQueryCache().findAll({
        queryKey: getQueryKey(
          api.question.getAllQuestionsInfinite,
          undefined,
          "any",
        ),
      });
      const questionCountQueries = queryClient.getQueryCache().findAll({
        queryKey: getQueryKey(
          api.question.getQuestionCountForDocument,
          undefined,
          "any",
        ),
      });

      await Promise.all(
        [...infiniteQueries, ...questionCountQueries].map((query) =>
          query.cancel(),
        ),
      );

      const previousQueries = [] as { queryKey: any; state: any }[];

      // Update cache for all matching queries
      infiniteQueries.forEach((query) => {
        const previousData = query.state.data as { pages: { items: any[] }[] };
        previousQueries.push({
          queryKey: query.queryKey,
          state: queryClient.getQueryData(query.queryKey),
        });
        if (previousData) {
          const newData = {
            ...previousData,
            pages: previousData.pages.map((page) => {
              return {
                ...page,
                items: page.items.map((item) => {
                  if (data.ids.includes(item.id)) {
                    return {
                      ...item,
                      status: data.status,
                    };
                  }
                  return item;
                }),
              };
            }),
          };
          queryClient.setQueryData(query.queryKey, newData);
        }
      });

      questionCountQueries.forEach((questionCountQuery) => {
        const previousCount = queryClient.getQueryData(
          questionCountQuery.queryKey,
        ) as
          | {
              newQuestions: number;
              answeredQuestions: number;
            }
          | undefined;

        if (!previousCount) {
          return;
        }

        previousQueries.push({
          queryKey: questionCountQuery.queryKey,
          state: queryClient.getQueryData(questionCountQuery.queryKey),
        });

        queryClient.setQueryData(questionCountQuery.queryKey, {
          newQuestions:
            data.status === QuestionStatusType.NEW
              ? previousCount.newQuestions + data.ids.length
              : previousCount.newQuestions - data.ids.length,
          answeredQuestions:
            data.status === QuestionStatusType.ANSWERED
              ? previousCount.answeredQuestions + data.ids.length
              : previousCount.answeredQuestions - data.ids.length,
        });
      });

      return { previousQueries };
    },
    onSuccess: (data) => {
      onSuccess(data.count);
    },
    onError: (error) => {
      onError(error);
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: getQueryKey(
          api.question.getAllQuestionsInfinite,
          undefined,
          "any",
        ),
      });
      queryClient.invalidateQueries({
        queryKey: getQueryKey(
          api.question.getQuestionCountForDocument,
          { documentId },
          "query",
        ),
      });
    },
    meta: {
      skipInvalidateQueries: true,
    },
  });

  return batchUpdateQuestionStatus;
};

export { useUpdateQuestionStatusBatch };

import { QuestionStatusType } from "@prisma/client";
import { useQueryClient } from "@tanstack/react-query";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { useCallback } from "react";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { UseMutationResult } from "@tanstack/react-query";

import { api } from "~/trpc/react";

const useUpdateQuestionStatus = ({
  onSuccess,
  onError,
}: {
  onSuccess: (data: any) => void;
  onError: (error: any) => void;
}) => {
  const documentId = useSelector(selectDocumentId);
  const queryClient = useQueryClient();

  const updateQuestionStatusMutation = api.question.updateStatus.useMutation({
    onMutate: async (data: {
      id: string;
      oldStatus: QuestionStatusType;
      newStatus: QuestionStatusType;
    }) => {
      if (data.oldStatus === data.newStatus) {
        return;
      }

      // Get all queries that match the base key
      const infiniteQueries = queryClient.getQueryCache().findAll({
        queryKey: getQueryKey(
          api.question.getAllQuestionsInfinite,
          undefined,
          "any",
        ),
      });

      const questionCountQueries = queryClient.getQueryCache().findAll({
        queryKey: getQueryKey(
          api.question.getQuestionCountForDocument,
          undefined,
          "any",
        ),
      });

      // Cancel any ongoing queries
      await Promise.all(
        [...infiniteQueries, ...questionCountQueries].map((query) =>
          query.cancel(),
        ),
      );

      const previousQueries = [] as { queryKey: any; state: any }[];

      // Update cache for all matching queries
      infiniteQueries.forEach((query) => {
        const previousData = query.state.data as { pages: { items: any[] }[] };
        previousQueries.push({
          queryKey: query.queryKey,
          state: queryClient.getQueryData(query.queryKey),
        });
        if (previousData) {
          const newData = {
            ...previousData,
            pages: previousData.pages.map((page) => {
              return {
                ...page,
                items: page.items.map((item) => {
                  if (item.id === data.id) {
                    return {
                      ...item,
                      status: data.newStatus,
                    };
                  }
                  return item;
                }),
              };
            }),
          };
          queryClient.setQueryData(query.queryKey, newData);
        }
      });

      questionCountQueries.forEach((questionCountQuery) => {
        const previousCount = queryClient.getQueryData(
          questionCountQuery.queryKey,
        ) as
          | {
              newQuestions: number;
              answeredQuestions: number;
            }
          | undefined;

        if (!previousCount) {
          return;
        }

        previousQueries.push({
          queryKey: questionCountQuery.queryKey,
          state: queryClient.getQueryData(questionCountQuery.queryKey),
        });

        queryClient.setQueryData(questionCountQuery.queryKey, {
          newQuestions:
            data.oldStatus === QuestionStatusType.NEW
              ? previousCount.newQuestions - 1
              : previousCount.newQuestions + 1,
          answeredQuestions:
            data.oldStatus === QuestionStatusType.ANSWERED
              ? previousCount.answeredQuestions - 1
              : previousCount.answeredQuestions + 1,
        });
      });

      return { previousQueries };
    },
    onSuccess: () => {
      onSuccess(undefined);
    },
    onError: (error, variables, context) => {
      // Restore all previous queries
      context?.previousQueries?.forEach((query) => {
        if (query) {
          queryClient.setQueryData(query.queryKey, query.state.data);
        }
      });
      onError(error);
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: getQueryKey(
          api.question.getAllQuestionsInfinite,
          undefined,
          "any",
        ),
      });
      queryClient.invalidateQueries({
        queryKey: getQueryKey(
          api.question.getQuestionCountForDocument,
          { documentId },
          "query",
        ),
      });
    },
    meta: {
      skipInvalidateQueries: true,
    },
  }) as unknown as UseMutationResult<
    void,
    Error,
    {
      id: string;
      newStatus: QuestionStatusType;
      oldStatus: QuestionStatusType;
    },
    unknown
  >;

  return updateQuestionStatusMutation;
};

export { useUpdateQuestionStatus };

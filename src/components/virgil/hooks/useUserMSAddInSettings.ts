import { useAuth } from "@clerk/nextjs";
import { api } from "~/trpc/react";

/**
 * A custom hook that fetches user data using Clerk authentication.
 *
 * @returns An object containing:
 * - isLoading: Boolean indicating if the user data is still being fetched
 * - data: User data including organizations they belong to, or undefined if not loaded
 */
export type UserMSAddInSettings = {
  insertTextColor: string;
  chatHeight: number;
  chatWidth: number;
  fontSize: number;
  bold: boolean;
  italic: boolean;
  underline: boolean;
  sameAsDocument: boolean;
};

export const useUserMSAddInSettings = () => {
  const { isLoaded, userId } = useAuth();
  const { data, isLoading } = api.user.getByClerkId.useQuery(
    { clerkId: userId },
    {
      enabled: !!userId,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 1000 * 60 * 60 * 24, // 24 hours
    },
  );

  const userSettings = data?.msAddInSettings ?? {
    insertTextColor: "#008000",
    chatHeight: 50,
    chatWidth: 50,
    fontSize: 12,
    bold: false,
    italic: false,
    underline: false,
    sameAsDocument: false,
  };

  return {
    isLoading: !isLoaded || isLoading,
    data: userSettings,
  };
};

import { LexicalEditor } from "lexical";
import { useCallback, useRef, useState } from "react";
import { api } from "~/trpc/react";
import { PLAYGROUND_TRANSFORMERS } from "../Wysiwyg/transformers/playgroundTransformers";
import { $convertToMarkdownString } from "@lexical/markdown";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getQueryKey } from "@trpc/react-query";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { toast } from "~/components/snackbar";

export const useCreateOrUpdateResponse = ({
  questionId,
  questionText,
  onSuccess,
}: {
  questionId: string;
  questionText: string;
  onSuccess: () => void;
}) => {
  const editorStateRef = useRef<LexicalEditor | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const documentId = useSelector(selectDocumentId);
  //   const utils = api.useUtils();
  const createResponse = api.response.createResponse.useMutation();
  const updateResponseById = api.response.updateResponseById.useMutation();
  // TODO: Optimistic update, for snappy UI
  //   const updateResponseById = api.response.updateResponseById.useMutation({
  //     onMutate: async (updatedResponse) => {
  //       setSubmitting(true);
  //       await utils.response.getAllResponses.cancel();
  //       await utils.question.similaritySearch.cancel();
  //       const previosData = utils.question.similaritySearch.getData({
  //         query: questionText,
  //       });

  //       // clone previosData and update the response text
  //       const previosDataClone = structuredClone(previosData);
  //       const similarResponseData = previosDataClone?.find(
  //         (answer) => answer.id === questionId,
  //       )?.response?.responseContents?.[0];
  //       (similarResponseData?.content as { text: string })!.text =
  //         updatedResponse.responseText;

  //       utils.question.similaritySearch.setData(
  //         { query: questionText },
  //         {
  //           ...(previosDataClone ?? []),
  //         },
  //       );
  //       onSuccess();

  //       return {
  //         previosData,
  //       };
  //     },
  //     onError: (error, _, context) => {
  //       utils.question.similaritySearch.setData(
  //         { query: questionText },
  //         context?.previosData,
  //       );
  //     },
  //     onSuccess: () => {
  //       setSubmitting(false);
  //     },
  //     onSettled: () => {
  //       void utils.question.similaritySearch.invalidate({ query: questionText });
  //     },
  //   });

  const createOrUpdateResponse = useCallback(
    (similarResponseId: string | null) => {
      if (submitting) return;
      editorStateRef.current?.getEditorState().read(() => {
        const markdown = $convertToMarkdownString(PLAYGROUND_TRANSFORMERS);
        setSubmitting(true);
        if (similarResponseId) {
          updateResponseById.mutate(
            {
              id: similarResponseId,
              responseText: markdown,
            },
            {
              onSuccess: () => {
                onSuccess();
                setSubmitting(false);
                toast.success("Response updated successfully");
              },
              onError: (error: any) => {
                console.error("Error updating response:", error);
                setSubmitting(false);
                toast.error("Error updating response");
              },
            },
          );
        } else {
          createResponse.mutate(
            {
              documentId: documentId,
              content: markdown,
              questionId,
            },
            {
              onSuccess: () => {
                console.log("Response created successfully");
                onSuccess();
                setSubmitting(false);
                toast.success("Response created successfully");
              },
              onError: (error: any) => {
                console.error("Error creating response:", error);
                setSubmitting(false);
                toast.error("Error creating response");
              },
            },
          );
        }
      });
    },
    [],
  );

  return {
    submitting,
    editorStateRef,
    createOrUpdateResponse,
  };
};

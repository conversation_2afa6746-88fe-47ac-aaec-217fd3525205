import * as React from 'react';
import { api } from '~/trpc/react';
import SplitButtonWithPrompt from '../ButtonWithPrompt/ButtonWithPrompt';
import { toast } from '~/components/snackbar';
import { getQueryKey } from '@trpc/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { RefreshCcw } from 'lucide-react';

interface RegenerateBtnProps {
    questionId: string;
    documentId: string;
}

export default function useRegenerateBtn({
    questionId,
    documentId,
}: RegenerateBtnProps) {
    const queryClient = useQueryClient();
    const mutation = api.rag.generateDDQResponseWithUserPrompt.useMutation({
        onSuccess: (data) => {
            const infiniteQueries = queryClient.getQueryCache().findAll({
                queryKey: getQueryKey(
                    api.question.getAllQuestionsInfinite,
                    undefined,
                    "any",
                ),
            });

            infiniteQueries.forEach((query) => {
                const previousData = queryClient.getQueryData(query.queryKey) as { pages: { items: any[] }[] } | undefined;
                if (!previousData) {
                    console.log("No previous data found for query");
                    return;
                }

                const newData = {
                    ...previousData,
                    pages: previousData.pages.map((page) => {
                        return {
                            ...page,
                            items: page.items.map((item) => {
                                if (item.id === questionId) {
                                    return data;
                                }
                                return item;
                            }),
                        };
                    }),
                };

                console.log("Setting new data for query");
                queryClient.setQueryData(query.queryKey, newData);

                // Verify the update
                const updatedData = queryClient.getQueryData(query.queryKey) as { pages: { items: any[] }[] } | undefined;
                console.log("Verified updated data for question:", questionId, updatedData?.pages.map(page =>
                    page.items.find(item => item.id === questionId)
                ));
            });

            const singleQuestionQuery = queryClient.getQueryCache().findAll({
                queryKey: getQueryKey(
                    api.question.getQuestionWithFeedback,
                    { id: questionId },
                    "any",
                ),
            });
            if (singleQuestionQuery.length === 0) {
                console.log("No single question query found for question:", questionId);
            }
            singleQuestionQuery.forEach((query) => {
                queryClient.setQueryData(query.queryKey, data);
            });
        },
        onError: (error) => {
            toast.error(`Error regenerating: ${JSON.stringify(error)}`)
        },
        meta: {
            skipInvalidateQueries: true,
        }
    })

    const regenerateBtn = <SplitButtonWithPrompt
        variant="outline"
        disabled={mutation.isPending}
        pendingText="Generating..."
        popupTitle="How would you like to regenerate this response?"
        buttonLabel="Regenerate"
        buttonIcon={<RefreshCcw />}
        inputConfig={{
            placeholder: "Tell Virgil how to improve.",
        }}
        mutationConfig={{
            mutation,
            onSubmit: (value: string) => {
                if (!value?.length) {
                    return;
                }

                mutation.mutate({
                    question: {
                        questionId,
                        customPrompt: value
                    },
                    documentId,
                })
            },
            onCancel: () => {
                mutation.reset()
            },
            onTryAgain: (value: string) => {
                mutation.reset()
                mutation.mutate({
                    question: {
                        questionId,
                        customPrompt: value
                    },
                    documentId,
                })
            }
        }}
    />

    return {
        regenerateBtn,
        isPending: mutation.isPending,
    }
}
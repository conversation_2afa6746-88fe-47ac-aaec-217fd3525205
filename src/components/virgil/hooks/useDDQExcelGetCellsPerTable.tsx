import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { api } from "~/trpc/react";

export const useDDQExcelGetCellsPerTable = (tableId: string) => {

  const documentId = useSelector(selectDocumentId);

  const { data, isLoading, isFetching } = api.sheet.getAllCellsForTable.useQuery({
    documentId: documentId ?? "",
    tableId: tableId ?? "",
  }, {
    enabled: !!documentId && !!tableId,
  });

  return {
    cellGroups: data ?? [],
    isLoading: isLoading || isFetching,
  };
};

import { useMemo, useState } from "react";
import { ActionButton } from "~/v2/components/composit/ActionButton";
import { useDDQTableSelect } from "./useDDQTableSelect";
import { api } from "~/trpc/react";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { insertCellRange } from "../OfficeAddIn/addInUtils/excel";

export const useSelectTableAction = () => {
  const [loading, setLoading] = useState(false);
  const { selectedIds, setSelectedIds } = useDDQTableSelect();
  const documentId = useSelector(selectDocumentId);

  const { mutateAsync: getCellsForTables } = api.sheet.getCellsForTables.useMutation({
    meta: {
      skipInvalidateQueries: true,
    }
  });

  const handlePopulate = async () => {
    try {
      setLoading(true);
      const cells = await getCellsForTables({
        tableIds: Array.from(selectedIds),
        documentId: documentId,
      });
      await insertCellRange({ cells });
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const SplitButtonMemo = useMemo(() => {
    return <ActionButton
      label="Bulk Actions"
      menuClassName="z-1000"
      disabled={loading}
      options={[
        {
          label: `Populate (${selectedIds.size})`, action: async () => {
            await handlePopulate();
          }
        },
      ]}
    />
  }, [loading, selectedIds]);

  return {
    SplitButtonMemo
  };
};

import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { api } from "~/trpc/react";
import { setDocumentMetadata } from "~/lib/features/documentMetadataSlice";
import {
  setAddInNavigationPath,
  setDocumentId,
} from "~/lib/features/addInSlice";
import {
  selectDocumentId,
  selectDocumentUrl,
} from "~/lib/features/addInSelectors";
import { DocumentStatus as DocumentStatusEnum } from "@prisma/client";

export const useDocumentInfo = () => {
  const dispatch = useDispatch();
  const documentId = useSelector(selectDocumentId);
  const documentUrl = useSelector(selectDocumentUrl);

  const {
    data: documentInfoByUrl,
    isSuccess: isSuccessByUrl,
    isError: isErrorByUrl,
  } = api.document.getDocumentByUrl.useQuery(
    { url: documentUrl ?? "" },
    {
      enabled: !!documentUrl && !documentId,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
    },
  );

  useEffect(() => {
    if (documentInfoByUrl && isSuccessByUrl) {
      dispatch(setDocumentId(documentInfoByUrl.id));
      dispatch(
        setDocumentMetadata({
          createdByName: documentInfoByUrl.createdBy.name ?? "",
          summary: documentInfoByUrl.summary ?? "",
          createdAt: documentInfoByUrl.createdAt.toLocaleDateString(),
          dueDate: documentInfoByUrl.dueDate?.toISOString() ?? "",
          title: documentInfoByUrl.title ?? "",
          documentStatus: documentInfoByUrl.status,
        }),
      );
      if (
        documentInfoByUrl.status !== DocumentStatusEnum.READY &&
        documentInfoByUrl.status !== DocumentStatusEnum.GENERATING_ANSWERS
      ) {
        dispatch(setAddInNavigationPath("document-status"));
      } else {
        dispatch(setAddInNavigationPath("ddq"));
      }
    } else {
      dispatch(setAddInNavigationPath("document-status"));
    }
  }, [documentInfoByUrl, isSuccessByUrl, isErrorByUrl]);
};

import { useSelector } from "react-redux";
import { selectActiveSheetName } from "~/lib/features/addInSelectors";
import { DDQTable } from "~/lib/features/documentDDQSlice";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { api } from "~/trpc/react";

export const useDDQExcelTablesPerSheet = () => {

  const documentId = useSelector(selectDocumentId);
  const activeSheetName = useSelector(selectActiveSheetName);

  const { data, isLoading, isFetching } = api.sheet.getAllTablesForSheet.useQuery({
    documentId: documentId ?? "",
    sheetName: activeSheetName ?? "",
  }, {
    enabled: !!documentId && !!activeSheetName,
  });

  const tables: DDQTable[] = data ?? [];

  return {
    tables,
    isLoading: isLoading || isFetching,
  };
};

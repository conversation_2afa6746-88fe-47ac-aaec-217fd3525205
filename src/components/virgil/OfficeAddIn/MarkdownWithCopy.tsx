import { Tooltip } from "@mui/material"
import { useCallback, forwardRef } from "react";
import { toast } from "~/components/snackbar";
import { copyToClipboard } from "~/lib/copyToClipboard";
import { Markdown } from "./Markdown";

interface MarkdownProps {
    markdown: string;
    className?: string;
    onClick?: ((e: React.MouseEvent<HTMLElement>) => void) | ((e: React.MouseEvent<HTMLElement>) => Promise<void>)
}

export const MarkdownWithCopy = forwardRef<HTMLDivElement, MarkdownProps>((props, ref) => {
    const { markdown, className, onClick } = props;
    const onClickHandler = useCallback(async (e: React.MouseEvent<HTMLElement>) => {
        if (onClick) {
            await onClick(e);
        }
        e.preventDefault();
        e.stopPropagation();
        if (await copyToClipboard(markdown)) {
            toast.success("Copied to clipboard", {
                duration: 500,
            });
        }
    }, [onClick, markdown])

    return (
        <Tooltip title="Click to copy" placement="top">
            <Markdown ref={ref} markdown={markdown} className={className} onClick={onClickHandler} />
        </Tooltip>
    )
})

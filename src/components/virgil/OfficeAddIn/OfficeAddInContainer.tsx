import React, { PropsWithChildren } from "react";
import { useOfficeNavigation } from "../hooks/useOfficeMenu";
import DocumentStatus from "./DocumentStatus";
import LaunchChat from "./LaunchChat";

const OfficeAddInContainer: React.FC<PropsWithChildren> = ({ children }) => {
    // fake navigation b/c MS add in doesn't support window.history.pushState which is used in next.js
    const { Menu, menuToggleButton, backButton } = useOfficeNavigation();
    return (
        <div className="relative h-screen w-full overflow-hidden">
            <div style={{
                position: 'sticky',
                top: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexDirection: 'row',
                backgroundColor: '#fff',
                padding: '10px',
                paddingRight: '50px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                zIndex: 1000,
                height: '40px'
            }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    {backButton ?? menuToggleButton}
                    <DocumentStatus />
                </div>
                <LaunchChat />
            </div>
            <div style={{ height: 'calc(100vh - 40px)', overflow: 'hidden' }}>
                {children}
            </div>
            {Menu}
        </div>
    );
};

export default OfficeAddInContainer;

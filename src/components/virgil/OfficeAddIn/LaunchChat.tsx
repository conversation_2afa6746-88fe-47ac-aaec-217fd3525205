import React from 'react';
import { Button } from '@mui/material';
import { ChatIcon } from '../Icons/ChatIcon';
import { useBaseUrl } from '../hooks/useBaseUrl';
import { useUserMSAddInSettings } from '../hooks/useUserMSAddInSettings';
import { useSelector } from 'react-redux';
import { selectAddInMode } from '~/lib/features/addInSelectors';
import { openChat } from './addInUtils/common';

export default function LaunchChat() {
    const orgDomain = useBaseUrl()
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();
    const mode = useSelector(selectAddInMode);

    if (!orgDomain || userDataLoading) {
        return null;
    }

    const { chatHeight, chatWidth } = msAddInSettings;

    return (
        <Button onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            openChat({
                domain: orgDomain,
                chatHeight,
                chatWidth,
                mode,
            });
        }}>
            <ChatIcon width={15} />
        </Button>

    );
}

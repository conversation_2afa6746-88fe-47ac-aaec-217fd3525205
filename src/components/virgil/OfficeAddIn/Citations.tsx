import React from "react";
import { Citation } from "~/lib/types";
import DocumentAccordion from "../DocumentAccordion/DocumentAccordion";

interface CitationsProps {
  citations: Citation[];
}

const Citations: React.FC<CitationsProps> = ({ citations }) => {
  return (
    <div>
      {citations.map((citation, index) => (
        <DocumentAccordion
          key={index}
          fileName={citation.fileName!}
          content={citation.quote}
          sourceLink=""
          pageNumber={citation.metadata.page_number ?? 0}
          documentId={citation.metadata.document_id ?? ""}
        />
      ))}
    </div>
  );
};

export default Citations;

import { PropsWithChildren, useRef, useCallback, createContext, useContext, useState, RefObject } from "react";
import Script from "next/script";
import React from "react";
import { useDispatch } from "react-redux";
import { registerBodySearchHandler } from "./addInUtils/word/word";
import { registerFileOpenHandler, registerHostType } from "./addInUtils/common";
import { loadAllWorksheets, registerSheetChangeHandler } from "~/components/virgil/OfficeAddIn/addInUtils/excel";
import { useDocumentInfo } from "../hooks/useDocumentInfo";
import { setHostType } from "~/lib/features/addInSlice";

interface MicrosoftOfficeContextType {
    selectedParagraphRef: RefObject<string> | null;
}

const MicrosoftOfficeContext = createContext<MicrosoftOfficeContextType>({
    selectedParagraphRef: null,
});

export const useMicrosoftOffice = () => {
    const context = useContext(MicrosoftOfficeContext);
    if (!context) {
        throw new Error('useMicrosoftOffice must be used within a MicrosoftOfficeProvider');
    }
    return context;
};

const MicrosoftOfficeProvider: React.FC<PropsWithChildren> = ({ children }) => {
    const dispatch = useDispatch();
    useDocumentInfo();
    const selectedParagraphRef = useRef<string>("");
    const selectedTextRef = useRef<string>("");
    // const textSelectionHandler = (data: WordTextSelectionData) => {
    //     eventBus.emit('wordSelectionChanged', {
    //         paragraph: data.paragraph,
    //         text: data.text,
    //     });
    //     selectedParagraphRef.current = data.paragraph;
    //     selectedTextRef.current = data.text;
    // };

    const handleOfficeReady = useCallback((info: {
        host: Office.HostType;
        platform: Office.PlatformType;
    }) => {
        // Common
        registerHostType(info.host, dispatch);
        registerFileOpenHandler(info.host, dispatch);
        registerBodySearchHandler(info.host);
        // Word
        // TODO:
        // registerOnParagraphChanged(info.host);
        // registerTextSelectionHandler(info.host, debounce(textSelectionHandler, 100));
        // Excel
        registerSheetChangeHandler(info.host, dispatch);
        loadAllWorksheets(info.host, dispatch);

        // TODO:
        // registerCellSelectionHandler(info.host, dispatch);
        // registerCellEditHandler(info.host, dispatch);
    }, [dispatch]);

    return (
        <MicrosoftOfficeContext.Provider value={{ selectedParagraphRef }}>
            <Script
                src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"
                onLoad={() => {
                    Office.onReady(handleOfficeReady);
                    window.history.replaceState = () => { };
                    window.history.pushState = () => { };
                }}
            />
            {children}
        </MicrosoftOfficeContext.Provider>
    );
};

export default MicrosoftOfficeProvider;

import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import useDocumentStatus from './useDocumentStatus';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

const StatusIcon = ({
    isLoading,
    isSuccess,
}: {
    isLoading: boolean;
    isSuccess: boolean;
}) => {
    if (isLoading) {
        return <CircularProgress size={20} />;
    }
    if (isSuccess) {
        return <CheckCircleIcon sx={{ color: 'green' }} />;
    }
    return <CancelIcon sx={{ color: 'red' }} />;
};

export default function DocumentStatusPage() {
    const { documentStatus, documentFound, questionsGenerated, documentReady, documentGeneratingAnswers, isFetchingDocument } = useDocumentStatus();
    return (
        <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <StatusIcon isLoading={isFetchingDocument} isSuccess={documentFound} />
                    <Typography variant="body2">
                        {isFetchingDocument ? 'Loading document...' :
                            documentFound ?
                                "Document is found in Data Room" :
                                "Document is not found in Data Room"
                        }
                    </Typography>
                </Box>
                {!isFetchingDocument && questionsGenerated && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <StatusIcon isLoading={false} isSuccess={true} />
                        <Typography variant="body2">
                            Questions are generated
                        </Typography>
                    </Box>
                )}
                {!isFetchingDocument && documentGeneratingAnswers && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <StatusIcon isLoading={true} isSuccess={false} />
                        <Typography variant="body2">
                            Generating answers...
                        </Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
}

import React from 'react';
import { useSelector } from 'react-redux';
import { selectAddInNavigationPath } from '~/lib/features/addInSelectors';
import DocumentDetails from './DocumentDetails';
import AddInSettings from './AddInSettings/AddInSettings';
import DetailedAnswerContainer from './DetailedView/DetailedAnswerContainer';
import DocumentStatusPage from './DocumentStatusPage';
import DocumentFeedbackStatusPage from './DocumentFeedbackStatusPage';
import DDQTablesListContainerDebug from './Excel/DDQTablesListContainerDebug';

const DetailOverlay: React.FC = () => {
    const addInNavigationPath = useSelector(selectAddInNavigationPath);

    const renderContent = () => {
        switch (addInNavigationPath) {
            case 'document-details':
                return <DocumentDetails />;
            case 'settings':
                return <AddInSettings />;
            case 'collaboration':
                return <>Collaboration</>;
            case 'alerts':
                return <>Alerts</>;
            case 'detailed-answer':
                return <DetailedAnswerContainer />;
            case 'document-status':
                return <DocumentStatusPage />;
            case 'document-feedback-status':
                return <DocumentFeedbackStatusPage />;
            case 'excel-debug':
                return <DDQTablesListContainerDebug />;
            default:
                return null;
        }
    };

    if (!addInNavigationPath || addInNavigationPath === 'ddq') {
        return null;
    }

    return (
        <div
            style={{
                position: 'absolute',
                top: '41px',
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 10,
                backgroundColor: "white",
                padding: 0,
                margin: 0
            }}
        >
            {renderContent()}
        </div>
    );
};

export default DetailOverlay;

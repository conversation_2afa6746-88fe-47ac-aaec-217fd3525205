function getLocalStorageStringItem(key: string) {
  return localStorage.getItem(key);
}

function getLocalStorageArrayItem(key: string): string[] {
  const item = getLocalStorageStringItem(key);
  try {
    return item ? JSON.parse(item) : [];
  } catch (error) {
    return [];
  }
}

function setLocalStorageArrayItem(key: string, value: string[]) {
  localStorage.setItem(key, JSON.stringify(value));
}

function removeLocalStorageItem(key: string) {
  localStorage.removeItem(key);
}

export function setLocalStorageTagsIds(tagIds: string[]) {
  if (tagIds.length === 0) {
    removeLocalStorageItem("selectedTagIds");
  } else {
    setLocalStorageArrayItem("selectedTagIds", tagIds);
  }
}

export function setLocalStorageCategoryIds(categoryIds: string[]) {
  if (categoryIds.length === 0) {
    removeLocalStorageItem("selectedCategoryIds");
  } else {
    setLocalStorageArrayItem("selectedCategoryIds", categoryIds);
  }
}

export function setLocalStorageFundIds(fundIds: string[]) {
  if (fundIds.length === 0) {
    removeLocalStorageItem("selectedFundIds");
  } else {
    setLocalStorageArrayItem("selectedFundIds", fundIds);
  }
}

export function getLocalStorageTagsIds(): string[] {
  return getLocalStorageArrayItem("selectedTagIds");
}

export function getLocalStorageCategoryIds(): string[] {
  return getLocalStorageArrayItem("selectedCategoryIds");
}

export function getLocalStorageFundIds(): string[] {
  return getLocalStorageArrayItem("selectedFundIds");
}

export function setLocalStorageQuestionStatusTypes(
  questionStatusTypes: string[],
) {
  if (questionStatusTypes.length === 0) {
    removeLocalStorageItem("selectedQuestionStatusTypes");
  } else {
    setLocalStorageArrayItem(
      "selectedQuestionStatusTypes",
      questionStatusTypes,
    );
  }
}

export function setLocalStorageResponseStatuses(responseStatuses: string[]) {
  if (responseStatuses.length === 0) {
    removeLocalStorageItem("selectedResponseStatuses");
  } else {
    setLocalStorageArrayItem("selectedResponseStatuses", responseStatuses);
  }
}

export function getLocalStorageQuestionStatusTypes(): string[] {
  return getLocalStorageArrayItem("selectedQuestionStatusTypes");
}

export function getLocalStorageResponseStatuses(): string[] {
  return getLocalStorageArrayItem("selectedResponseStatuses");
}

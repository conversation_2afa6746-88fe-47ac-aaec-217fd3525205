import React from "react";
import { useDDQExcelTablesPerSheet } from "../../hooks/useDDQExcelTablesPerSheet";
import SelectSheet from "./SelectSheet";
import DDQTablesList from "./DDQTablesList";
import { DDQTableSelectProvider } from "../../contexts/DDQTableSelectContext";
import TableSelectActions from "./TableSelectActions";

const DDQTablesListContainer: React.FC = () => {

    const { tables, isLoading } = useDDQExcelTablesPerSheet();

    return (
        <DDQTableSelectProvider tables={tables}>
            <div className="flex flex-col h-full">
                <div className='flex justify-center items-center w-full p-2'>
                    <SelectSheet />
                </div>
                <TableSelectActions className="z-1" />
                <div className="flex-1 overflow-y-auto">
                    <DDQTablesList
                        tables={tables}
                        noTablesText="No tables found, please re-process document in the Data Room"
                        isLoading={isLoading}
                    />
                </div>
            </div>
        </DDQTableSelectProvider>
    );
};

export default DDQTablesListContainer;

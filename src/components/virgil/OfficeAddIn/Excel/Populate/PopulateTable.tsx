import { useState } from "react";
import { useDDQExcelGetCellsPerTable } from "~/components/virgil/hooks/useDDQExcelGetCellsPerTable";
import { Button } from "~/v2/components";
import { Typography } from "~/v2/components/ui/Typography";
import { insertCellRange } from "../../addInUtils/excel";

export default function PopulateTable({
    tableId,
    mode = "office",
}: {
    tableId: string;
    mode?: "office" | "web";
}) {
    const [populatedCells, setPopulatedCells] = useState<number>(0);
    const [isPopulating, setIsPopulating] = useState<boolean>(false);
    const [errors, setErrors] = useState<number>(0);
    const { cellGroups } = useDDQExcelGetCellsPerTable(tableId);
    const populateTable = async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        setPopulatedCells(0);
        setErrors(0);
        setIsPopulating(true);
        for (const cellsRow of Object.values(cellGroups)) {
            try {
                await insertCellRange({
                    cells: cellsRow.map((cell) => ({
                        value: cell.value ?? "",
                        address: cell.address,
                    })),
                });
                setPopulatedCells((prev) => prev + cellsRow.length);
            } catch (error) {
                console.error(error);
                setErrors((prev) => prev + 1);
            }
        }
        setIsPopulating(false);
    }
    return <div>
        <Button title="Populate" disabled={mode !== "office" || isPopulating} onClick={populateTable}>
            {isPopulating ? `Populating ${populatedCells}/${Object.values(cellGroups).reduce((acc, group) => acc + group.length, 0)}` : "Populate"}
        </Button>
        {populatedCells > 0 && <Typography variant="body">Populated: {populatedCells}/{Object.values(cellGroups).flat().length}</Typography>}
        {errors > 0 && <Typography variant="body">Errors: {errors}</Typography>}
    </div>
}
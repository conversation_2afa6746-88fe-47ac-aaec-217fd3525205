import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '~/v2/components/ui/Select';
import { activateWorksheet } from '~/components/virgil/OfficeAddIn/addInUtils/excel';
import { selectActiveSheetName, selectAddInMode, selectDocumentId, selectHostType, selectWorksheets } from '~/lib/features/addInSelectors';
import { api } from '~/trpc/react';
import { setActiveSheet } from '~/lib/features/addInSlice';

const SelectSheet: React.FC = () => {
    const activeSheet = useSelector(selectActiveSheetName);
    const documentId = useSelector(selectDocumentId);
    const mode = useSelector(selectAddInMode);
    const dispatch = useDispatch();
    const { data: worksheets } = api.sheet.getAllSheetsForDocument.useQuery({ documentId }, {
        enabled: !!documentId
    });

    const handleSheetChange = (value: string) => {
        if (mode === "web") {
            dispatch(setActiveSheet(value));
        } else {
            if (value && value !== activeSheet) {
                activateWorksheet(value);
            }
        }
    };

    return (
        <Select value={activeSheet || ''} onValueChange={handleSheetChange}>
            <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a sheet" />
            </SelectTrigger>
            <SelectContent>
                {worksheets?.map((sheet: string) => (
                    <SelectItem key={sheet} value={sheet}>
                        {sheet}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    );
};

export default SelectSheet;

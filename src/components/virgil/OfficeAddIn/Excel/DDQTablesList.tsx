import React, { <PERSON> } from "react";
import { DDQTable } from "~/lib/features/documentDDQSlice";
import InfoBox from "../IndexView/InfoBox";
import { AccordionRoot } from "~/v2/components/ui/Accordion/Accordion";
import DDQTablesListItem from "./DDQTablesListItem";

type Props = {
  tables: DDQTable[];
  noTablesText: string;
  isLoading: boolean;
};

const DDQTablesList: FC<Props> = ({ tables, noTablesText, isLoading }) => {
  let warningText = "";

  if (isLoading) {
    warningText = "Loading tables...";
  } else {
    if (tables.length === 0) {
      warningText = noTablesText;
    }
  }

  if (warningText) {
    return (
      <InfoBox text={warningText} />
    );
  }

  return (
    <AccordionRoot type="single" collapsible>
      {tables.map((table) => <DDQTablesListItem key={table.id} table={table} />)}
    </AccordionRoot>
  );
};

export default DDQTablesList;

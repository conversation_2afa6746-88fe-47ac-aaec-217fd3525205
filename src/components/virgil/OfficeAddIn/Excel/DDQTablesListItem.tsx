import React, { FC, useEffect, useMemo } from "react";
import { DDQTable } from "~/lib/features/documentDDQSlice";
import { AccordionContent, AccordionItem, AccordionTrigger } from "~/v2/components/ui/Accordion/Accordion";
import { Checkbox, Typography } from "~/v2/components";
import LaunchTablePreview from "./LaunchTablePreview";
import PopulateTable from "./Populate/PopulateTable";
import { selectRangeAndFocus } from "../addInUtils/excel";
import { AgGridExcelFromHtml } from "./AgGridExcel/AgGridExcelFromHtml";
import { ErrorBoundary } from 'react-error-boundary';
import { useDDQTableSelect } from "../../hooks/useDDQTableSelect";

const DDQTablesListItemContent = ({ table }: { table: DDQTable }) => {
  return (
    <AccordionContent>
      <div className="p-2">
        <div className="mb-2">
          <Typography variant="body">
            {table.name}
          </Typography>
          <Typography variant="body">
            {table.description}
          </Typography>
        </div>
        <div className="w-full overflow-auto mb-2" style={{ height: "200px" }}>
          {table.generatedTable &&
            <ErrorBoundary fallbackRender={() => <Typography variant="h4">Error parsing table</Typography>}>
              <AgGridExcelFromHtml html={table.generatedTable} />
            </ErrorBoundary>
          }
        </div>
        <div className="flex items-center gap-x-2">
          <PopulateTable tableId={table.id} />
          <LaunchTablePreview tableId={table.id} />
        </div>
      </div>
    </AccordionContent>
  )
}

type Props = {
  table: DDQTable;
};

const DDQTablesListItem: FC<Props> = ({ table }) => {
  const { selectedIds, handleSelectSingle } = useDDQTableSelect();
  const onSelectTable = () => {
    if (table.tableAddress) {
      selectRangeAndFocus({ address: table.tableAddress });
    }
  }
  return (
    <AccordionItem key={table.id} value={table.id}>
      <AccordionTrigger
        className="flex items-center gap-x-2"
        onClick={onSelectTable}
      >
        <Checkbox
          checked={selectedIds.has(table.id)}
          onCheckedChange={() => {
            handleSelectSingle(table.id);
          }}
          onClick={(e) => e.stopPropagation()}
        />
        <Typography variant="sectionHeader">
          {table.name}
        </Typography>
      </AccordionTrigger>
      <DDQTablesListItemContent table={table} />
    </AccordionItem>

  );
};

export default DDQTablesListItem;

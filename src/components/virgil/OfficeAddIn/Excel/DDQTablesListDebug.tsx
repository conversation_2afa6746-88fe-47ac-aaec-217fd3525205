import React, { <PERSON> } from "react";
import { Box, Divider, Typography } from "@mui/material";
import { DDQTable } from "~/lib/features/documentDDQSlice";
import InfoBox from "../IndexView/InfoBox";

type Props = {
  tables: DDQTable[];
  noTablesText: string;
  isLoading: boolean;
};

// Function to inject CSS styles into HTML content
const injectTableStyles = (html: string): string => {
  const tableStyles = `
    <style>
      table {
        border-collapse: collapse;
        width: 100%;
      }
      th, td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
      }
      th {
        background-color: #4a90e2;
        color: white;
        font-weight: bold;
      }
      td:first-child {
        font-weight: bold;
      }
    </style>
  `;

  return tableStyles + html;
};

const DDQTablesListDebug: FC<Props> = ({ tables, noTablesText, isLoading }) => {
  let warningText = "";

  if (isLoading) {
    warningText = "Loading prompts...";
  } else {
    if (tables.length === 0) {
      warningText = noTablesText;
    }
  }

  if (warningText) {
    return (
      <Box
        sx={{ overflow: "auto", height: "calc(100vh - 55px - 50px - 30px)" }}
      >
        <InfoBox text={warningText} />
      </Box>
    );
  }

  return (
    <>
      <Box
        sx={{
          overflow: "auto",
          height: "calc(100vh - 55px - 45px)",
          position: "absolute",
          top: "100px",
          left: 0,
          padding: 1,
          width: "100%",
        }}
      >
        <Typography variant="h5">
          Sheet contains {tables.length}{" "}
          {tables.length === 1 ? "Table" : "Tables"}
        </Typography>
        <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
        {tables.map((table: DDQTable, index: number) => (
          <Box key={table.id}>
            <Typography variant="h6">
              {index + 1}. {table.name}
            </Typography>
          </Box>
        ))}
        <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
        {tables.map((table) => {
          return (
            <Box
              key={table.id}
              sx={{
                marginBottom: 2,
                padding: 1,
                border: "1px solid #e0e0e0",
                borderRadius: 2,
                maxHeight: "1000px",
                overflow: "scroll",
                width: "100%",
              }}
            >
              <Typography variant="h6">{table.name}</Typography>
              <Typography variant="body1">{table.description}</Typography>
              {/* <Markdown markdown={table.markdown} /> */}
              <Divider sx={{ marginBottom: 2, marginTop: 2 }} />
              <Typography variant="body1">
                {table.table_metadata.map((metadata) => (
                  <Typography variant="body1" key={metadata.label}>
                    {metadata.label}: {metadata.value}
                  </Typography>
                ))}
              </Typography>
              <Divider sx={{ marginBottom: 2, marginTop: 2 }} />

              <Typography variant="h6">Extracted Table</Typography>
              <div
                dangerouslySetInnerHTML={{
                  __html: injectTableStyles(table.html),
                }}
                style={{
                  maxHeight: "400px",
                  overflowX: "scroll",
                  marginBottom: 2,
                  marginTop: 2,
                }}
              />

              <Typography variant="h6">Generated Table</Typography>
              <div
                dangerouslySetInnerHTML={{
                  __html: injectTableStyles(table.generatedTable),
                }}
                style={{
                  maxHeight: "400px",
                  overflowX: "scroll",
                  marginBottom: 2,
                  marginTop: 2,
                }}
              />
            </Box>
          );
        })}
      </Box>
    </>
  );
};

export default DDQTablesListDebug;

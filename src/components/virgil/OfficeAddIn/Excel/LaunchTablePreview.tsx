import { Button } from "~/v2/components";
import { openTablePreview } from "../addInUtils/excel";
import { EyeIcon } from "lucide-react";
import { useBaseUrl } from "../../hooks/useBaseUrl";
import { useSelector } from "react-redux";
import { selectActiveSheetName, selectAddInMode, selectDocumentId } from "~/lib/features/addInSelectors";


export default function LaunchTablePreview({ tableId }: { tableId: string }) {
    const orgDomain = useBaseUrl()
    const mode = useSelector(selectAddInMode);
    const sheetName = useSelector(selectActiveSheetName);
    const documentId = useSelector(selectDocumentId);

    if (!orgDomain) {
        return null;
    }

    return (
        <Button
            variant="outline"
            className="ml-auto"
            title="Preview Table"
            onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                openTablePreview({
                    domain: orgDomain,
                    tableHeight: 70,
                    tableWidth: 80,
                    tableId,
                    documentId,
                    sheetName,
                    mode,
                });
            }}>
            <EyeIcon />
        </Button>
    );
}

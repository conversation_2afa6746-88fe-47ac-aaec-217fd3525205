import { createContext, Dispatch, SetStateAction, useContext, useState } from "react";
import { ParsedCell } from "./types";

export const SelectedCellsContext = createContext<{
    selectedCells: ParsedCell[];
    setSelectedCells: Dispatch<SetStateAction<ParsedCell[]>>;
}>({
    selectedCells: [],
    setSelectedCells: () => { },
});

export const useSelectedCellsContext = () => {
    const context = useContext(SelectedCellsContext);
    if (!context) {
        throw new Error("useSelectedCellsContext must be used within a SelectedContextProvider");
    }
    return context;
}

export const SelectedCellsContextProvider = ({ children }: { children: React.ReactNode }) => {
    const [selectedCells, setSelectedCells] = useState<ParsedCell[]>([]);
    return <SelectedCellsContext.Provider value={{ selectedCells, setSelectedCells }}>{children}</SelectedCellsContext.Provider>
}

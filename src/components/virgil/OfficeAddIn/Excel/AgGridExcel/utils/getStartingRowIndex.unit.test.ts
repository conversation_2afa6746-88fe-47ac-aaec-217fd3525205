import { getStartingRowIndex } from "./getStartingRowIndex";
import { CellDataType } from "../types";

describe("getStartingRowIndex", () => {
  it("should return 0 for empty row data", () => {
    const cells2DArray: CellDataType[][] = [];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(0);
  });

  it("should return the minimum row number for single row data", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A5" },
        { text: "Value 2", address: "B5" },
        { text: "Value 3", address: "C5" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(5);
  });

  it("should return the minimum row number for multiple rows with different row numbers", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A10" },
        { text: "Value 2", address: "B10" },
      ],
      [
        { text: "Value 3", address: "A15" },
        { text: "Value 4", address: "B15" },
      ],
      [
        { text: "Value 5", address: "A8" },
        { text: "Value 6", address: "B8" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(8);
  });

  it("should handle complex scenarios with multiple rows and mixed addresses", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A20" },
        { text: "Value 2", address: "B5" },
        { text: "Value 3", address: "C15" },
      ],
      [
        { text: "Value 4", address: "A3" },
        { text: "Value 5", address: "B25" },
        { text: "Value 6", address: "C8" },
      ],
      [
        { text: "Value 7", address: "A1" },
        { text: "Value 8", address: "B30" },
        { text: "Value 9", address: "C12" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(1);
  });

  it("should handle addresses with different column letters", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A5" },
        { text: "Value 2", address: "Z5" },
        { text: "Value 3", address: "AA5" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(5);
  });

  it("should handle addresses with large row numbers", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A1000" },
        { text: "Value 2", address: "B1000" },
      ],
      [
        { text: "Value 3", address: "A500" },
        { text: "Value 4", address: "B500" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(500);
  });

  it("should handle addresses with single digit row numbers", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A1" },
        { text: "Value 2", address: "B9" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(1);
  });

  it("should handle addresses with double digit row numbers", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A10" },
        { text: "Value 2", address: "B99" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(10);
  });

  it("should handle addresses with triple digit row numbers", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A100" },
        { text: "Value 2", address: "B999" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(100);
  });

  it("should handle edge case with row number 0", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Value 1", address: "A0" },
        { text: "Value 2", address: "B5" },
      ],
    ];
    const result = getStartingRowIndex(cells2DArray);
    expect(result).toBe(0);
  });
});

import { ColDef } from "ag-grid-community";
import { generateColumnDefs } from "./generatedColumnDefs";
import { CellDataType } from "../types";

const defaultExpect: Partial<ColDef> = {
  sortable: false,
  filter: false,
  resizable: true,
  suppressMovable: true,
  suppressHeaderMenuButton: true,
  suppressHeaderContextMenu: true,
  valueFormatter: expect.any(Function),
  spanRows: expect.any(Function),
  // colSpan: expect.any(Function),
};

describe("generatedColumnDefs", () => {
  it("should return empty array for empty row data", () => {
    const result = generateColumnDefs([]);
    expect(result).toEqual([]);
  });

  it("should generate column definitions for single column data", () => {
    const rowData: CellDataType[][] = [
      [{ text: "row1 col1", address: "A1" }],
      [{ text: "row2 col1", address: "A2" }],
      [{ text: "row3 col1", address: "A3" }],
    ];

    const result = generateColumnDefs(rowData);

    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      headerName: "A",
      field: "A",
      ...defaultExpect,
    });
  });

  it("should generate column definitions for multiple columns in order", () => {
    const rowData: CellDataType[][] = [
      [{ text: "row1 col1", address: "A1" }],
      [{ text: "row1 col2", address: "B1" }],
      [{ text: "row1 col3", address: "C1" }],
    ];

    const result = generateColumnDefs(rowData);

    expect(result).toHaveLength(3);
    expect(result[0]).toMatchObject({
      headerName: "A",
      field: "A",
      ...defaultExpect,
    });
    expect(result[1]).toMatchObject({
      headerName: "B",
      field: "B",
      ...defaultExpect,
    });
    expect(result[2]).toMatchObject({
      headerName: "C",
      field: "C",
      ...defaultExpect,
    });
  });
});

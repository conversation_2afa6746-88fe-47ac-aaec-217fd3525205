import { CellDataType, RowData } from "../types";
import { parseColumnFromAddress } from "./generatedColumnDefs";

// Helper to convert Excel column letters to a 1-based number (A=1, Z=26, AA=27, ...)
function columnToNumber(column: string): number {
  let result = 0;
  for (let i = 0; i < column.length; i += 1) {
    const charCode = column.charCodeAt(i) - 64; // 'A' is 65 in ASCII
    result = result * 26 + charCode;
  }
  return result;
}

// Helper to convert a 1-based number to Excel column letters
function numberToColumn(n: number): string {
  let result = "";
  let number = n;
  while (number > 0) {
    const remainder = (number - 1) % 26;
    result = String.fromCharCode(65 + remainder) + result;
    number = Math.floor((number - 1) / 26);
  }
  return result;
}

function getNextColumn(column: string): string {
  return numberToColumn(columnToNumber(column) + 1);
}

export function convert2DArrayToAgGridData(
  cells2DArray: CellDataType[][],
): RowData[] {
  const rowData: RowData[] = [];

  cells2DArray.forEach((row) => {
    const rowDataItem: RowData = {};

    row.forEach((cell) => {
      const { column } = parseColumnFromAddress(cell.address);
      if (!column) return;

      // Find the first available column slot for this cell within the row.
      // If a duplicate address maps to the same column, shift to the right.
      let targetColumn = column;
      while (rowDataItem[targetColumn] !== undefined) {
        targetColumn = getNextColumn(targetColumn);
      }

      rowDataItem[targetColumn] = cell;
    });

    rowData.push(rowDataItem);
  });

  return rowData;
}

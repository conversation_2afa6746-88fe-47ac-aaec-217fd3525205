import { CellDataType } from "../types";

export function convertHtmlTableStringTo2DArray(
  htmlString: string,
): CellDataType[][] {
  // Create a DOM parser (in browser)
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, "text/html");
  const tableEl = doc.querySelector("table");

  if (!tableEl) throw new Error("No <table> element found in HTML string");

  const matrix: CellDataType[][] = [];
  const occupancy: boolean[][] = [];

  const rows = tableEl.rows;

  for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
    const row = rows[rowIndex];
    const cells = row?.cells;

    matrix[rowIndex] = matrix[rowIndex] || [];
    occupancy[rowIndex] = occupancy[rowIndex] || [];

    let colIndex = 0;

    for (let cellIndex = 0; cellIndex < (cells?.length ?? 0); cellIndex++) {
      const cell = cells?.[cellIndex];

      // Find next available column index
      while (occupancy[rowIndex]?.[colIndex]) {
        colIndex++;
      }

      const rowspan = parseInt(cell?.getAttribute("rowspan") || "1", 10);
      const colspan = parseInt(cell?.getAttribute("colspan") || "1", 10);
      const text = cell?.textContent?.trim() ?? "";
      const id = cell?.getAttribute("id");
      const address = id?.split("-").pop();

      for (let r = 0; r < rowspan; r++) {
        for (let c = 0; c < colspan; c++) {
          const targetRow = rowIndex + r;
          const targetCol = colIndex + c;

          matrix[targetRow] = matrix[targetRow] || [];
          occupancy[targetRow] = occupancy[targetRow] || [];

          const isAnchorCell = r === 0 && c === 0;
          matrix[targetRow][targetCol] = {
            text,
            address: address ?? "",
            ...(isAnchorCell && colspan > 1 ? { colSpan: colspan } : {}),
          };
          occupancy[targetRow][targetCol] = true;
        }
      }

      colIndex += colspan;
    }
  }

  return matrix;
}

import { describe, it, expect } from "@jest/globals";
import { convert2DArrayToAgGridData } from "./convert2DArrayToAgGridData";
import { CellDataType } from "../types";

describe("convert2DArrayToAgGridData", () => {
  it("should convert a 2D array of cells to AgGrid row data format", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Name", address: "A1" },
        { text: "Age", address: "B1" },
        { text: "City", address: "C1" },
      ],
      [
        { text: "<PERSON>", address: "A2" },
        { text: "25", address: "B2" },
        { text: "New York", address: "C2" },
      ],
    ];

    const expected = [
      {
        A: { text: "Name", address: "A1" },
        B: { text: "Age", address: "B1" },
        C: { text: "City", address: "C1" },
      },
      {
        A: { text: "<PERSON>", address: "A2" },
        B: { text: "25", address: "B2" },
        C: { text: "New York", address: "C2" },
      },
    ];

    const result = convert2DArrayToAgGridData(cells2DArray);
    expect(result).toEqual(expected);
  });

  it("should handle empty rows and cells with invalid addresses", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Header", address: "A1" },
        { text: "Data", address: "B1" },
      ],
      [
        { text: "Value1", address: "A2" },
        { text: "Value2", address: "B2" },
        { text: "Invalid", address: "123" }, // Invalid address format
      ],
      [], // Empty row
    ];

    const expected = [
      {
        A: { text: "Header", address: "A1" },
        B: { text: "Data", address: "B1" },
      },
      {
        A: { text: "Value1", address: "A2" },
        B: { text: "Value2", address: "B2" },
      },
      {}, // Empty row object
    ];

    const result = convert2DArrayToAgGridData(cells2DArray);
    expect(result).toEqual(expected);
  });
  it("should render col spans correctly", () => {
    const cells2DArray: CellDataType[][] = [
      [
        { text: "Col 1 Row 1", address: "A1" },
        { text: "Col 1 Row 1", address: "A1" }, // col span 2
      ],
      [
        { text: "Col 1 Row 2", address: "A2" },
        { text: "Col 1 Row 2", address: "A2" }, // col span 2
      ],
      [
        { text: "Col 1 Row 3", address: "A3" },
        { text: "Col 1 Row 3", address: "B3" }, // col span 2
      ],
    ];

    const expected = [
      {
        A: { text: "Col 1 Row 1", address: "A1" },
        B: { text: "Col 1 Row 1", address: "A1" },
      },
      {
        A: { text: "Col 1 Row 2", address: "A2" },
        B: { text: "Col 1 Row 2", address: "A2" },
      },
      {
        A: { text: "Col 1 Row 3", address: "A3" },
        B: { text: "Col 1 Row 3", address: "B3" },
      },
    ];

    const result = convert2DArrayToAgGridData(cells2DArray);
    expect(result).toEqual(expected);
  });
});

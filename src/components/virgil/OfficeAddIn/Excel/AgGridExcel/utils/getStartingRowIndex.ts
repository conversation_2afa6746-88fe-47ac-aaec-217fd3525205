import { CellDataType, RowData } from "../types";

// Helper function to extract row number from address
const extractRowFromAddress = (address: string): number => {
  const regex = /(\d+)$/;
  const match = regex.exec(address);
  return match?.[1] ? parseInt(match[1], 10) : 0;
};

export const getStartingRowIndex = (cells2DArray: CellDataType[][]): number => {
  if (cells2DArray.length === 0) return 0;

  const addresses = new Set<number>();
  cells2DArray.forEach((row) => {
    Object.values(row).forEach((cell) => {
      const row = extractRowFromAddress(cell.address);
      addresses.add(row);
    });
  });

  return Math.min(...Array.from(addresses));
};

import { ColDef, ColSpanParams, ValueFormatterParams } from "ag-grid-community";
import { CellDataType, RowData } from "../types";
const defaultColDef: ColDef<RowData> = {
  sortable: false,
  filter: false,
  resizable: true,
  suppressMovable: true,
  suppressHeaderMenuButton: true,
  suppressHeaderContextMenu: true,
  valueFormatter: (params: ValueFormatterParams<RowData, CellDataType, any>) =>
    params.value?.text.toString() ?? "",
  spanRows: ({
    valueA,
    valueB,
  }: {
    valueA: CellDataType;
    valueB: CellDataType;
  }) =>
    !!(valueA?.address && valueB?.address && valueA.address === valueB.address),
  colSpan: (params: ColSpanParams<RowData, any, any>) =>
    params.data?.[params.column?.getColId()]?.colSpan ?? 1,
};

export const parseColumnFromAddress = (address: string) => {
  const columnMatch = /[A-Z]+/.exec(address);
  const rowMatch = /\d+/.exec(address);
  const column = columnMatch ? columnMatch[0] : undefined;
  const row = rowMatch ? rowMatch[0] : undefined;
  return { column, row };
};

export const generateColumnDefs = (
  rowData: CellDataType[][],
): ColDef<RowData>[] => {
  // Get all unique column keys from all rows to handle spans properly
  const allColumns = new Set<string>();

  rowData.forEach((row) => {
    row.forEach((cell) => {
      const { column } = parseColumnFromAddress(cell.address);
      if (column) {
        allColumns.add(column);
      }
    });
  });

  const columns = Array.from(allColumns).sort();

  return columns.map((col) => {
    return {
      ...defaultColDef,
      field: col,
      headerName: col,
    };
  });
};

import { convertHtmlTableStringTo2DArray } from "./convertHtmlTableStringTo2DArray";
import { JSDOM } from "jsdom";

describe("convertHtmlTableStringTo2DArray", () => {
  beforeEach(() => {
    // Mock DOMParser for Node.js environment
    global.DOMParser = class DOMParser {
      parseFromString(string: string, contentType: string) {
        const dom = new JSDOM(string, { contentType });
        return dom.window.document;
      }
    } as any;
  });

  afterEach(() => {
    delete (global as any).DOMParser;
  });

  describe("basic table conversion", () => {
    it("should convert a simple 2x2 table", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">A</td>
            <td id="sjs-B1">B</td>
          </tr>
          <tr>
            <td id="sjs-A2">C</td>
            <td id="sjs-B2">D</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "A", address: "A1" },
          { text: "B", address: "B1" },
        ],
        [
          { text: "C", address: "A2" },
          { text: "D", address: "B2" },
        ],
      ]);
    });

    it("should handle empty cells", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">A</td>
            <td id="sjs-B1"></td>
          </tr>
          <tr>
            <td id="sjs-A2"></td>
            <td id="sjs-B2">D</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "A", address: "A1" },
          { text: "", address: "B1" },
        ],
        [
          { text: "", address: "A2" },
          { text: "D", address: "B2" },
        ],
      ]);
    });

    it("should handle cells with whitespace", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">  A  </td>
            <td id="sjs-B1">  B  </td>
          </tr>
          <tr>
            <td id="sjs-A2">  C  </td>
            <td id="sjs-B2">  D  </td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "A", address: "A1" },
          { text: "B", address: "B1" },
        ],
        [
          { text: "C", address: "A2" },
          { text: "D", address: "B2" },
        ],
      ]);
    });
  });

  describe("table with colspan", () => {
    it("should handle colspan correctly", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" colspan="2">Header</td>
          </tr>
          <tr>
            <td id="sjs-A2">A</td>
            <td id="sjs-B2">B</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Header", address: "A1", colSpan: 2 },
          { text: "Header", address: "A1" },
        ],
        [
          { text: "A", address: "A2" },
          { text: "B", address: "B2" },
        ],
      ]);
    });

    it("should handle multiple colspan cells", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" colspan="2">Header 1</td>
            <td id="sjs-C1" colspan="2">Header 2</td>
          </tr>
          <tr>
            <td id="sjs-A2">A</td>
            <td id="sjs-B2">B</td>
            <td id="sjs-C2">C</td>
            <td id="sjs-D2">D</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Header 1", address: "A1", colSpan: 2 },
          { text: "Header 1", address: "A1" },
          { text: "Header 2", address: "C1", colSpan: 2 },
          { text: "Header 2", address: "C1" },
        ],
        [
          { text: "A", address: "A2" },
          { text: "B", address: "B2" },
          { text: "C", address: "C2" },
          { text: "D", address: "D2" },
        ],
      ]);
    });
  });

  describe("table with rowspan", () => {
    it("should handle rowspan correctly", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" rowspan="2">Side</td>
            <td id="sjs-B1">A</td>
          </tr>
          <tr>
            <td id="sjs-B2">B</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Side", address: "A1" },
          { text: "A", address: "B1" },
        ],
        [
          { text: "Side", address: "A1" },
          { text: "B", address: "B2" },
        ],
      ]);
    });

    it("should handle multiple rowspan cells", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" rowspan="2">Left</td>
            <td id="sjs-B1">A</td>
            <td id="sjs-C1" rowspan="2">Right</td>
          </tr>
          <tr>
            <td id="sjs-B2">B</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Left", address: "A1" },
          { text: "A", address: "B1" },
          { text: "Right", address: "C1" },
        ],
        [
          { text: "Left", address: "A1" },
          { text: "B", address: "B2" },
          { text: "Right", address: "C1" },
        ],
      ]);
    });
  });

  describe("complex table with both rowspan and colspan", () => {
    it("should handle complex table structure", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" colspan="2" rowspan="2">Corner</td>
            <td id="sjs-C1">C</td>
          </tr>
          <tr>
            <td id="sjs-C2">F</td>
          </tr>
          <tr>
            <td id="sjs-A3">G</td>
            <td id="sjs-B3">H</td>
            <td id="sjs-C3">I</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Corner", address: "A1", colSpan: 2 },
          { text: "Corner", address: "A1" },
          { text: "C", address: "C1" },
        ],
        [
          { text: "Corner", address: "A1" },
          { text: "Corner", address: "A1" },
          { text: "F", address: "C2" },
        ],
        [
          { text: "G", address: "A3" },
          { text: "H", address: "B3" },
          { text: "I", address: "C3" },
        ],
      ]);
    });

    it("should handle overlapping spans correctly", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" rowspan="3">A</td>
            <td id="sjs-B1" colspan="2">B</td>
          </tr>
          <tr>
            <td id="sjs-B2">C</td>
            <td id="sjs-C2">D</td>
          </tr>
          <tr>
            <td id="sjs-B3">E</td>
            <td id="sjs-C3">F</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "A", address: "A1" },
          { text: "B", address: "B1", colSpan: 2 },
          { text: "B", address: "B1" },
        ],
        [
          { text: "A", address: "A1" },
          { text: "C", address: "B2" },
          { text: "D", address: "C2" },
        ],
        [
          { text: "A", address: "A1" },
          { text: "E", address: "B3" },
          { text: "F", address: "C3" },
        ],
      ]);
    });
  });

  describe("edge cases", () => {
    it("should handle single cell table", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">Single Cell</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([[{ text: "Single Cell", address: "A1" }]]);
    });

    it("should handle table with only header row", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">Header 1</td>
            <td id="sjs-B1">Header 2</td>
            <td id="sjs-C1">Header 3</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Header 1", address: "A1" },
          { text: "Header 2", address: "B1" },
          { text: "Header 3", address: "C1" },
        ],
      ]);
    });

    it("should handle cells with numeric content", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">1</td>
            <td id="sjs-B1">2</td>
          </tr>
          <tr>
            <td id="sjs-A2">3</td>
            <td id="sjs-B2">4</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "1", address: "A1" },
          { text: "2", address: "B1" },
        ],
        [
          { text: "3", address: "A2" },
          { text: "4", address: "B2" },
        ],
      ]);
    });

    it("should handle table starting with C4 coordinates", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-C4">Start</td>
            <td id="sjs-D4">Data</td>
          </tr>
          <tr>
            <td id="sjs-C5">More</td>
            <td id="sjs-D5">Info</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Start", address: "C4" },
          { text: "Data", address: "D4" },
        ],
        [
          { text: "More", address: "C5" },
          { text: "Info", address: "D5" },
        ],
      ]);
    });
  });

  describe("error handling", () => {
    it("should throw error when no table element is found", () => {
      const htmlString = "<div>No table here</div>";

      expect(() => {
        convertHtmlTableStringTo2DArray(htmlString);
      }).toThrow("No <table> element found in HTML string");
    });

    it("should throw error for empty string", () => {
      expect(() => {
        convertHtmlTableStringTo2DArray("");
      }).toThrow("No <table> element found in HTML string");
    });

    it("should throw error for null/undefined input", () => {
      expect(() => {
        convertHtmlTableStringTo2DArray(null as any);
      }).toThrow("No <table> element found in HTML string");
    });
  });

  describe("real-world scenarios", () => {
    it("should handle table with mixed content types", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1">Name</td>
            <td id="sjs-B1">Age</td>
            <td id="sjs-C1">City</td>
          </tr>
          <tr>
            <td id="sjs-A2">John Doe</td>
            <td id="sjs-B2">30</td>
            <td id="sjs-C2">New York</td>
          </tr>
          <tr>
            <td id="sjs-A3">Jane Smith</td>
            <td id="sjs-B3">25</td>
            <td id="sjs-C3">Los Angeles</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Name", address: "A1" },
          { text: "Age", address: "B1" },
          { text: "City", address: "C1" },
        ],
        [
          { text: "John Doe", address: "A2" },
          { text: "30", address: "B2" },
          { text: "New York", address: "C2" },
        ],
        [
          { text: "Jane Smith", address: "A3" },
          { text: "25", address: "B3" },
          { text: "Los Angeles", address: "C3" },
        ],
      ]);
    });

    it("should handle table with merged cells in header", () => {
      const htmlString = `
        <table>
          <tr>
            <td id="sjs-A1" colspan="3">Sales Report</td>
          </tr>
          <tr>
            <td id="sjs-A2">Q1</td>
            <td id="sjs-B2">Q2</td>
            <td id="sjs-C2">Q3</td>
          </tr>
          <tr>
            <td id="sjs-A3">100</td>
            <td id="sjs-B3">150</td>
            <td id="sjs-C3">200</td>
          </tr>
        </table>
      `;

      const result = convertHtmlTableStringTo2DArray(htmlString);

      expect(result).toEqual([
        [
          { text: "Sales Report", address: "A1", colSpan: 3 },
          { text: "Sales Report", address: "A1" },
          { text: "Sales Report", address: "A1" },
        ],
        [
          { text: "Q1", address: "A2" },
          { text: "Q2", address: "B2" },
          { text: "Q3", address: "C2" },
        ],
        [
          { text: "100", address: "A3" },
          { text: "150", address: "B3" },
          { text: "200", address: "C3" },
        ],
      ]);
    });
  });
});

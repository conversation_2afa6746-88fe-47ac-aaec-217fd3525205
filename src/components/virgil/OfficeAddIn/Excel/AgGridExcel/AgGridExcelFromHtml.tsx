import { convertHtmlTableStringTo2DArray } from "./utils/convertHtmlTableStringTo2DArray";
import { getStartingRowIndex } from "./utils/getStartingRowIndex";
import { generateColumnDefs } from "./utils/generatedColumnDefs";
import { CellDataType, ParsedCell, RowData } from "./types";
import { ColDef } from "ag-grid-community";
import { AgGridExcel } from "./AgGridExcel";
import { convert2DArrayToAgGridData } from "./utils/convert2DArrayToAgGridData";

export type AgGridExcelFromHtmlProps = {
    html: string;
    onCellSelectionChange?: (cells: ParsedCell[]) => void;
}

export const AgGridExcelFromHtml = ({
    html,
    onCellSelectionChange,
}: AgGridExcelFromHtmlProps) => {
    const cells2DArray: CellDataType[][] = convertHtmlTableStringTo2DArray(html);
    const columDefs: ColDef<RowData>[] = generateColumnDefs(cells2DArray);
    const rowData: RowData[] = convert2DArrayToAgGridData(cells2DArray);
    const startRowIndex = getStartingRowIndex(cells2DArray);
    return (
        <AgGridExcel
            rowData={rowData}
            columnDefs={columDefs}
            startRowIndex={startRowIndex}
            onCellSelectionChange={onCellSelectionChange}
        />
    )
};
import { themeQuartz } from "ag-grid-enterprise";

export const excelTheme = ({
  headerFontSize = 14,
  dataFontSize = 14,
  spacing = 4,
}: {
  headerFontSize?: number;
  dataFontSize?: number;
  spacing?: number;
}) => {
  return themeQuartz.withParams({
    rowHoverColor: "#00000000",
    accentColor: "#5E8779",
    browserColorScheme: "light",
    fontFamily: {
      googleFont: "Open Sans",
    },
    headerFontFamily: {
      googleFont: "Open Sans",
    },
    headerFontSize,
    dataFontSize,
    headerFontWeight: 400,
    spacing,
    wrapperBorderRadius: 0,
    columnBorder: true,
    rowBorder: true,
    headerColumnBorder: true,
  });
};

import { ModuleRegistry, ColDef, CellSelectionChangedEvent, GridReadyEvent, ValueFormatterParams, CellRange } from 'ag-grid-community';
import { AllEnterpriseModule, LicenseManager } from "ag-grid-enterprise";

import {
    <PERSON>rid<PERSON><PERSON>,
} from "ag-grid-community";
import {
    CellSelectionModule,
    ClipboardModule,
    ContextMenuModule,
} from "ag-grid-enterprise";

LicenseManager.setLicenseKey(process.env.NEXT_PUBLIC_AG_GRID_LICENSE_KEY || "[TRIAL]_this_{AG_Charts_and_AG_Grid}_Enterprise_key_{AG-090576}_is_granted_for_evaluation_only___Use_in_production_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_purchasing_a_production_key_please_contact_info@ag-grid.com___You_are_granted_a_{Single_Application}_Developer_License_for_one_application_only___All_Front-End_JavaScript_developers_working_on_the_application_would_need_to_be_licensed___This_key_will_deactivate_on_{31 August 2025}____[v3]_[0102]_MTc1NjU5NDgwMDAwMA==055771d37eabf862ce4b35dbb0d2a1df");
ModuleRegistry.registerModules([AllEnterpriseModule]);

import { AgGridReact } from 'ag-grid-react';

import { useRef, useCallback, useMemo } from 'react';
import { excelTheme } from './excelTheme';
import { CellDataType, ParsedCell, RowData } from './types';
ModuleRegistry.registerModules([
    // ClientSideRowModelModule,
    ClipboardModule,
    // ColumnMenuModule,
    ContextMenuModule,
    CellSelectionModule,
]);

type AgGridStyledProps<Tvalue extends CellDataType> = {
    startRowIndex?: number;
    rowData: RowData[];
    columnDefs?: ColDef<RowData, Tvalue>[];
    onCellSelectionChange?: (cells: ParsedCell[]) => void;
}

export const AgGridExcel = ({
    startRowIndex = 1,
    rowData,
    columnDefs,
    onCellSelectionChange
}: AgGridStyledProps<CellDataType>) => {
    const gridApiRef = useRef<GridApi<any> | null>(null);

    const onGridReady = (params: GridReadyEvent<any>) => {
        gridApiRef.current = params.api;
    };

    // Parse selected cells from cell ranges
    const parseSelectedCells = useCallback((ranges: CellRange[]) => {
        const parsedCells: ParsedCell[] = [];

        ranges.forEach((range) => {
            const startRow = range.startRow?.rowIndex ?? 0;
            const endRow = range.endRow?.rowIndex ?? startRow;

            // Get all columns in the range
            const columns = range.columns?.map((col: any) => col.getColId()) ?? [];

            // Parse each cell in the range
            for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
                columns.forEach((columnId: string) => {
                    const node = gridApiRef.current?.getDisplayedRowAtIndex(rowIndex);
                    if (node) {
                        const cellData: CellDataType = node.data?.[columnId];

                        parsedCells.push({
                            rowIndex,
                            columnId,
                            text: cellData?.text ?? '',
                            address: cellData?.address ?? '',
                        });
                    }
                });
            }
        });

        return parsedCells;
    }, []);

    // Handle cell selection changes
    const onCellSelectionChanged = useCallback((event: CellSelectionChangedEvent) => {
        if (!gridApiRef.current) return;

        // Get all selected cell ranges
        const ranges: CellRange[] | null = gridApiRef.current.getCellRanges();

        // Handle null case
        if (!ranges) {
            if (onCellSelectionChange) {
                onCellSelectionChange([]);
            }
            return;
        }

        // Parse the selected cells
        const parsedCells = parseSelectedCells(ranges);

        // Call the callback if provided
        if (onCellSelectionChange) {
            onCellSelectionChange(parsedCells);
        }
    }, [parseSelectedCells, onCellSelectionChange]);

    return (
        <AgGridReact
            rowData={rowData}
            columnDefs={columnDefs}
            theme={excelTheme({ headerFontSize: 10, dataFontSize: 10, spacing: 2 })}
            enableCellTextSelection={false}
            suppressCellFocus={false}
            suppressColumnMoveAnimation={true}
            onGridReady={onGridReady}
            onCellSelectionChanged={onCellSelectionChanged}
            cellSelection={{
                handle: { mode: "range" },
                suppressMultiRanges: false,
                enableHeaderHighlight: true
            }}
            rowNumbers={{
                valueGetter: `node.rowIndex + ${startRowIndex}`,
                minWidth: 30,
                width: 30,
                suppressCellSelectionIntegration: true,
            }}
            enableCellSpan={true}
        />
    );
}

export interface HtmlTestData {
  name: string;
  description: string;
  html: string;
}

export const htmlTestData: HtmlTestData[] = [
  {
    name: "Simple Table 1",
    description: "Basic",
    html: `
      <table>
        <tr>
          <td id="sjs-A1">Name</td>
          <td id="sjs-B1">Age</td>
          <td id="sjs-C1">Email</td>
          <td id="sjs-D1">Phone</td>
        </tr>
        <tr>
          <td id="sjs-A2"><PERSON></td>
          <td id="sjs-B2">30</td>
          <td id="sjs-C2"><EMAIL></td>
          <td id="sjs-D2">555-0101</td>
        </tr>
        <tr>
          <td id="sjs-A3"><PERSON></td>
          <td id="sjs-B3">25</td>
          <td id="sjs-C3"><EMAIL></td>
          <td id="sjs-D3">555-0102</td>
        </tr>
      </table>`,
  },
  {
    name: "Table Starting at D5",
    description: "Table with cell addresses starting from D5",
    html: `
      <table>
        <tr>
          <td id="sjs-D5">Product</td>
          <td id="sjs-E5">Category</td>
          <td id="sjs-F5">Price</td>
        </tr>
        <tr>
          <td id="sjs-D6">Laptop</td>
          <td id="sjs-E6">Electronics</td>
          <td id="sjs-F6">$999</td>
        </tr>
        <tr>
          <td id="sjs-D7">Mouse</td>
          <td id="sjs-E7">Electronics</td>
          <td id="sjs-F7">$25</td>
        </tr>
        <tr>
          <td id="sjs-D8">Desk</td>
          <td id="sjs-E8">Furniture</td>
          <td id="sjs-F8">$200</td>
        </tr>
      </table>
    `,
  },
  {
    name: "Colspan Table",
    description: "Table with colspan attribute",
    html: `
      <table>
        <tr>
          <td id="sjs-A1" colspan="2">Header</td>
          <td id="sjs-C1">Other</td>
        </tr>
        <tr>
          <td id="sjs-A2">Cell1</td>
          <td id="sjs-B2">Cell2</td>
          <td id="sjs-C2">Cell3</td>
        </tr>
      </table>
    `,
  },
  {
    name: "Rowspan Table",
    description: "Table with rowspan attribute",
    html: `
      <table>
        <tr>
          <td id="sjs-A1" rowspan="2">Side</td>
          <td id="sjs-B1">Top1</td>
          <td id="sjs-C1">Top2</td>
        </tr>
        <tr>
          <td id="sjs-B2">Bottom1</td>
          <td id="sjs-C2">Bottom2</td>
        </tr>
      </table>
    `,
  },
  {
    name: "Complex Table with Colspan and Rowspan",
    description: "Table with both colspan and rowspan attributes",
    html: `
      <table>
        <tr>
          <th id="sjs-A1" colspan="2">Name</th>
          <th id="sjs-C1" rowspan="2">Age</th>
        </tr>
        <tr>
          <th id="sjs-A2">First</th>
          <th id="sjs-B2">Last</th>
        </tr>
        <tr>
          <td id="sjs-A3">John</td>
          <td id="sjs-B3">Doe</td>
          <td id="sjs-C3">30</td>
        </tr>
      </table>
    `,
  },
  {
    name: "Complex Merged Table",
    description: "Complex table with multiple colspan and rowspan combinations",
    html: `
      <table>
        <tr>
          <td id="sjs-A1" colspan="3" rowspan="2">Main Header</td>
          <td id="sjs-D1">Sub Header 1</td>
          <td id="sjs-E1">Sub Header 2</td>
        </tr>
        <tr>
          <td id="sjs-D2">Data 1</td>
          <td id="sjs-E2">Data 2</td>
        </tr>
        <tr>
          <td id="sjs-A3">Row 1 Col 1</td>
          <td id="sjs-B3">Row 1 Col 2</td>
          <td id="sjs-C3">Row 1 Col 3</td>
          <td id="sjs-D3" rowspan="2">Merged Cell</td>
          <td id="sjs-E3">Row 1 Col 5</td>
        </tr>
        <tr>
          <td id="sjs-A4">Row 2 Col 1</td>
          <td id="sjs-B4">Row 2 Col 2</td>
          <td id="sjs-C4">Row 2 Col 3</td>
          <td id="sjs-E4">Row 2 Col 5</td>
        </tr>
      </table>
    `,
  },
];

export default htmlTestData;

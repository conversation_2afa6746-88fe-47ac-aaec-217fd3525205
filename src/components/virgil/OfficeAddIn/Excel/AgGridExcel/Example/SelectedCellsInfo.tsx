import { Typography } from "~/v2/components/ui";
import { useSelectedCellsContext } from "../SelectedCellsContext";
import { ParsedCell } from "../types";

export const SelectedCellsInfo = () => {
    const { selectedCells } = useSelectedCellsContext();
    return (
        <div>
            <Typography variant="h6">
                <strong>Selected Cells:</strong> {selectedCells.map((cell: ParsedCell) => `${cell.text} (${cell.address})`).join(', ')}
            </Typography>
        </div>
    )
}
import { Typography } from "~/v2/components";
import { SelectedCellsInfo } from "./SelectedCellsInfo";
import { SelectedCellsContextProvider } from "../SelectedCellsContext";
import { AgGridExcelFromHtmlContainer } from "./AgGridExcelFromHtmlContainer";
import { DebugAccordion } from "./DebugAccordion";
import { HtmlTable } from "./HtmlTable";

export const AgGridExcelFromHtmlWithDebug = ({
    html,
    title,
}: {
    html: string;
    title: string;
}) => {
    return (
        <SelectedCellsContextProvider>
            <div className="flex flex-col gap-4 border-2 border-gray-300 p-4 rounded-md">
                <Typography variant="h1">{title}</Typography>
                <HtmlTable html={html} />
                <DebugAccordion html={html} />
                <Typography variant="h3">Ag Grid Excel</Typography>
                <SelectedCellsInfo />
                <AgGridExcelFromHtmlContainer html={html} />
            </div>
        </SelectedCellsContextProvider>
    )
};
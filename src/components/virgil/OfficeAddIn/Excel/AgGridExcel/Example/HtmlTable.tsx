import { Typography } from "~/v2/components";
const injectTableStyles = (html: string): string => {
    const tableStyles = `
      <style>
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th, td {
          border: 1px solid #ccc;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #4a90e2;
          color: white;
          font-weight: bold;
        }
        td:first-child {
          font-weight: bold;
        }
      </style>
    `;

    return tableStyles + html;
};
export const HtmlTable = ({
    html,
}: {
    html: string;
}) => {
    return (
        <div className="flex flex-col gap-4 border-2 border-gray-300 p-4 rounded-md">
            <Typography variant="h3">HTML Table</Typography>
            <div
                dangerouslySetInnerHTML={{ __html: injectTableStyles(html) }}
                style={{
                    maxHeight: "400px",
                    overflowX: "scroll",
                }}
            />
        </div>
    )
};

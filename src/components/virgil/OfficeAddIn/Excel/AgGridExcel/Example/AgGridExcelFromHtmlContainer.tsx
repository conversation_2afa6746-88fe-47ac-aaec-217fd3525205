import { AgGridExcelFromHtml } from "../AgGridExcelFromHtml";
import { useSelectedCellsContext } from "../SelectedCellsContext";
import { useMemo } from "react";
import { useState } from "react";

export const AgGridExcelFromHtmlContainer = ({
    html,
}: {
    html: string;
}) => {
    const { setSelectedCells } = useSelectedCellsContext();
    const memoizedSheet = useMemo(() => {
        return <AgGridExcelFromHtml
            html={html}
            onCellSelectionChange={setSelectedCells}
        />
    }, [html, setSelectedCells]);

    const [height, setHeight] = useState(150);
    const [width, setWidth] = useState(700);
    return (
        <div>
            <div className="flex items-center gap-4 mt-4">
                <div>

                    <label htmlFor="valueRange" className="text-sm font-medium">Adjust Table Height:</label>
                    <input
                        id="valueRange"
                        type="range"
                        min="80"
                        max="300"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        onChange={(e) => setHeight(Number(e.target.value))}
                    />
                    <span className="text-sm">{`Height: ${height}`}</span>
                </div>
                <div>
                    <label htmlFor="valueRange" className="text-sm font-medium">Adjust Table Width:</label>
                    <input
                        id="valueRange"
                        type="range"
                        min="150"
                        max="1000"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        onChange={(e) => setWidth(Number(e.target.value))}
                    />
                    <span className="text-sm">{`Width: ${width}`}</span>
                </div>
            </div>
            <div style={{ height: `${height}px`, width: `${width}px` }}>
                {memoizedSheet}
            </div>
        </div>
    )
}
import { CellDataType, RowData } from "../types";
import { Typography } from "~/v2/components";
import { ColDef } from "ag-grid-enterprise";
import { convert2DArrayToAgGridData } from "../utils/convert2DArrayToAgGridData";
import { convertHtmlTableStringTo2DArray } from "../utils/convertHtmlTableStringTo2DArray";
import { generateColumnDefs } from "../utils/generatedColumnDefs";
import { AccordionContent, AccordionItem, AccordionRoot, AccordionTrigger } from "~/v2/components/ui/Accordion/Accordion";

export const DebugAccordion = ({
    html,
}: {
    html: string;
}) => {
    const cells2DArray: CellDataType[][] = convertHtmlTableStringTo2DArray(html);
    const columDefs: ColDef<RowData>[] = generateColumnDefs(cells2DArray);
    const rowData: RowData[] = convert2DArrayToAgGridData(cells2DArray);
    return (
        <AccordionRoot type="single" collapsible>
            <AccordionItem key="debug" value="debug">
                <AccordionTrigger className="flex items-center gap-x-2">Debug</AccordionTrigger>
                <AccordionContent className="p-4">
                    <Typography variant="h3">Html String</Typography>
                    <pre className="text-wrap whitespace-pre-wrap">{html}</pre>
                    <Typography variant="h3">2D Array</Typography>
                    <pre>{JSON.stringify(cells2DArray, null, 2)}</pre>
                    <Typography variant="h3">Row Data</Typography>
                    <pre>{JSON.stringify(rowData, null, 2)}</pre>
                    <Typography variant="h3">Column Defs</Typography>
                    <pre>{JSON.stringify(columDefs, null, 2)}</pre>
                </AccordionContent>
            </AccordionItem>
        </AccordionRoot>
    )
}

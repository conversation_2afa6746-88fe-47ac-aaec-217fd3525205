import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '~/v2/components/ui/Select';
import { DDQTable, setSelectedTableId } from '~/lib/features/documentDDQSlice';
import { selectSelectedTableId } from '~/lib/features/documentDDQSelector';

const SelectTable: React.FC<{
    tables: DDQTable[];
}> = ({
    tables,
}) => {
        const dispatch = useDispatch();
        const activeTable = useSelector(selectSelectedTableId);

        const handleTableChange = (value: string) => {
            dispatch(setSelectedTableId(value));
        };

        return (
            <Select value={activeTable || ''} onValueChange={handleTableChange}>
                <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a sheet" />
                </SelectTrigger>
                <SelectContent>
                    {tables.map((table: DDQTable) => (
                        <SelectItem key={table.id} value={table.id}>
                            {table.name}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        );
    };

export default SelectTable;

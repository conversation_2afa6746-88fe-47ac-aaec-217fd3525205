"use client";

import { useDDQExcelTablesPerSheet } from "~/components/virgil/hooks/useDDQExcelTablesPerSheet";
import { SelectedCellsContextProvider } from "../AgGridExcel/SelectedCellsContext";
import { TwoColumnDrag } from "~/app/add-in-word-web/TwoColumnDrag";
import TablePreviewLeft from "./TablePreviewLeft";
import TablePreviewRight from "./TablePreviewRight";

export default function TablePreview() {
    const { tables } = useDDQExcelTablesPerSheet();

    return (
        <SelectedCellsContextProvider>
            <TwoColumnDrag
                leftChildren={<TablePreviewLeft tables={tables} />}
                rightChildren={<TablePreviewRight tables={tables} />}
            />
        </SelectedCellsContextProvider>
    )
}

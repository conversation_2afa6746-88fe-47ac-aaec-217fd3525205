"use client";

import { AgGridExcelFromHtml } from "../AgGridExcel/AgGridExcelFromHtml";
import { ErrorBoundary } from 'react-error-boundary';
import { Typography } from "~/v2/components";
import { useSelectedCellsContext } from "../AgGridExcel/SelectedCellsContext";
import { useMemo } from "react";

export default function TableContainer({ htmlTable }: { htmlTable: string }) {
    const { setSelectedCells } = useSelectedCellsContext();

    const memoizedSheet = useMemo(() => {
        return (
            <ErrorBoundary fallbackRender={() => <Typography variant="h4">Error parsing table</Typography>}>
                <AgGridExcelFromHtml html={htmlTable} onCellSelectionChange={setSelectedCells} />
            </ErrorBoundary>
        )
    }, [htmlTable, setSelectedCells]);

    return (
        <div className="h-full w-full overflow-scroll border">
            {memoizedSheet}
        </div>
    );
}

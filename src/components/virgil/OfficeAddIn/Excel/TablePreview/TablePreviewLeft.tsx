"use client";

import { useSelector } from "react-redux";
import { selectSelectedTableId } from "~/lib/features/documentDDQSelector";
import { Typography } from "~/v2/components/ui/Typography";
import { DDQTable } from "~/lib/features/documentDDQSlice";
import TableContainer from "./TableContainer";
import { AgGridExcelFromHtmlWithDebug } from "../AgGridExcel/Example/AgGridExcelFromHtmlWithDebug";
import { useMemo } from "react";

export default function TablePreviewLeft({
    tables,
}: {
    tables: DDQTable[];
}) {
    const selectedTableId = useSelector(selectSelectedTableId);

    const selectedTable = useMemo(() => {
        return tables.find((table) => table.id === selectedTableId);
    }, [tables, selectedTableId]);

    return (
        <div className="flex-1 p-4 w-full h-full flex flex-col min-h-0 overflow-auto">
            <div className="text-lg font-semibold mb-4">
                <Typography variant="h6">
                    {tables.find((table) => table.id === selectedTableId)?.description}
                </Typography>
            </div>
            {selectedTable?.generatedTable && <div>
                <div style={{ height: "300px" }}>
                    <TableContainer
                        htmlTable={selectedTable.generatedTable}
                    />
                </div>
                {/* <AgGridExcelFromHtmlWithDebug html={selectedTable.generatedTable} title="Debug" /> */}
            </div>
            }
        </div>
    )
}

"use client";

import SelectTable from "../SelectTable";
import { useDispatch, useSelector } from "react-redux";
import { selectSelectedTableId } from "~/lib/features/documentDDQSelector";
import { Typography } from "~/v2/components/ui/Typography";
import { Button } from "~/v2/components";
import { DDQTable, setSelectedTableId } from "~/lib/features/documentDDQSlice";
import { useMemo } from "react";
import { useSelectedCellsContext } from "../AgGridExcel/SelectedCellsContext";
import { useDDQExcelGetCellsPerTable } from "~/components/virgil/hooks/useDDQExcelGetCellsPerTable";

export default function TablePreviewRight({
    tables,
}: {
    tables: DDQTable[];
}) {
    const selectedTableId = useSelector(selectSelectedTableId);
    const dispatch = useDispatch();

    // const { cells } = useDDQExcelGetCellsPerTable(selectedTableId);
    const { selectedCells, setSelectedCells } = useSelectedCellsContext();

    // const selectedCellsData = useMemo(() => {
    //     return cells.filter((cell) => selectedCells.some((selectedCell) => selectedCell.address === cell.address));
    // }, [cells, selectedCells]);

    return (
        <div className="bg-gray-100 border-l border-gray-200 h-full">
            <div className="p-4">
                <div className="text-xs text-gray-500">
                    <Typography variant="h6">Select Table</Typography>
                    <SelectTable tables={tables} />
                </div>
                <div className="text-xs text-gray-500">
                    <div className="flex justify-between mt-4">
                        <Button
                            onClick={() => {
                                const currentIndex = tables.findIndex(
                                    (table) => table.id === selectedTableId,
                                );
                                if (currentIndex > 0) {
                                    dispatch(
                                        setSelectedTableId(tables[currentIndex - 1]?.id || ""),
                                    );
                                    setSelectedCells([]);
                                }
                            }}
                            disabled={
                                tables.findIndex((table) => table.id === selectedTableId) ===
                                0
                            }
                        >
                            Previous
                        </Button>
                        <Button
                            onClick={() => {
                                const currentIndex = tables.findIndex(
                                    (table) => table.id === selectedTableId,
                                );
                                if (currentIndex < tables.length - 1) {
                                    dispatch(
                                        setSelectedTableId(tables[currentIndex + 1]?.id || ""),
                                    );
                                    setSelectedCells([]);
                                }
                            }}
                            disabled={
                                tables.findIndex((table) => table.id === selectedTableId) ===
                                tables.length - 1
                            }
                        >
                            Next
                        </Button>
                    </div>
                </div>
                {/* <div className="mt-4">
                    <Typography variant="h3">Debug Info</Typography>
                    <Typography variant="h5">Selected table id: {selectedTableId}</Typography>

                    <Typography variant="h5">Selected Cells:</Typography>
                    <Typography variant="h6">{selectedCells.map((cell) => cell.address).join(", ")}</Typography>
                    <Typography variant="h5">Selected Cells Data:</Typography>
                    <div>
                        {
                            selectedCells.map((cell) => {
                                const cellData = selectedCellsData.find((c) => c.address === cell.address) ?? null;
                                return (
                                    <div key={cell.address}>
                                        <Typography variant="h6">{cell.address}</Typography>
                                        <pre>{JSON.stringify(cellData, null, 2)}</pre>
                                    </div>
                                )
                            })
                        }
                    </div>
                </div> */}
            </div>
        </div>
    )
}

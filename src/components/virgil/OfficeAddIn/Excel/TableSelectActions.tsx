import React from "react";
import { useSelectTableAction } from "../../hooks/useSelectTableAction";
import { useDDQTableSelect } from "../../hooks/useDDQTableSelect";
import { SelectActions } from "../SelectActions";

const TableSelectActions: React.FC<{ className?: string }> = ({ className }) => {
    const { isAllSelected, handleSelectAll, totalSelected, totalCount } = useDDQTableSelect();
    const { SplitButtonMemo } = useSelectTableAction();

    if (totalSelected === 0) {
        return null;
    }

    return (
        <SelectActions
            className={className}
            actionButtonsComponent={SplitButtonMemo}
            isAllSelected={isAllSelected}
            totalSelected={totalSelected}
            totalCount={totalCount}
            handleSelectAll={handleSelectAll}
        />
    )
}

export default TableSelectActions;
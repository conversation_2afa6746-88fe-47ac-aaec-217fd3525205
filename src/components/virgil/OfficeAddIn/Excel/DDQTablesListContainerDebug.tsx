import React from "react";
import { useDDQExcelTablesPerSheet } from "../../hooks/useDDQExcelTablesPerSheet";
import DDQTablesListDebug from "./DDQTablesListDebug";
import SelectSheet from "./SelectSheet";

const DDQTablesListContainerDebug: React.FC = () => {

    const { tables, isLoading } = useDDQExcelTablesPerSheet();

    return (
        <>
            <div className='flex justify-center items-center w-full p-2'>
                <SelectSheet />
            </div>
            <DDQTablesListDebug
                tables={tables}
                noTablesText="No tables found, please re-process document in the Data Room"
                isLoading={isLoading}
            />
        </>
    );
};

export default DDQTablesListContainerDebug;

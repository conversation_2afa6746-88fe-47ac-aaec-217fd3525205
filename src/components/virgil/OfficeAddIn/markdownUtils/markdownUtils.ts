import { renderToString } from "react-dom/server";
import { Markdown } from "../Markdown";
import TurndownService from "turndown";
import React from "react";

export function isMarkdownTable(text: string): boolean {
  // Match table separator row (contains | and -)
  const tableSeparatorPattern = /^\|?\s*[-:|]+[-:|]*\s*\|/m;

  // Match a more complete table pattern (header + separator + data)
  const fullTablePattern = /^\|?.+\|.+\|[\r\n]+\|?\s*[-:|]+[-:|]*\s*\|/m;

  return tableSeparatorPattern.test(text) || fullTablePattern.test(text);
}

export const convertMarkdownToPreview = (
  markdown: string,
  maxLength: number = 150,
): string => {
  return (
    markdown
      // Remove headers
      .replace(/#{1,6}\s+/g, "")
      // Remove bold/italic
      .replace(/\*\*(.+?)\*\*/g, "$1")
      .replace(/\*(.+?)\*/g, "$1")
      // Remove bullet points
      .replace(/^\s*[-*+]\s+/g, "")
      // Remove numbered lists
      .replace(/^\s*\d+\.\s+/g, "")
      // Remove links
      .replace(/\[(.+?)\]\(.+?\)/g, "$1")
      // Remove images
      .replace(/!\[.+?\]\(.+?\)/g, "")
      // Remove blockquotes
      .replace(/^\s*>\s+/g, "")
      // Remove code blocks
      .replace(/```[\s\S]*?```/g, "")
      // Remove inline code
      .replace(/`(.+?)`/g, "$1")
      // Collapse multiple newlines
      .replace(/\n\s*\n/g, " ")
      // Collapse multiple spaces
      .replace(/\s+/g, " ")
      .trim()
      // Truncate to maxLength
      .slice(0, maxLength) +
    (markdown.length > maxLength ? "..." : "")
      // Remove markdown tables
      .replace(/^\|.*\|.*$/gm, "")
      // Remove table separators
      .replace(/^\|[\s-:|]*\|[\s-:|]*$/gm, "")
  );
};

export const convertMarkdownToHtml = (markdown: string): string => {
  return `${renderToString(React.createElement(Markdown, { markdown }))}`;
};

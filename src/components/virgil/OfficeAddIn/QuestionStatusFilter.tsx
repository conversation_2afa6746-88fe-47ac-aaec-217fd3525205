"use client";

import { QuestionStatusType, ResponseStatus } from "@prisma/client";
import { Filter } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import {
  MultiSelect,
  MultiSelectClear,
  MultiSelectContent,
  MultiSelectItem,
  MultiSelectList,
  MultiSelectTrigger,
  MultiSelectValues,
} from "~/v2/components/composit/MultiSelect/MultiSelect";
import { Button } from "~/v2/components/ui/Button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import { Typography } from "~/v2/components/ui/Typography";

import {
  selectSelectedQuestionStatusTypes,
  selectSelectedResponseStatuses,
} from "~/lib/features/documentDDQSelector";
import {
  setSelectedQuestionStatusTypes,
  setSelectedResponseStatuses,
} from "~/lib/features/documentDDQSlice";
import {
  setLocalStorageQuestionStatusTypes,
  setLocalStorageResponseStatuses,
} from "./localStorageClient";

export const QuestionStatusFilter = () => {
  const dispatch = useDispatch();
  const selectedQuestionStatusTypes = useSelector(
    selectSelectedQuestionStatusTypes,
  );
  const selectedResponseStatuses = useSelector(selectSelectedResponseStatuses);
  const [open, setOpen] = useState(false);

  const questionStatusOptions = useMemo(
    () => [
      { value: QuestionStatusType.NEW, label: "New" },
      { value: QuestionStatusType.ANSWERED, label: "Answered" },
    ],
    [],
  );

  const responseStatusOptions = useMemo(
    () => [
      { value: ResponseStatus.DRAFT, label: "Draft" },
      { value: ResponseStatus.PENDING_APPROVAL, label: "Pending Approval" },
      { value: ResponseStatus.APPROVED, label: "Approved" },
      { value: ResponseStatus.REJECTED, label: "Rejected" },
    ],
    [],
  );

  const handleQuestionStatusChange = useCallback(
    (newSelectedQuestionStatusTypes: string[]) => {
      dispatch(
        setSelectedQuestionStatusTypes(
          newSelectedQuestionStatusTypes as QuestionStatusType[],
        ),
      );
    },
    [dispatch],
  );

  const handleResponseStatusChange = useCallback(
    (newSelectedResponseStatuses: string[]) => {
      dispatch(
        setSelectedResponseStatuses(
          newSelectedResponseStatuses as ResponseStatus[],
        ),
      );
    },
    [dispatch],
  );

  const handleClear = useCallback(() => {
    dispatch(setSelectedQuestionStatusTypes([]));
    dispatch(setSelectedResponseStatuses([]));
  }, [dispatch]);

  // persist selectedQuestionStatusTypes and selectedResponseStatuses to localStorage
  useEffect(() => {
    setLocalStorageQuestionStatusTypes(selectedQuestionStatusTypes);
    setLocalStorageResponseStatuses(selectedResponseStatuses);
  }, [selectedQuestionStatusTypes, selectedResponseStatuses]);

  const hasActiveFilters =
    [...selectedQuestionStatusTypes, ...selectedResponseStatuses].length > 0;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={`min-w-0 ${hasActiveFilters ? "text-green-600" : ""}`}
        >
          <Filter className="h-5 w-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-4" align="end">
        <div className="space-y-2">
          <Typography variant="h6" className="font-bold">
            Question Status
          </Typography>
          <MultiSelect
            options={questionStatusOptions}
            selected={selectedQuestionStatusTypes}
            onClose={handleQuestionStatusChange}
          >
            <MultiSelectTrigger>
              <MultiSelectValues placeholder="Select question statuses" />
            </MultiSelectTrigger>
            <MultiSelectContent>
              <MultiSelectList>
                {questionStatusOptions.map((option) => (
                  <MultiSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </MultiSelectItem>
                ))}
                <MultiSelectClear />
              </MultiSelectList>
            </MultiSelectContent>
          </MultiSelect>

          <Typography variant="h6" className="font-bold mt-4">
            Response Status
          </Typography>
          <MultiSelect
            options={responseStatusOptions}
            selected={selectedResponseStatuses}
            onClose={handleResponseStatusChange}
          >
            <MultiSelectTrigger>
              <MultiSelectValues placeholder="Select response statuses" />
            </MultiSelectTrigger>
            <MultiSelectContent>
              <MultiSelectList>
                {responseStatusOptions.map((option) => (
                  <MultiSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </MultiSelectItem>
                ))}
                <MultiSelectClear />
              </MultiSelectList>
            </MultiSelectContent>
          </MultiSelect>

          <div className="flex gap-4 pt-2">
            <Button variant="outline" onClick={handleClear} className="flex-1">
              Clear
            </Button>
            <Button
              variant="default"
              className="flex-1"
              onClick={() => setOpen(false)}
            >
              Close
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

import { useState } from 'react';
import { TextField } from '@mui/material';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useUpdateAddInSettings } from '../../hooks/useUpdateAddInSettings';
import { Button } from '~/v2/components';
import { Typography } from '~/v2/components/ui/Typography';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/v2/components/ui/Select';
import { Switch } from '~/v2/components/ui/switch';

export interface SettingsFormData {
    insertTextColor: string;
    chatHeight: number;
    chatWidth: number;
    fontSize: number;
    bold: boolean;
    italic: boolean;
    underline: boolean;
    sameAsDocument: boolean;
}

const SingleSettingCard = ({
    title,
    inputComponent,
}: {
    title: string;
    inputComponent: React.ReactNode;
}) => {
    return (
        <div className='flex justify-between items-center'>
            <Typography variant="h6">{title}</Typography>
            <div className="ml-auto">
                {inputComponent}
            </div>
        </div>
    );
};

export default function AddInSettings({
    insertTextColor,
    chatHeight,
    chatWidth,
    fontSize,
    bold,
    italic,
    underline,
    sameAsDocument,
}: SettingsFormData) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { mutateAsync: updateAddInSettings } = useUpdateAddInSettings();

    const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<SettingsFormData>({
        defaultValues: {
            chatHeight: chatHeight || 50,
            chatWidth: chatWidth || 50,
            sameAsDocument: sameAsDocument || false,
            fontSize: fontSize || 12,
            bold: bold || false,
            italic: italic || false,
            underline: underline || false,
            insertTextColor: insertTextColor || '#000000',
        }
    });

    const onSubmit = async (data: SettingsFormData) => {
        try {
            setIsSubmitting(true);
            await updateAddInSettings({
                msAddInSettings: {
                    insertTextColor: data.insertTextColor,
                    chatHeight: data.chatHeight,
                    chatWidth: data.chatWidth,
                    fontSize: data.fontSize,
                    bold: data.bold,
                    italic: data.italic,
                    underline: data.underline,
                    sameAsDocument: data.sameAsDocument,
                }
            });
            toast.success('Settings saved successfully');
        } catch (error) {
            toast.error('Failed to save settings');
            console.error('Error saving settings:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <div className='flex flex-col space-y-4 z-10'>
                <SingleSettingCard
                    title="Chat size"
                    inputComponent={
                        <Select
                            value={`${watch('chatHeight')},${watch('chatWidth')}` || ''}
                            onValueChange={(value) => {
                                const [height, width] = value.split(',').map(Number);
                                setValue('chatHeight', height || 50);
                                setValue('chatWidth', width || 50);
                            }}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select chat size" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="30,30">Small</SelectItem>
                                <SelectItem value="50,50">Medium</SelectItem>
                                <SelectItem value="70,70">Large</SelectItem>
                            </SelectContent>
                        </Select>
                    } />

                <div className="relative flex items-center">
                    <div className="flex-grow border-t border-gray-300"></div>
                    <Typography className='px-2' variant="h6">Insert text settings</Typography>
                    <div className="flex-grow border-t border-gray-300"></div>
                </div>
                <div className='space-y-2'>
                    <SingleSettingCard
                        title="Same as document"
                        inputComponent={
                            <Switch
                                className='ml-auto'
                                checked={watch('sameAsDocument')}
                                onCheckedChange={(checked) => {
                                    setValue('sameAsDocument', checked);
                                }}
                            />
                        } />
                    {!watch('sameAsDocument') && (
                        <>
                            <SingleSettingCard
                                title="Font Size"
                                inputComponent={
                                    <Select
                                        value={watch('fontSize').toString() || '12'}
                                        onValueChange={(value) => {
                                            setValue('fontSize', Number(value) || 12);
                                        }}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select Font Size" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="10">X Small (10px)</SelectItem>
                                            <SelectItem value="12">Small (12px)</SelectItem>
                                            <SelectItem value="14">Medium (14px)</SelectItem>
                                            <SelectItem value="16">Large (16px)</SelectItem>
                                            <SelectItem value="18">Extra Large (18px)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                }
                            />

                            <SingleSettingCard
                                title="Bold"
                                inputComponent={
                                    <Switch
                                        className='ml-auto'
                                        checked={watch('bold')}
                                        onCheckedChange={(checked) => {
                                            setValue('bold', checked);
                                        }}
                                    />
                                } />

                            <SingleSettingCard
                                title="Italic"
                                inputComponent={
                                    <Switch
                                        className='ml-auto'
                                        checked={watch('italic')}
                                        onCheckedChange={(checked) => {
                                            setValue('italic', checked);
                                        }}
                                    />
                                } />

                            <SingleSettingCard
                                title="Underline"
                                inputComponent={
                                    <Switch
                                        className='ml-auto'
                                        checked={watch('underline')}
                                        onCheckedChange={(checked) => {
                                            setValue('underline', checked);
                                        }}
                                    />
                                } />

                            <SingleSettingCard
                                title="Insert Text Color"
                                inputComponent={
                                    <TextField
                                        sx={{
                                            width: '150px',
                                        }}
                                        type="color"
                                        {...register('insertTextColor')}
                                        error={!!errors.insertTextColor}
                                        helperText={errors.insertTextColor?.message}
                                    />
                                } />
                        </>)}
                </div>

                <Button
                    type="submit"
                    disabled={isSubmitting}
                // loading={isSubmitting}
                >
                    {isSubmitting ? 'Saving...' : 'Save Settings'}
                </Button>
            </div>
        </form >
    );
}

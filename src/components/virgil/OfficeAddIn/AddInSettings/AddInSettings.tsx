import { useUserMSAddInSettings } from '../../hooks/useUserMSAddInSettings';
import Loading from '~/app/loading';
import AddInSettingsForm from './AddInSettingsForm';

export default function AddInSettings() {
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();
    if (userDataLoading) return <Loading />

    return (
        <div className='p-2'>
            <AddInSettingsForm
                sameAsDocument={msAddInSettings?.sameAsDocument || false}
                insertTextColor={msAddInSettings.insertTextColor || '#000000'}
                chatHeight={msAddInSettings.chatHeight || 40}
                chatWidth={msAddInSettings.chatWidth || 40}
                fontSize={msAddInSettings.fontSize || 12}
                bold={msAddInSettings.bold || false}
                italic={msAddInSettings.italic || false}
                underline={msAddInSettings.underline || false}
            />
        </div>
    );
}

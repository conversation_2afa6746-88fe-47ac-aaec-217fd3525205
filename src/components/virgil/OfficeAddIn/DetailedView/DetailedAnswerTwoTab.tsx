import { useState } from 'react';
import { Tabs, TabsList, TabsTrigger } from "~/v2/components/ui/Tabs";

interface TabPanelProps {
  children?: React.ReactNode;
  value: string;
  index: string;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => {
  return (
    <div role="tabpanel" hidden={value !== index} >
      {value === index && children}
    </div>
  );
};

export type DetailedAnswerProps = {
  tab1Label?: string;
  tab1Component: React.ReactNode;
  tab2Label?: string;
  tab2Component: React.ReactNode;
}
const DetailedAnswerTwoTab: React.FC<DetailedAnswerProps> = ({
  tab1Component,
  tab2Component,
  tab1Label = "Editor",
  tab2Label = "Details",
}) => {
  const [activeTab, setActiveTab] = useState<"tab1" | "tab2">('tab1');

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }}>
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as "tab1" | "tab2")}
        aria-label="detailed answer tabs"
      >
        <TabsList>
          <TabsTrigger value="tab1">
            {tab1Label}
          </TabsTrigger>
          <TabsTrigger value="tab2">
            {tab2Label}
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <TabPanel value={activeTab} index="tab1">
        {tab1Component}
      </TabPanel>
      <TabPanel value={activeTab} index="tab2">
        {tab2Component}
      </TabPanel>
    </div>
  );
};

export default DetailedAnswerTwoTab;

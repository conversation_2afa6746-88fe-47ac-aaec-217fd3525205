
import { useSelector } from "react-redux";
import {
  selectSelectedQuestionId,
  selectSelectedQuestionText,
} from "~/lib/features/documentDDQSelector";
import DetailedAnswerTwoTab from "./DetailedAnswerTwoTab";
import DetailsTabContentGenerated from "./DetailsTabContentGenerated";
import { PreviewTabContent } from "./PreviewTabContent";
import { useGetQuestionWithFeedback } from "../../hooks/useGetQuestionWithFeedback";
import { PreviewTabContentLoading } from "./PreviewTabContentLoading";
import { Skeleton } from "@mui/material";

const DetailedAnswerContainer: React.FC = () => {
  const selectedQuestionId = useSelector(selectSelectedQuestionId);
  const selectedQuestionText = useSelector(selectSelectedQuestionText);

  const { questionWithFeedback, isLoading: isLoadingQuestion } = useGetQuestionWithFeedback({ questionId: selectedQuestionId });

  if (isLoadingQuestion || !questionWithFeedback) {
    return (
      <DetailedAnswerTwoTab
        tab1Label="Editor"
        tab1Component={
          <PreviewTabContentLoading questionText={selectedQuestionText ?? ""} />
        }
        tab2Label="Details"
        tab2Component={
          <Skeleton sx={{ height: 7 }} />
        }
      />
    );
  }

  return (
    <DetailedAnswerTwoTab
      tab1Label="Editor"
      tab1Component={
        <PreviewTabContent question={questionWithFeedback} />
      }
      tab2Label="Details"
      tab2Component={
        <DetailsTabContentGenerated
          question={questionWithFeedback}
        />
      }
    />
  );
};

export default DetailedAnswerContainer;

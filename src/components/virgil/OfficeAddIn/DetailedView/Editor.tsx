import {
  <PERSON>,
  Button,
  CircularProgress,
  Stack,
  Typography,
} from "@mui/material";
import { AnswerGenerationType, type ResponseStatus } from "@prisma/client";
import * as Sentry from "@sentry/nextjs";
import { useCallback, useEffect, useMemo, useState } from "react";
import { AIGeneratedResponseLabel } from "../../Labels/AIGeneratedResponseLabel";
import { ResponseLibraryResponseLabel } from "../../Labels/ResponseLibraryResponseLabel";
import ResponseStatusLabel from "../../Response/ResponseStatusLabel";
import Wysiwyg from "../../Wysiwyg/Wysiwyg";
import { useCreateOrUpdateResponse } from "../../hooks/useCreateOrUpdateResponse";
import { CircleLoading } from "../CircleLoading";
import { Markdown } from "../Markdown";
import Reason from "../Reason";
import { FeedbackButtons } from "../FeedbackButtons";
import SplitButtonWithDefault from "../SplitButtonWithDefault";
import { AggregatedFeedback } from "~/server/api/managers/feedbackManager";
import { ActionButton } from "~/v2/components/composit/ActionButton";
const MarkdownWithLoading = ({
  markdown,
  fallbackText,
  isLoading,
}: {
  markdown: string;
  fallbackText: string;
  isLoading: boolean;
}) => {
  if (isLoading) {
    return (
      <Typography variant="body2" color="text.secondary">
        {fallbackText}
        <CircleLoading size={16} />
      </Typography>
    );
  }

  return <Markdown markdown={markdown} className="add-in" />;
};

type EditorProps = {
  questionText: string;
  questionId: string;
  handleInsert: (insertype: "AfterQuestion" | "AtCursor") => void;
  // similar response
  similarResponseId: string;
  similarResponseText: string;
  isSimilaritySearchLoading: boolean;
  // rag response
  ragResponseText: string;
  isRagResponseLoading: boolean;
  similarResponseStatus: ResponseStatus | null;
  answerGenerationType: AnswerGenerationType;
  reason?: string;
  responseIdCanonical: string;
  feedback: AggregatedFeedback | null;
};
const Editor = ({
  questionText,
  questionId,
  handleInsert,
  feedback,
  similarResponseId,
  similarResponseText,
  isSimilaritySearchLoading,
  ragResponseText,
  isRagResponseLoading,
  similarResponseStatus,
  answerGenerationType,
  reason,
  responseIdCanonical,
}: EditorProps) => {
  const [mode, setMode] = useState<"view" | "edit">("view");

  const { editorStateRef, createOrUpdateResponse, submitting } =
    useCreateOrUpdateResponse({
      questionId,
      questionText,
      onSuccess: () => {
        setMode("view");
      },
    });

  const SourceResponseLabel = useMemo(() => {
    if (answerGenerationType === AnswerGenerationType.EXTRACTED) {
      return <ResponseLibraryResponseLabel />
    }
    if (answerGenerationType === AnswerGenerationType.GENERATED) {
      return <AIGeneratedResponseLabel />
    }

    return null;
  }, [
    isSimilaritySearchLoading,
    isRagResponseLoading,
    similarResponseText,
    ragResponseText,
  ]);

  const handleSave = useCallback(() => {
    createOrUpdateResponse(similarResponseId ?? null);
  }, [similarResponseId, createOrUpdateResponse]);

  return (
    <>
      <Box
        sx={{ height: "calc(100vh - 55px - 50px - 70px)", overflow: "auto" }}
      >
        <Box sx={{ p: 1 }}>
          <Typography variant="h6">{questionText}</Typography>
        </Box>
        <Box sx={{ p: 1 }}>
          {mode === "view" ? (
            <Box
              sx={{
                border: "1px solid #ddd",
                backgroundColor: "#F4F4F6",
                borderRadius: 1,
                p: 1,
              }}
            >
              <Stack direction="row" alignItems="left" spacing={1}>
                {SourceResponseLabel}
                {similarResponseStatus && (
                  <ResponseStatusLabel responseStatus={similarResponseStatus} />
                )}
              </Stack>
              <MarkdownWithLoading
                markdown={similarResponseText ?? ""}
                fallbackText="Fetching from response library..."
                isLoading={isSimilaritySearchLoading}
              />
              <MarkdownWithLoading
                markdown={ragResponseText ?? ""}
                fallbackText="Generating with LLM..."
                isLoading={isRagResponseLoading}
              />
            </Box>
          ) : submitting ? (
            <CircleLoading size={16} />
          ) : (
            <Box
              sx={{
                border: "1px solid #ddd",
                backgroundColor: "#F4F4F6",
                borderRadius: 1,
                p: 0,
              }}
            >
              <Wysiwyg
                markdownString={similarResponseText || ragResponseText || ""}
                editorStateRef={editorStateRef}
                editorConentClass="add-in markdown-body"
              />
            </Box>
          )}
        </Box>
        {reason && <Box sx={{ p: 1 }}>
          <Reason reason={reason} />
        </Box>}
      </Box>
      <Box
        sx={{
          mt: 2,
          position: "sticky",
          bottom: 0,
          bgcolor: "background.paper",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
          height: "70px",
          display: "flex",
          gap: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {mode === "view" ? (
          <ActionButton
            options={[
              { label: "Insert", action: () => handleInsert("AtCursor") },
              { label: "Insert after", action: () => handleInsert("AfterQuestion") },
            ]}
            variant="default"
          />
        ) : (
          <>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSave}
              disabled={submitting}
              startIcon={submitting ? <CircularProgress size={20} /> : null}
            >
              Save
            </Button>
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              onClick={() => setMode("view")}
              disabled={submitting}
            >
              Cancel
            </Button>
          </>
        )}
      </Box>
    </>
  );
};

export default Editor;

import { DDQuestionWithFeedback, DDQuestionWithIndexAndFeedback } from "~/server/api/managers/questionManager";
import parseQuestionData from "../IndexView/SingleDDQWithData/parseQuestionData";
import useActionButtons from "../IndexView/SingleDDQWithData/useActionButtons";
import Reason from "../Reason";
import { ResponseDetails } from "../../Response/ResponseDetails";
import { Typography } from "~/v2/components";

type PreviewProps = {
    question: DDQuestionWithIndexAndFeedback | DDQuestionWithFeedback;
}

export const PreviewTabContent = ({ question }: PreviewProps) => {
    const { ActionButtonsMemo, isGenerating } = useActionButtons({ question, view: "detailed" });
    const { responseReason, responseStatus, responseText, answerGenerationType, insufficientData, isAnswered } = parseQuestionData(question);
    return <>
        <div className="h-[calc(100vh-55px-50px-45px)] overflow-auto p-1 space-y-1">
            <Typography variant="boldBody">{question.text}</Typography>
            <ResponseDetails
                responseReason={responseReason}
                responseStatus={responseStatus}
                responseText={responseText}
                answerGenerationType={answerGenerationType}
                insufficientData={insufficientData}
                isAnswered={isAnswered}
                isGenerating={isGenerating}
                displayAnsweredStatus={true}
            />
            {responseReason && <Reason reason={responseReason} />}
        </div>
        <div
            style={{
                marginTop: 2,
                position: "sticky",
                bottom: 0,
                backgroundColor: "background.paper",
                padding: 2,
                borderTop: 1,
                borderColor: "divider",
                height: "70px",
                display: "flex",
                gap: 1,
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            {ActionButtonsMemo}
        </div>
    </>
};
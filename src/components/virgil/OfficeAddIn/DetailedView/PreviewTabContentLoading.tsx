import { Typography } from "~/v2/components";
import { Skeleton } from "~/v2/components/ui/Skeleton";


type PreviewProps = {
    questionText?: string;
}

export const PreviewTabContentLoading = ({ questionText }: PreviewProps) => {
    return (
        <div
            className="h-[calc(100vh-55px-50px-85px)] overflow-auto"
        >
            <div className="p-1">
                <Typography
                    variant="boldBody"
                >
                    {questionText}
                </Typography>
                <Skeleton className="h-4" />
                <Skeleton className="h-4" />
            </div>
        </div>
    );
};
import {
  Box,
  Divider,
  Card,
  Tooltip,
  Typography,
  Chip,
  Stack,
} from "@mui/material";
import { FileThumbnail } from "~/components/file-thumbnail";
import { GetDocumentType } from "~/server/api/routers/question";
import { Label } from "~/components/label";
import { Category, Fund, Tag } from "@prisma/client";
import { FundCollection } from "~/views/data-room/FileExplorer";
import React from "react";
import { getFilename } from "~/v2/lib/utils";

type SourceDocumentsProps = {
  documents: GetDocumentType[];
};
export const SourceDocuments = ({ documents }: SourceDocumentsProps) => {
  return (
    <Stack direction="column" spacing={2}>
      <Typography variant="h4">
        Source Documents ({documents.length})
      </Typography>
      {documents.map((document) => (
        <SourceDocument key={document.id} document={document} />
      ))}
    </Stack>
  );
};

const SourceDocument = ({ document }: { document: GetDocumentType }) => {
  const baseName = getFilename(document.name);

  return (
    <Card sx={{ p: 2 }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          gap: 5,
          width: "100%",
        }}
      >
        <FileThumbnail
          file={document.name}
          sx={{ width: 20, height: 20 }}
          slotProps={{
            icon: {
              color: "primary.main",
            },
          }}
        />
        <Tooltip title={document.name}>
          <Typography noWrap variant="subtitle2">
            {baseName}
          </Typography>
        </Tooltip>
      </div>
      <Divider sx={{ my: 2 }} />
      <Typography variant="subtitle2">Funds:</Typography>
      <FundCollection funds={document.funds} />
      <Divider sx={{ my: 2 }} />
      <Typography variant="subtitle2">Tags:</Typography>
      <TagCollection tags={document.tags} />
      <Divider sx={{ my: 2 }} />
      <Typography variant="subtitle2">Categories:</Typography>
      <CategoryCollection categories={document.Categories} />
    </Card>
  );
};

function TagCollection(props: { tags: Tag[] }) {
  return (
    <Box
      display="flex"
      gap={1}
      justifyContent="flex-start"
      flexDirection="row"
      alignItems="center"
      height="100%"
      width="100%"
      pt={1}
      sx={{
        cursor: "pointer",
      }}
    >
      <Tooltip
        title={
          <React.Fragment>
            <Box gap={1} display="flex" flexDirection="column">
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                {props.tags[0]?.name}
              </Typography>
              {props.tags[0]?.summary}
            </Box>
          </React.Fragment>
        }
      >
        {props.tags[0]?.name ? (
          <Chip
            sx={{ width: 170, backgroundColor: "primary.main", color: "white" }}
            size="small"
            variant="filled"
            label={props.tags[0]?.name}
          />
        ) : (
          <Typography variant="caption" sx={{ fontWeight: "bold" }}>
            No tags
          </Typography>
        )}
      </Tooltip>
      {props.tags.length > 1 && (
        <Tooltip
          title={
            <React.Fragment>
              {props.tags.slice(1).map((t: Tag) => (
                <Box
                  key={t.id}
                  gap={1}
                  display="flex"
                  flexDirection="column"
                  sx={{ mb: 1 }}
                >
                  <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                    {t.name}
                  </Typography>
                  <Typography variant="caption">{t.summary}</Typography>
                </Box>
              ))}
            </React.Fragment>
          }
        >
          <Chip
            sx={{ width: 35 }}
            size="small"
            variant="soft"
            label={`+${props.tags.length - 1}`}
          />
        </Tooltip>
      )}
    </Box>
  );
}

function CategoryCollection(props: { categories: Category[] }) {
  return (
    <Box
      display="flex"
      gap={1}
      justifyContent="flex-start"
      flexDirection="row"
      alignItems="center"
      height="100%"
      width="100%"
      pt={1}
      sx={{
        cursor: "pointer",
      }}
    >
      <Tooltip
        title={
          <React.Fragment>
            <Box gap={1} display="flex" flexDirection="column">
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                {props.categories[0]?.name}
              </Typography>
              {props.categories[0]?.description}
            </Box>
          </React.Fragment>
        }
      >
        {props.categories[0]?.name ? (
          <Chip
            sx={{ width: 170, backgroundColor: "primary.main", color: "white" }}
            size="small"
            variant="filled"
            label={props.categories[0]?.name}
          />
        ) : (
          <Typography variant="caption" sx={{ fontWeight: "bold" }}>
            No categories
          </Typography>
        )}
      </Tooltip>
      {props.categories.length > 1 && (
        <Tooltip
          title={
            <React.Fragment>
              {props.categories.slice(1).map((t: Category) => (
                <Box
                  key={t.id}
                  gap={1}
                  display="flex"
                  flexDirection="column"
                  sx={{ mb: 1 }}
                >
                  <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                    {t.name}
                  </Typography>
                  <Typography variant="caption">{t.description}</Typography>
                </Box>
              ))}
            </React.Fragment>
          }
        >
          <Chip
            sx={{ width: 35 }}
            size="small"
            variant="soft"
            label={`+${props.categories.length - 1}`}
          />
        </Tooltip>
      )}
    </Box>
  );
}

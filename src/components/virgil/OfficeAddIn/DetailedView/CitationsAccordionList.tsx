import { Citation } from "~/lib/types";
import DocumentAccordion from "../../DocumentAccordion/DocumentAccordion";
import { useState } from "react";
import { Button, Typography } from "~/v2/components";

const MAX_CITATIONS_IN_PREVIEW = 3;

export const CitationsAccordionList = ({
  citations,
}: {
  citations: Citation[];
}) => {
  const [viewAll, setViewAll] = useState(false);
  const displayedCitations = viewAll
    ? citations
    : citations.slice(0, MAX_CITATIONS_IN_PREVIEW);

  return (
    <div className="p-1 overflow-auto">
      <div className="flex justify-between items-center mb-1">
        <Typography variant="h6" className="mb-1">
          Citations ({citations.length})
        </Typography>
        {citations.length > MAX_CITATIONS_IN_PREVIEW && (
          <Button
            variant="link"
            onClick={() => setViewAll((viewAll) => !viewAll)}
          >
            {viewAll ? "View Less" : "View All"}
          </Button>
        )}
      </div>
      {displayedCitations.map((citation, idx) => (
        <DocumentAccordion
          key={idx}
          fileName={citation?.fileName?.split("/").at(-1) ?? ""}
          content={citation?.quote ?? ""}
          sourceLink={""}
          pageNumber={citation?.metadata?.page_number ?? 0}
          documentId={citation?.documentId ?? ""}
        />
      ))}
    </div>
  );
};

import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import { forwardRef } from "react"
import { Typography } from "~/v2/components";

interface MarkdownProps {
    markdown: string;
    className?: string;
    onClick?: (e: React.MouseEvent<HTMLElement>) => void;
    onMouseEnter?: (e: React.MouseEvent<HTMLElement>) => void;
    onMouseLeave?: (e: React.MouseEvent<HTMLElement>) => void;
}

export const Markdown = forwardRef<HTMLDivElement, MarkdownProps>((props, ref) => {
    const { markdown, className, onClick, onMouseEnter, onMouseLeave, ...rest } = props;
    return (
        <div ref={ref} onClick={onClick} onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} className={`markdown-body${className ? ` ${className}` : ""}`} {...rest}>
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    table: ({ node, ...props }) => (
                        <div className="table-wrapper text-xs">
                            <table {...props} />
                        </div>
                    ),
                    h1: ({ node, ...props }) => (
                        <Typography variant="markdownH1" {...props} />
                    ),
                    h2: ({ node, ...props }) => (
                        <Typography variant="markdownH2" {...props} />
                    ),
                    h3: ({ node, ...props }) => (
                        <Typography variant="markdownH2" {...props} />
                    ),
                    h4: ({ node, ...props }) => (
                        <Typography variant="markdownH2" {...props} />
                    ),
                    h5: ({ node, ...props }) => (
                        <Typography variant="markdownH2" {...props} />
                    ),
                    h6: ({ node, ...props }) => (
                        <Typography variant="markdownH2" {...props} />
                    ),
                    p: ({ node, ...props }) => (
                        <Typography variant="body">{props.children}</Typography>
                    ),
                }}
            >
                {markdown}
            </ReactMarkdown>
        </div>
    )
})

import React from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import useDocumentStatus from './useDocumentStatus';

export default function DocumentStatus() {
    const { color, fullTextStatus, shortStatusText } = useDocumentStatus();
    return (
        <Box display="flex" alignItems="center" gap={1} justifyContent="center">
            <Tooltip title={fullTextStatus}>
                <div
                    style={{
                        backgroundColor: color,
                        width: 8,
                        height: 8,
                        borderRadius: '50%'
                    }}
                />
            </Tooltip>
            <Typography variant="body2" color="text.secondary">{shortStatusText}</Typography>
        </Box>
    );
}

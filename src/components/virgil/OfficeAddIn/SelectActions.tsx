import { useMemo } from "react";
import { Checkbox, Typography } from "~/v2/components";
import { cn } from "~/v2/lib/utils";

export type SelectActionsProps = {
    className?: string;
    actionButtonsComponent: React.ReactNode;
    isAllSelected: boolean;
    totalSelected: number;
    totalCount: number;
    handleSelectAll: () => void;
}
export const SelectActions: React.FC<SelectActionsProps> = ({
    className,
    actionButtonsComponent,
    isAllSelected,
    totalSelected,
    totalCount,
    handleSelectAll,
}) => {
    const checkbox = useMemo(() => {
        let checked: boolean | "indeterminate" | undefined = isAllSelected;
        if (totalSelected > 0 && totalSelected < totalCount) {
            checked = "indeterminate";
        }
        return <Checkbox
            checked={checked}
            onCheckedChange={() => { handleSelectAll() }}
            onClick={(e) => e.stopPropagation()}
        />
    }, [isAllSelected, handleSelectAll, totalSelected, totalCount])
    return (
        <div
            className={
                cn("flex justify-between items-center w-full p-2 py-1 bg-white", className)
            }
            style={{
                height: '50px',
                boxShadow: '0 15px 15px -5px var(--shadow-color)',
            }}
        >
            <div className="flex items-center gap-1">
                {checkbox}
                <Typography variant="body">{totalSelected} of {totalCount} selected
                    <span className="underline cursor-pointer ml-1" onClick={() => { handleSelectAll() }}>({isAllSelected ? "Deselect all" : "Select all"})</span>
                </Typography>
            </div>
            <div className="flex items-center gap-1">
                {actionButtonsComponent}
            </div>
        </div>
    )
}

import { highlightGeneratedCells } from "./highlightGeneratedCells";

describe("highlightGeneratedCells", () => {
  it('should add "generated" class to cells with different content', async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td colspan="8">Is there an ESG Committee or a formal oversight structure in place implementing ESG integration? If yes, list the people involved and their roles.</td>
          </tr>
          <tr>
            <td id="sjs-C30" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td colspan="8">Is there an ESG Committee or a formal oversight structure in place implementing ESG integration? If yes, list the people involved and their roles.</td>
          </tr>
          <tr>
            <td id="sjs-C30" colspan="8">Yes, we have established a formal ESG oversight structure to implement ESG integration across our investment process and portfolio management activities.</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
    expect(result).toContain(
      "Yes, we have established a formal ESG oversight structure",
    );
  });

  it('should not add "generated" class to cells with same content', async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Same content</td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Same content</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).not.toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it('should preserve existing classes when adding "generated"', async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" class="existing-class" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" class="existing-class" colspan="8">New content</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="existing-class generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it("should handle multiple cells with different content", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8"></td>
          </tr>
          <tr>
            <td id="sjs-C31" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Content 1</td>
          </tr>
          <tr>
            <td id="sjs-C31" colspan="8">Content 2</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('id="sjs-C30"');
    expect(result).toContain('id="sjs-C31"');
    expect(result.match(/class="generated"/g)).toHaveLength(2);
  });

  it("should handle cells without IDs", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td colspan="8">Question</td>
          </tr>
          <tr>
            <td id="sjs-C30" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td colspan="8">Question</td>
          </tr>
          <tr>
            <td id="sjs-C30" colspan="8">Answer</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it("should handle whitespace differences", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">  Content with spaces  </td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Content with spaces</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    // Should not add generated class since content is the same after trimming
    expect(result).not.toContain('class="generated"');
  });

  it("should handle complex HTML content with tags", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">
              Yes, we have established a formal ESG oversight structure.<br><br>
              <strong>ESG Committee:</strong><br>
              • <strong>James Gallatin</strong> - Managing Partner and Co-Founder<br>
              • <strong>Sarah Chen</strong> - Partner, Healthcare Practice Lead
            </td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="generated"');
    expect(result).toContain("<strong>ESG Committee:</strong>");
    expect(result).toContain("<strong>James Gallatin</strong>");
  });

  it("should handle malformed HTML gracefully", async () => {
    const extractedHtml = '<table><td id="sjs-C30">Content</td></table>';
    const generatedHtml =
      '<table><td id="sjs-C30">Different content</td></table>';

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it("should handle cells with special characters in content", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Content with "quotes" & <tags></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Different content with "quotes" & <tags></td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it("should handle cells with multiple class attributes", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" class="class1 class2" colspan="8"></td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" class="class1 class2" colspan="8">New content</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).toContain('class="class1 class2 generated"');
    expect(result).toContain('id="sjs-C30"');
  });

  it("should handle cells with no content difference but whitespace difference", async () => {
    const extractedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Content</td>
          </tr>
        </tbody>
      </table>
    `;

    const generatedHtml = `
      <table>
        <tbody>
          <tr>
            <td id="sjs-C30" colspan="8">Content</td>
          </tr>
        </tbody>
      </table>
    `;

    const result = highlightGeneratedCells(extractedHtml, generatedHtml);

    expect(result).not.toContain('class="generated"');
    expect(result).toContain('id="sjs-C30"');
  });
});

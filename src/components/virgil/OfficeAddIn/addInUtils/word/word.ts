import { convertMarkdownToHtml } from "../../markdownUtils/markdownUtils";
import { eventBus } from "~/lib/eventBus";
import { FullOptions, Searcher } from "fast-fuzzy";
import { debounce } from "@mui/material";
import { parseHtmlToTree } from "../transformerUtils";
import { insertMarkdown } from "./wordInsert";

const STRING_LOOKUP_SIZE = 254; // search limit is 255 characters in MS Word
const WORD_SOFT_BREAK = String.fromCharCode(11);
let searchParagraphs: Searcher<
  { text: string; id: string },
  FullOptions<{ text: string; id: string }>
> = new Searcher<
  { text: string; id: string },
  FullOptions<{ text: string; id: string }>
>([]);
export type WordTextSelectionData = { text: string; paragraph: string };
async function registerTextSelectionHandler(
  hostType: Office.HostType,
  onSelectionChange?: (data: WordTextSelectionData) => void,
) {
  if (hostType === Office.HostType.Word) {
    // Add event handler for selection changes
    Office.context.document.addHandlerAsync(
      Office.EventType.DocumentSelectionChanged,
      async () => {
        // Get the current selection
        Office.context.document.getSelectedDataAsync(
          Office.CoercionType.Text,
          async (result: Office.AsyncResult<string>) => {
            const handleSelectionChange = async (
              result: Office.AsyncResult<string>,
            ) => {
              if (result.status === Office.AsyncResultStatus.Succeeded) {
                const selectedText = result.value;

                await Word.run(async (context) => {
                  // Get the current selection
                  const range = context.document.getSelection();

                  // Get the paragraph containing the selection
                  const paragraph = range.paragraphs.getFirst();

                  // Load the text property
                  paragraph.load("text");
                  await context.sync();

                  // Call the callback with the selection data
                  if (onSelectionChange) {
                    onSelectionChange({
                      text: selectedText,
                      paragraph: paragraph.text,
                    });
                  }
                });
              }
            };
            handleSelectionChange(result);
          },
        );
      },
    );
  }
}
const debouncedBuildSearchParagraphs = debounce(buildSearchParagraphs, 200);

async function buildSearchParagraphs() {
  await Word.run(async (context) => {
    const paragraphs = context.document.body.paragraphs;
    paragraphs.load(["text", "uniqueLocalId"]);
    await context.sync();

    const p = paragraphs.items
      .map((paragraph: Word.Paragraph) => {
        return {
          text: paragraph.text,
          id: paragraph.uniqueLocalId,
        };
      })
      .filter((p) => p.text.trim().length > 0);
    searchParagraphs = new Searcher(p, { keySelector: (obj) => obj.text });
  });
}

async function registerBodySearchHandler(hostType: Office.HostType) {
  if (hostType === Office.HostType.Word) {
    await buildSearchParagraphs();
  }
}

async function highlightTextInDocument(
  text: string,
  highlightColor = "#FFFF00",
) {
  await Word.run(async (context) => {
    const searchResults = context.document.body.search(prepareSearchStr(text), {
      matchWildcards: true,
      ignoreSpace: true,
      matchCase: false,
      matchWholeWord: false,
      ignorePunct: true,
    });

    searchResults.load(["font", "range"]);
    await context.sync();

    // Highlight the found text
    searchResults.items.forEach((item) => {
      item.font.highlightColor = highlightColor;
    });

    await context.sync();
    return searchResults;
  });
}

// Take the first half of the string and the last half of the string and join them with a wildcard
export function prepareSearchStr(str: string) {
  if (str.length <= STRING_LOOKUP_SIZE) {
    return str;
  }

  const firstHalf = str
    .substring(0, Math.floor(str.length / 2))
    .substring(0, STRING_LOOKUP_SIZE / 2);
  const secondHalf = str
    .substring(str.length / 2)
    .substring(str.length / 2 - STRING_LOOKUP_SIZE / 2);

  return `${firstHalf}*${secondHalf}`;
}

function findRangeSync(
  text: string,
  context: Word.RequestContext,
): Word.RangeCollection {
  const searchResults = context.document.body.search(prepareSearchStr(text), {
    ignoreSpace: true,
    matchWildcards: true,
    matchCase: false,
    matchWholeWord: false,
    ignorePunct: true,
  });

  searchResults.load(["range", "text", "font"]);
  return searchResults;
}

// Lookup substrings of 254 characters and join them together
async function findRange(
  text: string,
  context: Word.RequestContext,
  position: "Start" | "End" = "Start",
): Promise<Word.Range | undefined> {
  // const searchResults = findRangeSync(text, context, position);

  // use fuzzy search to find the text
  const searchResults = searchParagraphs.search(text);
  if (searchResults.length > 0 && searchResults[0]?.id) {
    const paragraphId = searchResults[0]?.id;
    const paragraph: Word.Paragraph =
      context.document.getParagraphByUniqueLocalId(paragraphId);
    paragraph.load();
    await context.sync();
    return paragraph.getRange();
  }
}

async function scrollToText(
  text: string,
  mode: "Select" | "Start" | "End" = "Select",
) {
  await Word.run(async (context) => {
    const range = await findRange(text, context, "Start");
    if (!range) {
      // throw new Error("Text not found");
      return;
    }
    range.select();
    await context.sync();
  });
}

async function scrollToTextFuzzy(text: string) {
  await Word.run(async (context) => {
    const searchResults = searchParagraphs.search(text);
    if (searchResults.length > 0 && searchResults[0]?.id) {
      const paragraphId = searchResults[0]?.id;
      const paragraph: Word.Paragraph =
        context.document.getParagraphByUniqueLocalId(paragraphId);
      paragraph.load();
      paragraph.select();
      await context.sync();
      return;
    }
  });
}

export type WordInsertOptions =
  | "AfterQuestion"
  | "AtCursor"
  | "CellToRight"
  | "CellBelow";

export async function findRangeBasedOnInsertType(
  text: string,
  context: Word.RequestContext,
  insertType: WordInsertOptions,
) {
  switch (insertType) {
    case "AfterQuestion":
      return await findRange(text, context, "End");
    case "AtCursor":
      return context.document.getSelection();
    case "CellToRight":
      const range = await findRange(text, context, "End");
      if (!range) return undefined;
    case "CellBelow":
      return await findRange(text, context, "End");
  }
}

// Insert the text after the found text
async function findAndInsertAfter(
  text: string,
  insertText: string,
  color = "green",
  fontSize = 12,
  bold = false,
  italic = false,
  underline = false,
  applyTextStyle = true,
  insertAt: WordInsertOptions = "AfterQuestion",
  cellAddress?: { rowIndex: number; colIndex: number },
) {
  await Word.run(async (context) => {
    const range = await (async () => {
      switch (insertAt) {
        case "AfterQuestion":
          return await findRange(text, context, "End");
        case "AtCursor":
          return context.document.getSelection();
        case "CellToRight":
        case "CellBelow":
          if (!cellAddress) {
            throw new Error("Cell address is required");
          }

          const r = await findRange(text, context, "End");
          if (!r) return undefined;
          r.load(["parentTableOrNullObject"]);
          await context.sync();
          const parentTable = r.parentTableOrNullObject;
          if (!parentTable) return undefined;
          const tableCell = parentTable.getCellOrNullObject(
            cellAddress?.rowIndex,
            cellAddress?.colIndex,
          );
          if (!tableCell) return undefined;
          return tableCell.body.getRange("Whole");
      }
    })();
    await context.sync();

    if (range) {
      if (insertAt === "AfterQuestion") {
        range.insertText(WORD_SOFT_BREAK, "End");
      }

      await insertMarkdown(range, insertText, {
        applyTextStyle,
        color,
        fontSize,
        bold,
        italic,
        underline,
      });
      await context.sync();
    } else {
      throw new Error("Text not found");
    }
  });
}
// right and below are the most typical insert options
export type TableInsertOption = {
  type: "CellToRight" | "CellBelow";
  address: {
    rowIndex: number;
    colIndex: number;
  };
};

export async function getTableInsertOptions(
  text: string,
): Promise<TableInsertOption[]> {
  return await Word.run(async (context) => {
    const range = await findRange(text, context, "Start");
    if (!range) {
      return [];
    }
    range.load(["parentTableCellOrNullObject", "parentTableOrNullObject"]);
    await context.sync();

    const parentTableCell: Word.TableCell = range.parentTableCellOrNullObject;
    const parentTable: Word.Table = range.parentTableOrNullObject;
    const availableOptions: TableInsertOption[] = [];
    if (parentTableCell && parentTable) {
      parentTableCell.load(["cellIndex", "rowIndex"]);
      await context.sync();
      const colIndex = parentTableCell.cellIndex;
      const rowIndex = parentTableCell.rowIndex;
      const tableCellToRight = parentTable.getCellOrNullObject(
        rowIndex,
        colIndex + 1,
      );
      // check insert to the right
      if (tableCellToRight && rowIndex >= 0 && colIndex + 1 >= 0) {
        availableOptions.push({
          type: "CellToRight",
          address: {
            rowIndex,
            colIndex: colIndex + 1,
          },
        });
      }
      const tableCellBelow = parentTable.getCellOrNullObject(
        rowIndex + 1,
        colIndex,
      );
      // check insert below
      if (tableCellBelow && rowIndex + 1 >= 0 && colIndex >= 0) {
        availableOptions.push({
          type: "CellBelow",
          address: {
            rowIndex: rowIndex + 1,
            colIndex,
          },
        });
      }
    }
    return availableOptions;
  });
}

// Insert the text after the found text
async function findAndInsertAfterBatch(
  pairs: { text: string; insertText: string; id: string }[],
  color = "green",
): Promise<{ success: number; failed: number; insertedQuestionIds: string[] }> {
  return await Word.run(async (context) => {
    // search in batch
    const searches = pairs.map((pair) => {
      const range = findRangeSync(pair.text, context);
      return {
        searchResults: range,
        id: pair.id,
        insertText: pair.insertText,
        text: pair.text,
      };
    });

    await context.sync();

    // get ranges
    const ranges: {
      range: Word.Range;
      id: string;
      insertText: string;
      text: string;
    }[] = searches
      .map((search) => {
        const range = search.searchResults.items[0]?.getRange();
        if (range) {
          return {
            range,
            id: search.id,
            insertText: search.insertText,
            text: search.text,
          };
        }
        return undefined;
      })
      .filter((r) => !!r);

    // load font for each range
    ranges.forEach((r) => {
      r.range.load("font");
    });

    await context.sync();

    // insert text
    ranges.forEach((r) => {
      const html = convertMarkdownToHtml(r.insertText);
      const tree = parseHtmlToTree(html);
      // TODO: insert nodes
    });

    // sync
    await context.sync();

    return {
      success: ranges.length,
      failed: pairs.length - ranges.length,
      insertedQuestionIds: ranges.map((r) => r.id),
    };
  });
}

async function registerOnParagraphChanged(hostType: Office.HostType) {
  if (hostType === Office.HostType.Word) {
    // Registers the onParagraphChanged event handler on the document.
    await Word.run(async (context) => {
      context.document.onParagraphChanged.add(debouncedBuildSearchParagraphs);
      await context.sync();
    });
  }
}

export function registerShortcuts(hostType: Office.HostType) {
  try {
    Office.actions.associate("InsertAnswer", () => {
      console.log("InsertAnswer callback triggered");
      eventBus.emit("insertAnswer");
    });
    console.log("Successfully registered InsertAnswer shortcut");
  } catch (error) {
    console.error("Error registering shortcuts:", error);
  }
}

export {
  registerOnParagraphChanged,
  registerTextSelectionHandler,
  highlightTextInDocument,
  findAndInsertAfter,
  findAndInsertAfterBatch,
  scrollToText,
  registerBodySearchHandler,
  scrollToTextFuzzy,
};

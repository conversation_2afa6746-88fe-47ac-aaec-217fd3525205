import { parseHtmlToTree } from "./transformerUtils";

describe("parseHtmlToTree", () => {
  it("should ignore div and section tags", () => {
    const html = "<div><p>Hello</p></div>";
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([{ type: "p", text: "Hello" }]);
  });

  it("should not ignore text inside container", () => {
    const html = "<div><p>Hello</p> just text</div>";
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "p",
            parentType: undefined,
            text: "Hello",
          },
          {
            type: "text",
            text: " just text",
          },
        ],
      },
    ]);
  });

  it("should handle lists", () => {
    const html = `
<div class="wrapper">
  <h1>Title</h1>
  <div class="content">
    <p>This is a <strong>bold</strong> paragraph.</p>
    <section>
      <ol>
        <li>First item<bold>bold</bold></li>
        <li>Second item<div class="nested">
            <ul>
              <li>Nested bullet</li>
            </ul>
          </div>
        </li>
      </ol>
    </section>
  </div>
  <p>Final paragraph</p>
</div>
    `;
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "h1",
            parentType: undefined,
            text: "Title",
          },
          {
            type: "container",
            children: [
              {
                type: "p",
                parentType: undefined,
                children: [
                  {
                    type: "text",
                    text: "This is a ",
                  },
                  {
                    type: "strong",
                    parentType: "p",
                    text: "bold",
                  },
                  {
                    type: "text",
                    text: " paragraph.",
                  },
                ],
              },
              {
                type: "ol",
                children: [
                  {
                    type: "li",
                    parentType: "ol",
                    text: "First item",
                  },
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "Second item",
                      },
                      {
                        type: "ul",
                        parentType: "li",
                        children: [
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Nested bullet",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            type: "p",
            parentType: undefined,
            text: "Final paragraph",
          },
        ],
      },
    ]);
  });
  it("should handle lists with bold and italic inside lists", () => {
    const html = `
<div class="wrapper">
  <h1>Title</h1>
  <div class="content">
    <p>This is a <strong>bold</strong> paragraph.</p>
    <section>
      <ol>
        <li>First item <bold>bold</bold> third text</li>
        <li>Second item<div class="nested">
            <ul>
              <li>Nested bullet</li>
            </ul>
          </div>
        </li>
      </ol>
    </section>
  </div>
  <p>Final paragraph</p>
</div>  
    `;
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "h1",
            parentType: undefined,
            text: "Title",
          },
          {
            type: "container",
            children: [
              {
                type: "p",
                parentType: undefined,
                children: [
                  {
                    type: "text",
                    text: "This is a ",
                  },
                  {
                    type: "strong",
                    parentType: "p",
                    text: "bold",
                  },
                  {
                    type: "text",
                    text: " paragraph.",
                  },
                ],
              },
              {
                type: "ol",
                parentType: undefined,
                children: [
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "First item ",
                      },
                      {
                        type: "text",
                        text: " third text",
                      },
                    ],
                  },
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "Second item",
                      },
                      {
                        type: "ul",
                        parentType: "li",
                        children: [
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Nested bullet",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            type: "p",
            parentType: undefined,
            text: "Final paragraph",
          },
        ],
      },
    ]);
  });

  it("should handle complex tables and lists together", () => {
    const html = `
<div class="wrapper">
  <h1>Project Overview</h1>
  <div class="content">
    <p>This document contains both tables and lists:</p>
    
    <table border="1">
      <thead>
        <tr>
          <th>Task</th>
          <th>Priority</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Design Review</td>
          <td>High</td>
          <td>In Progress</td>
        </tr>
        <tr>
          <td>Code Implementation</td>
          <td>Medium</td>
          <td>Pending</td>
        </tr>
      </tbody>
    </table>

    <h2>Implementation Steps</h2>
    <ol>
      <li>Setup project structure<ul>
        <li>Create directories</li>
        <li>Initialize git repository</li>
        <li>Install dependencies</li>
      </ul>
      </li>
      <li>Database design<table border="1">
        <tr>
          <th>Table</th>
          <th>Purpose</th>
        </tr>
        <tr>
          <td>Users</td>
          <td>Store user information</td>
        </tr>
        <tr>
          <td>Projects</td>
          <td>Project metadata</td>
        </tr>
      </table>
      </li>
      <li>API development<ol>
        <li>Authentication endpoints</li>
        <li>CRUD operations</li>
        <li>File upload handling</li>
      </ol>
      </li>
    </ol>

    <p>Final considerations:</p>
    <ul>
      <li>Performance optimization</li>
      <li>Security measures</li>
      <li>Testing coverage</li>
    </ul>
  </div>
</div>
    `;

    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "h1",
            parentType: undefined,
            text: "Project Overview",
          },
          {
            type: "container",
            children: [
              {
                type: "p",
                parentType: undefined,
                text: "This document contains both tables and lists:",
              },
              {
                type: "table",
                tableData: [
                  ["Task", "Priority", "Status"],
                  ["Design Review", "High", "In Progress"],
                  ["Code Implementation", "Medium", "Pending"],
                ],
              },
              {
                type: "h2",
                parentType: undefined,
                text: "Implementation Steps",
              },
              {
                type: "ol",
                parentType: undefined,
                children: [
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "Setup project structure",
                      },
                      {
                        type: "ul",
                        parentType: "li",
                        children: [
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Create directories",
                          },
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Initialize git repository",
                          },
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Install dependencies",
                          },
                        ],
                      },
                    ],
                  },
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "Database design",
                      },
                      {
                        type: "table",
                        tableData: [
                          ["Table", "Purpose"],
                          ["Users", "Store user information"],
                          ["Projects", "Project metadata"],
                        ],
                      },
                    ],
                  },
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "API development",
                      },
                      {
                        type: "ol",
                        parentType: "li",
                        children: [
                          {
                            type: "li",
                            parentType: "ol",
                            text: "Authentication endpoints",
                          },
                          {
                            type: "li",
                            parentType: "ol",
                            text: "CRUD operations",
                          },
                          {
                            type: "li",
                            parentType: "ol",
                            text: "File upload handling",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                type: "p",
                parentType: undefined,
                text: "Final considerations:",
              },
              {
                type: "ul",
                parentType: undefined,
                children: [
                  {
                    type: "li",
                    parentType: "ul",
                    text: "Performance optimization",
                  },
                  {
                    type: "li",
                    parentType: "ul",
                    text: "Security measures",
                  },
                  {
                    type: "li",
                    parentType: "ul",
                    text: "Testing coverage",
                  },
                ],
              },
            ],
          },
        ],
      },
    ]);
  });
});

import { prepareSearchStr } from "./word/word";

describe("prepareSearchStr", () => {
  it("should return original string if length is less than or equal to STRING_LOOKUP_SIZE", () => {
    const shortStr = "hello world";
    expect(prepareSearchStr(shortStr)).toBe(shortStr);
  });

  it("should split long string and add wildcard between halves", () => {
    const longStr =
      "This is a very long string that needs to be split into two parts with a wildcard in between, very very long string. We need to make this string much longer so that it exceeds 255 characters. Here is some more text to make it longer. The quick brown fox jumps over the lazy dog. Pack my box with five dozen liquor jugs. How vexingly quick daft zebras jump! The five boxing wizards jump quickly.";
    const result = prepareSearchStr(longStr);

    // Should contain first half, wildcard, and second half
    expect(result).toContain("*");
    expect(result.split("*").length).toBe(2);

    // First part should be from start of string
    expect(result.startsWith(longStr.substring(0, result.indexOf("*")))).toBe(
      true,
    );

    // Last part should be from end of string
    expect(
      result.endsWith(
        longStr.substring(
          longStr.length - (result.length - result.indexOf("*") - 1),
        ),
      ),
    ).toBe(true);
  });
});

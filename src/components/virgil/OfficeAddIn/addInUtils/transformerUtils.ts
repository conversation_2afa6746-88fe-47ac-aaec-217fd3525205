export interface HtmlNode {
  type: string;
  text?: string;
  children?: HtmlNode[];
  parentType?: string;
  tableData?: string[][];
  href?: string;
}

export interface FlatNode {
  nodeType: string;
  text: string;
  tableData?: string[][];
  href?: string;
}

export function parseHtmlToTree(html: string): HtmlNode[] {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");

  const ALLOWED_ELEMENTS = new Set([
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "p",
    "ul",
    "ol",
    "li",
    "strong",
    "b",
    "em",
    "i",
    "br",
    "div",
    "section",
    "table",
    "tr",
    "td",
    "th",
    "a",
  ]);

  function traverseNode(node: Node, parentType?: string): HtmlNode | null {
    // Handle text nodes
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent;
      const trimmedText = text?.trim();
      return text && trimmedText ? { type: "text", text } : null;
    }

    // Handle element nodes
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();

      // Skip if not in allowed elements
      if (!ALLOWED_ELEMENTS.has(tagName)) {
        return null;
      }

      // For div and section, just process their children
      if (tagName === "div" || tagName === "section") {
        const children: HtmlNode[] = [];
        element.childNodes.forEach((childNode) => {
          const childResult = traverseNode(childNode, parentType);
          if (childResult) {
            children.push(childResult);
          }
        });
        return children.length === 0
          ? null
          : children.length === 1
            ? (children[0] ?? null)
            : { type: "container", children };
      }

      const htmlNode: HtmlNode = { type: tagName, parentType };
      const children: HtmlNode[] = [];

      // Process child nodes
      element.childNodes.forEach((childNode) => {
        const childResult = traverseNode(childNode, tagName);
        if (childResult) {
          children.push(childResult);
        }
      });

      // Handle special cases
      if (tagName === "br") {
        return { type: "br", text: "\n" };
      }

      // Handle anchor tags
      if (tagName === "a") {
        htmlNode.href = (element as HTMLAnchorElement).href;
      }

      // Add children if they exist
      if (children.length > 0) {
        htmlNode.children = children;
      }

      // For text-only nodes, collapse children into text
      if (children.length === 1 && children[0]?.type === "text") {
        htmlNode.text = children[0]?.text;
        delete htmlNode.children;
      }

      // Add table handling
      if (tagName === "table") {
        const tableData: string[][] = [];
        element.querySelectorAll("tr").forEach((row) => {
          const rowData: string[] = [];
          row.querySelectorAll("td, th").forEach((cell) => {
            rowData.push(cell.textContent?.trim() || "");
          });
          if (rowData.length > 0) {
            tableData.push(rowData);
          }
        });

        return tableData.length > 0 ? { type: "table", tableData } : null;
      }

      return htmlNode;
    }

    return null;
  }

  // Start traversal from body and filter out null results
  const nodes = Array.from(doc.body.childNodes)
    .map((node) => traverseNode(node))
    .filter((node): node is HtmlNode => node !== null);

  return nodes;
}

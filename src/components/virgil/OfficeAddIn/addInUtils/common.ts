import { type UnknownAction } from "@reduxjs/toolkit";
import { type Dispatch } from "react";
import {
  EXCEL_HOST_TYPE,
  setHostType,
  WORD_HOST_TYPE,
} from "~/lib/features/addInSlice";
import { setDocumentUrl } from "~/lib/features/addInSlice";

export function registerHostType(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  switch (hostType) {
    case Office.HostType.Word:
      dispatch(setHostType(WORD_HOST_TYPE));
      break;
    case Office.HostType.Excel:
      dispatch(setHostType(EXCEL_HOST_TYPE));
      break;
    default:
      return null;
  }
}

export async function registerFileOpenHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Word) {
    await Word.run(async (context) => {
      dispatch(setDocumentUrl(`${Office.context.document.url}`));
      return context.sync();
    });
  }

  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      dispatch(setDocumentUrl(`${Office.context.document.url}`));
      return context.sync();
    });
  }
}

// https://learn.microsoft.com/en-us/office/dev/add-ins/develop/dialog-best-practices
export function openOfficeDialog({
  url,
  height,
  width,
}: {
  url: string;
  height: number;
  width: number;
}) {
  try {
    // close the dialog if it is already open (before opening a new one)
    if (window.msAddInDialog) {
      window.msAddInDialog.close();
      window.msAddInDialog = undefined;
    }
  } catch (error) {
    console.error("Error closing dialog", error);
  }
  Office.context.ui.displayDialogAsync(
    url,
    {
      height,
      width,
    },
    (result) => {
      if (result.status === Office.AsyncResultStatus.Succeeded) {
        window.msAddInDialog = result.value;
      }
      if (result.status === Office.AsyncResultStatus.Failed) {
        if (result.error.code === 12007) {
          openOfficeDialog({
            url,
            height,
            width,
          }); // Recursive call.
        } else {
          console.error("Error opening dialog", result.error);
        }
      }
    },
  );
}

export function openChat({
  domain,
  chatHeight,
  chatWidth,
  mode = "office",
}: {
  domain: string;
  chatHeight: number;
  chatWidth: number;
  mode?: "office" | "web";
}) {
  const url = `${domain}/add-in-chat`;
  if (mode === "office") {
    openOfficeDialog({
      url,
      height: chatHeight,
      width: chatWidth,
    });
  } else {
    window.open(url, "_blank", "width=500,height=500");
  }
}

export function openDocumentPreview({
  domain,
  previewHeight,
  previewWidth,
  documentId,
  mode = "office",
}: {
  domain: string;
  previewHeight: number;
  previewWidth: number;
  documentId: string;
  mode?: "office" | "web";
}) {
  const url = `${domain}/add-in-document-preview?documentId=${documentId}`;
  if (mode === "office") {
    openOfficeDialog({
      url,
      height: previewHeight,
      width: previewWidth,
    });
  } else {
    window.open(url, "_blank", "width=500,height=500");
  }
}

export function openResponseLibrary({
  domain,
  previewHeight,
  previewWidth,
  questionId,
  mode = "office",
}: {
  domain: string;
  previewHeight: number;
  previewWidth: number;
  questionId: string;
  mode?: "office" | "web";
}) {
  const url = `${domain}/add-in-response-library?questionId=${questionId}`;
  if (mode === "office") {
    openOfficeDialog({
      url,
      height: previewHeight,
      width: previewWidth,
    });
  } else {
    window.open(url, "_blank", "width=500,height=500");
  }
}

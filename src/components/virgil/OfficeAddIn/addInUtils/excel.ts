import { UnknownAction } from "@reduxjs/toolkit";
import type { Dispatch } from "react";
import {
  setActiveSheet,
  setWorksheets,
  setActiveRangeAddress,
  setCellChangedEventDetail,
  setSelectedCellValue,
} from "../../../../lib/features/addInSlice";
import { openOfficeDialog } from "./common";

async function getActiveSheet() {
  try {
    return await Excel.run(async (context) => {
      const worksheet = context.workbook.worksheets.getActiveWorksheet();
      worksheet.load("name");
      await context.sync();

      return worksheet.name;
    });
  } catch (error) {
    console.error("Error getting active sheet", error);
    return null;
  }
}

async function selectRange({ address }: { address: string }) {
  try {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getActiveWorksheet();
      const range = sheet.getRange(address);

      range.select();

      await context.sync();
    });
  } catch (error) {
    console.error(error);
  }
}

async function insertCellValue({
  text,
  rangeAddress,
}: {
  text: string;
  rangeAddress: string;
}) {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getActiveWorksheet();
    const range = sheet.getRange(rangeAddress);
    if (range) {
      range.values = [[text]];
      // range.format.autofitColumns(); // this will resize columns to fit
      range.select();
    } else {
      console.log("No range found");
    }

    return context.sync();
  });
}

async function insertCellRange({
  cells,
}: {
  cells: {
    value: string;
    address: string;
  }[];
}) {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getActiveWorksheet();
    for (const cell of cells) {
      const range = sheet.getRange(cell.address);
      range.values = [[cell.value]];
    }
    const firstRange = sheet.getRange(cells?.at(0)?.address);
    const lastRange = sheet.getRange(cells?.at(-1)?.address);
    firstRange.load("address");
    lastRange.load("address");
    await context.sync();
    const range = sheet.getRange(`${firstRange.address}:${lastRange.address}`);
    range.select();
    return context.sync();
  });
}

function cellSelectionHandlerFn(
  context: Excel.RequestContext,
  dispatch: Dispatch<UnknownAction>,
) {
  return async ({ address }: { address: string }) => {
    const range = address;
    const cellValue = context?.workbook?.worksheets
      ?.getActiveWorksheet()
      ?.getRange(range)
      .load("values");
    await context.sync();
    dispatch(
      setSelectedCellValue((cellValue?.values?.at(0)?.at(0) as string) ?? ""),
    );
    dispatch(setActiveRangeAddress(range));
  };
}

async function registerCellSelectionHandler(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  if (hostType === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onSingleClicked.add(
        cellSelectionHandlerFn(context, dispatch),
      );
      context.workbook.worksheets.onSelectionChanged.add(
        cellSelectionHandlerFn(context, dispatch),
      );

      return context.sync();
    });
  }
}

async function registerCellEditHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onChanged.add(async (event) => {
        console.log("onChanged event", event);

        dispatch(
          setCellChangedEventDetail({
            ...event.details,
            range: event.address,
          }),
        );
      });

      return context.sync();
    });
  }
}

async function registerSheetChangeHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onActivated.add(async (event) => {
        const worksheet = context.workbook.worksheets.getItem(
          event.worksheetId,
        );
        worksheet.load("name");
        await context.sync();
        dispatch(setActiveSheet(worksheet?.name ?? ""));
      });

      const activeSheet = context.workbook.worksheets.getActiveWorksheet();
      activeSheet.load("name");
      await context.sync();
      dispatch(setActiveSheet(activeSheet?.name ?? ""));
      console.log("worksheet", activeSheet?.name);

      return context.sync();
    });
  }
}

async function loadAllWorksheets(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  if (hostType !== Office.HostType.Excel) return;

  try {
    await Excel.run(async (context) => {
      const worksheets = context.workbook.worksheets;
      worksheets.load("items");
      await context.sync();

      const worksheetNames: string[] = [];
      for (const worksheet of worksheets.items) {
        if (!worksheet) continue;
        worksheet.load("name");
        await context.sync();
        worksheetNames.push(worksheet.name);
      }

      console.log("worksheetNames", worksheetNames);

      dispatch(setWorksheets(worksheetNames));
    });
  } catch (error) {
    console.error("Error getting all worksheets", error);
    return [];
  }
}

async function activateWorksheet(sheetName: string) {
  try {
    return await Excel.run(async (context) => {
      const worksheet = context.workbook.worksheets.getItem(sheetName);
      worksheet.activate();
      await context.sync();
      return true;
    });
  } catch (error) {
    console.error("Error activating worksheet", error);
    return false;
  }
}

export function openTablePreview({
  domain,
  tableHeight,
  tableWidth,
  tableId,
  sheetName,
  documentId,
  mode = "office",
}: {
  domain: string;
  tableHeight: number;
  tableWidth: number;
  tableId: string;
  mode?: "office" | "web";
  sheetName: string;
  documentId: string;
}) {
  const url = `${domain}/add-in-table-preview?tableId=${tableId}&sheetName=${sheetName}&documentId=${documentId}`;
  if (mode === "office") {
    openOfficeDialog({
      url,
      height: tableHeight,
      width: tableWidth,
    });
  } else {
    window.open(url, "_blank", "width=800,height=500");
  }
}

async function selectRangeAndFocus({ address }: { address: string }) {
  await Excel.run(async (context) => {
    const sheet = context.workbook.worksheets.getActiveWorksheet();
    const range = sheet.getRange(address);
    range.select();
  });
}

export {
  selectRangeAndFocus,
  insertCellRange,
  getActiveSheet,
  loadAllWorksheets,
  activateWorksheet,
  selectRange,
  insertCellValue,
  registerCellSelectionHandler,
  registerCellEditHandler,
  registerSheetChangeHandler,
};

/**
 * Compares extracted and generated HTML table strings and adds "generated" class
 * to table cells that have different content.
 *
 * @param extractedHtml - The original extracted HTML table string
 * @param generatedHtml - The generated HTML table string with responses
 * @returns The generated HTML with "generated" class added to modified cells
 */
export function highlightGeneratedCells(
  extractedHtml: string,
  generatedHtml: string,
): string {
  // Create DOM parsers for both HTML strings
  const parser = new DOMParser();
  const extractedDoc = parser.parseFromString(extractedHtml, "text/html");
  const generatedDoc = parser.parseFromString(generatedHtml, "text/html");

  // Get all td elements from both documents
  const extractedTds = extractedDoc.querySelectorAll("td");
  const generatedTds = generatedDoc.querySelectorAll("td");

  // Create a map of extracted td elements by their id
  const extractedTdMap = new Map<string, Element>();
  extractedTds.forEach((td) => {
    const id = td.getAttribute("id");
    if (id) {
      extractedTdMap.set(id, td);
    }
  });

  // Compare generated tds with extracted tds
  generatedTds.forEach((td) => {
    const id = td.getAttribute("id");
    if (id) {
      const extractedTd = extractedTdMap.get(id);
      if (extractedTd) {
        // Compare the innerHTML content
        const extractedContent = extractedTd.innerHTML.trim();
        const generatedContent = td.innerHTML.trim();

        // If content is different, add the "generated" class
        if (extractedContent !== generatedContent) {
          const existingClasses = td.getAttribute("class") || "";
          const newClasses = existingClasses
            ? `${existingClasses} generated`
            : "generated";
          td.setAttribute("class", newClasses);
        }
      }
    }
  });

  // Return the modified generated HTML
  return generatedDoc.body.innerHTML;
}

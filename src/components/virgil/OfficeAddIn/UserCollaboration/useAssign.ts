import { toast } from "~/components/snackbar";
import { api } from "~/trpc/react";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { UserWithAssignedBit } from "~/server/api/managers/collaborationManager";

export const useAssign = ({ searchQuery }: { searchQuery: string }) => {
  const queryClient = useQueryClient();
  const { mutate: assign, isPending: isAssigning } =
    api.collaboration.assignQuestion.useMutation({
      onMutate: async (data: { questionId: string; userId: string }) => {
        const quesries = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.searchUsers,
            { questionId: data.questionId, query: searchQuery },
            "any",
          ),
        });

        const previousQueries = [] as { queryKey: any; state: any }[];
        const userMap = new Map<string, UserWithAssignedBit>();

        quesries.forEach((query) => {
          const state: UserWithAssignedBit[] | undefined =
            queryClient.getQueryData(query.queryKey);
          if (!state) {
            return;
          }

          previousQueries.push({
            queryKey: query.queryKey,
            state,
          });

          // set users:
          for (const user of state) {
            userMap.set(user.id, user);
          }

          queryClient.setQueryData(query.queryKey, (old: any) => {
            return old.map((user: UserWithAssignedBit) => {
              if (user.id === data.userId) {
                return { ...user, isAssigned: true };
              }
              return user;
            });
          });
        });

        // update assigned users:
        const assignedUsersQuery = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.getAssignedUsersByQuestion,
            { questionId: data.questionId },
            "any",
          ),
        });

        assignedUsersQuery.forEach((query) => {
          previousQueries.push({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          });

          queryClient.setQueryData(query.queryKey, (old: any) => {
            if (
              userMap.has(data.userId) &&
              !old.find((user: UserWithAssignedBit) => user.id === data.userId)
            ) {
              return [...old, userMap.get(data.userId)];
            }
            return old;
          });
        });

        return {
          previousQueries: previousQueries.map((query) => ({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          })),
        };
      },
      onSuccess: (data, variables, context) => {
        toast.success("Question assigned successfully");
      },
      onSettled: (data, error, variables, context) => {
        context?.previousQueries.forEach((query) => {
          queryClient.refetchQueries({ queryKey: query.queryKey });
        });
      },
      onError: (error, variables, context) => {
        toast.error("Failed to assign question");
        context?.previousQueries.forEach((query) => {
          queryClient.setQueryData(query.queryKey, query.state);
        });
      },
      meta: {
        skipInvalidateQueries: false, // =false will invalidate all queries and refetch all questions
      },
    });

  return { assign, isAssigning };
};

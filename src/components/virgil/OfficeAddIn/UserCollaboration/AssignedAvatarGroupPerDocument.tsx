import { Initials } from "./initials";
import { Skeleton } from "~/v2/components/ui/Skeleton";
import { Tooltip, TooltipTrigger, TooltipContent } from "~/v2/components/ui/Tooltip";
import { Avatar } from "~/v2/components/ui/Avatar";
import { AvatarGroup } from "~/v2/components/ui/AvatarGroup";
import { useGetAssignedUsersPerDocument } from "./useGetAssignedUsersPerDocument";

export const AssignedAvatarGroupPerDocument = ({
    documentId,
}: {
    documentId: string;
}) => {
    const { users, isLoading } = useGetAssignedUsersPerDocument({ documentId });
    return (
        <AvatarGroup max={4}>
            {isLoading ? (
                <Skeleton style={{ width: 24, height: 24, borderRadius: '50%' }} />
            ) : (
                users.map((user) => (
                    <Tooltip key={user.id}>
                        <TooltipTrigger asChild>
                            <Avatar>
                                {Initials({ name: user.name ?? '' })}
                            </Avatar>
                        </TooltipTrigger>
                        <TooltipContent side="top" align="center" className="z-1600">
                            {user.name || 'Unknown User'}
                        </TooltipContent>
                    </Tooltip>
                ))
            )}
        </AvatarGroup>
    )
}
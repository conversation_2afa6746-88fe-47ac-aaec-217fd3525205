import { toast } from "~/components/snackbar";
import { api } from "~/trpc/react";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { UserWithAssignedBit } from "~/server/api/managers/collaborationManager";

export const useAssignQuestionWithApproval = ({
  searchQuery,
}: {
  searchQuery: string;
}) => {
  const queryClient = useQueryClient();
  const { mutate: assignWithApproval, isPending: isAssigningWithApproval } =
    api.collaboration.assignQuestionWithApproval.useMutation({
      onMutate: async (data: { questionId: string; userId: string }) => {
        const queries = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.searchUsers,
            { questionId: data.questionId, query: searchQuery },
            "any",
          ),
        });

        const previousQueries = [] as { queryKey: any; state: any }[];
        const userMap = new Map<string, UserWithAssignedBit>();

        queries.forEach((query) => {
          const state: UserWithAssignedBit[] | undefined =
            queryClient.getQueryData(query.queryKey);
          if (!state) {
            return;
          }

          previousQueries.push({
            queryKey: query.queryKey,
            state,
          });

          // set users:
          for (const user of state) {
            userMap.set(user.id, user);
          }

          queryClient.setQueryData(query.queryKey, (old: any) => {
            return old.map((user: UserWithAssignedBit) => {
              if (user.id === data.userId) {
                return {
                  ...user,
                  isAssigned: true,
                  isAssignedForApproval: true,
                };
              }
              return user;
            });
          });
        });

        // update assigned users with approval status:
        const assignedUsersQuery = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.getAssignedUsersByQuestion,
            { questionId: data.questionId },
            "any",
          ),
        });

        assignedUsersQuery.forEach((query) => {
          previousQueries.push({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          });

          queryClient.setQueryData(query.queryKey, (old: any) => {
            if (
              userMap.has(data.userId) &&
              !old.find((user: UserWithAssignedBit) => user.id === data.userId)
            ) {
              const userToAdd = userMap.get(data.userId);
              return [...old, { ...userToAdd, isAssignedForApproval: true }];
            }
            return old;
          });
        });

        return {
          previousQueries: previousQueries.map((query) => ({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          })),
        };
      },
      onSuccess: (data, variables, context) => {
        toast.success("Question assigned with approval request");
      },
      onSettled: (data, error, variables, context) => {
        context?.previousQueries.forEach((query) => {
          queryClient.refetchQueries({ queryKey: query.queryKey });
        });
      },
      onError: (error, variables, context) => {
        toast.error("Failed to assign question with approval");
        context?.previousQueries.forEach((query) => {
          queryClient.setQueryData(query.queryKey, query.state);
        });
      },
      meta: {
        skipInvalidateQueries: false,
      },
    });

  return { assignWithApproval, isAssigningWithApproval };
};

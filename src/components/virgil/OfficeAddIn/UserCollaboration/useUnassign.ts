import { useQueryClient } from "@tanstack/react-query";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { toast } from "~/components/snackbar";
import { UserWithAssignedBit } from "~/server/api/managers/collaborationManager";
import { api } from "~/trpc/react";

export const useUnassign = ({ searchQuery }: { searchQuery: string }) => {
  const queryClient = useQueryClient();
  const { mutate: unassign, isPending: isUnassigning } =
    api.collaboration.unassignQuestion.useMutation({
      onMutate: async (data: { questionId: string; userId: string }) => {
        const previousQueries = [] as { queryKey: any; state: any }[];

        const quesries = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.searchUsers,
            { questionId: data.questionId, query: searchQuery },
            "any",
          ),
        });

        quesries.forEach((query) => {
          previousQueries.push({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          });
          queryClient.setQueryData(query.queryKey, (old: any) => {
            return old.map((user: UserWithAssignedBit) => {
              if (user.id === data.userId) {
                return {
                  ...user,
                  isAssigned: false,
                  isAssignedForApproval: false,
                };
              }
              return user;
            });
          });
        });

        const assignedUsersQuery = queryClient.getQueryCache().findAll({
          queryKey: getQueryKey(
            api.collaboration.getAssignedUsersByQuestion,
            { questionId: data.questionId },
            "any",
          ),
        });

        assignedUsersQuery.forEach((query) => {
          previousQueries.push({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          });

          queryClient.setQueryData(query.queryKey, (old: any) => {
            return old.filter(
              (user: UserWithAssignedBit) => user.id !== data.userId,
            );
          });
        });
        return {
          previousQueries: previousQueries.map((query) => ({
            queryKey: query.queryKey,
            state: queryClient.getQueryData(query.queryKey),
          })),
        };
      },
      onSuccess: () => {
        toast.success("Question unassigned successfully");
      },
      onSettled: (data, error, variables, context) => {
        context?.previousQueries.forEach((query) => {
          queryClient.refetchQueries({ queryKey: query.queryKey });
        });
      },
      onError: (error, variables, context) => {
        toast.error("Failed to unassign question");
        context?.previousQueries.forEach((query) => {
          queryClient.setQueryData(query.queryKey, query.state);
        });
      },
      meta: {
        skipInvalidateQueries: true,
      },
    });

  return { unassign, isUnassigning };
};

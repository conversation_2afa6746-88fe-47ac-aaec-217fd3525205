import { keepPreviousData } from "@tanstack/react-query";
import { api } from "~/trpc/react";

interface UseUserSearchProps {
  questionId: string;
}

export function useGetAssignedUsers({ questionId }: UseUserSearchProps) {
  const {
    data: users = [],
    isLoading,
    refetch,
  } = api.collaboration.getAssignedUsersByQuestion.useQuery(
    { questionId },
    {
      placeholderData: keepPreviousData,
    },
  );

  return {
    users,
    collaborators: users.filter((user) => !user.isAssignedForApproval),
    approvers: users.filter((user) => user.isAssignedForApproval),
    isLoading,
    refetch,
  };
}

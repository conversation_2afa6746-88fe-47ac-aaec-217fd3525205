import React from 'react';
import { Button } from '~/v2/components/ui';
import {
    DropdownMenuItem,
} from '~/v2/components/ui/DropdownMenu';
import { Typography } from '~/v2/components/ui/Typography';
import { Avatar } from '~/v2/components/ui/Avatar';
import {
    Send as SendIcon,
    User as PersonIcon,
    X as CloseIcon,
} from 'lucide-react';
import { Initials } from './initials';
import { cn } from '~/v2/lib/utils';
import { User } from './types';
type DropDownItemProps = {
    user: User,
    isAssigning: boolean,
    isUnassigning: boolean,
    handleUserClick: (e: React.MouseEvent<HTMLElement>, user: User) => void,
    handleSendForApproval: (e: React.MouseEvent<HTMLElement>, user: User) => void,
    handleUnassignClick: (e: React.MouseEvent<HTMLElement>, user: User) => void,
    className?: string
}
export function DropDownItem({ user, isAssigning, isUnassigning, handleUserClick, handleSendForApproval, handleUnassignClick, className }: DropDownItemProps) {
    return (
        <DropdownMenuItem
            onClick={(e) => handleUserClick(e, user)}
            disabled={isAssigning || isUnassigning}
            className={cn(
                "flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer rounded-none",
                className
            )}
        >
            <Avatar
                size={40}
                bgColor={user.isAssigned ? "#22c55e" : "#3b82f6"}
                className="text-xs"
            >
                {Initials({ name: user.name ?? '' })}
            </Avatar>
            <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1">
                    <Typography
                        variant="boldBody"
                        className={cn(
                            "truncate",
                            user.isAssigned && "text-primary"
                        )}
                    >
                        {user.name || 'Unknown User'}
                    </Typography>
                    {user.isAssigned && (
                        <PersonIcon style={{ width: '11px', height: '11px' }} className="text-primary inline-block" />
                    )}
                    {user.isAssignedForApproval && (
                        <SendIcon style={{ width: '11px', height: '11px' }} className="text-primary inline-block" />
                    )}
                </div>
                {user.email && (
                    <Typography
                        variant="caption"
                        className="text-muted-foreground truncate block text-xs"
                    >
                        {user.email}
                    </Typography>
                )}
            </div>
            <div className="flex items-center gap-1">
                {!user.isAssignedForApproval && (
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => handleSendForApproval(e, user)}
                        disabled={isAssigning || isUnassigning}
                        title="Send for approval"
                        className="h-6 w-6 p-0 text-blue-600 hover:bg-blue-100 hover:text-blue-700"
                    >
                        <SendIcon className="w-2 h-2" />
                    </Button>
                )}
                {user.isAssigned && (
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => handleUnassignClick(e, user)}
                        disabled={isAssigning || isUnassigning}
                        className="h-6 w-6 p-0 text-red-600 hover:bg-red-100 hover:text-red-700"
                    >
                        <CloseIcon className="w-2 h-2" />
                    </Button>
                )}
            </div>
        </DropdownMenuItem>
    )
}
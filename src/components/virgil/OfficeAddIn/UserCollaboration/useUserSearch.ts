import { api } from "~/trpc/react";

interface UseUserSearchProps {
  searchQuery: string;
  isOpen: boolean;
  questionId: string;
}

export function useUserSearch({
  searchQuery,
  isOpen,
  questionId,
}: UseUserSearchProps) {
  const {
    data: users = [],
    isLoading,
    refetch,
  } = api.collaboration.searchUsers.useQuery(
    { query: searchQuery, questionId },
    {
      enabled: isOpen,
    },
  );

  return {
    users,
    isLoading,
    refetch,
  };
}

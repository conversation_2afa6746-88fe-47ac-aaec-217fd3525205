import { keepPreviousData } from "@tanstack/react-query";
import { api } from "~/trpc/react";

interface UseUserSearchProps {
  documentId: string;
}

export function useGetAssignedUsersPerDocument({
  documentId,
}: UseUserSearchProps) {
  const {
    data: users = [],
    isLoading,
    refetch,
  } = api.collaboration.getAssignedUsersByDocument.useQuery(
    { documentId },
    {
      placeholderData: keepPreviousData,
    },
  );

  return {
    users,
    isLoading,
    refetch,
  };
}

import React, { useState, useCallback } from 'react';
import { Button } from '~/v2/components/ui';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
    DropdownMenuItem,
} from '~/v2/components/ui/DropdownMenu';
import { Typography } from '~/v2/components/ui/Typography';
import { Input } from '~/v2/components/ui/Input';
import {
    User as PersonIcon,
    Users as GroupIcon,
    Search as SearchIcon,
    X as CloseIcon,
} from 'lucide-react';
import { useUserSearch } from './useUserSearch';
import { useGetAssignedUsers } from './useGetAssignedUsers';
import { useAssign } from './useAssign';
import { useUnassign } from './useUnassign';
import { useAssignQuestionWithApproval } from './useAssignQuestionWithApproval';
import { cn } from '~/v2/lib/utils';
import { DropDownItem } from './DropDownItem';
import { User } from './types';

interface UserTagProps {
    disabled?: boolean;
    questionId: string;
    responseId: string;
}

export default function UserCollaboration({ disabled = false, questionId, responseId }: UserTagProps) {
    const [open, setOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');

    // Use the custom hook for user search
    const { users, isLoading } = useUserSearch({ searchQuery, isOpen: open, questionId });
    const { users: assignedUsers, isLoading: isAssignedLoading, refetch } = useGetAssignedUsers({ questionId });
    const { assign, isAssigning } = useAssign({ searchQuery });
    const { unassign, isUnassigning } = useUnassign({ searchQuery });
    const { assignWithApproval } = useAssignQuestionWithApproval({ searchQuery });

    const handleClose = useCallback(() => {
        setOpen(false);
        setSearchQuery('');
    }, []);

    const handleUserClick = useCallback((event: React.MouseEvent, user: User) => {
        event.stopPropagation();
        event.preventDefault();
        if (!user.isAssigned) {
            assign({ questionId, userId: user.id });
            refetch();
        }
    }, [assign, questionId, refetch]);

    const handleSendForApproval = useCallback((event: React.MouseEvent, user: User) => {
        event.stopPropagation();
        event.preventDefault();
        assignWithApproval({ questionId, userId: user.id, responseId });
    }, [assignWithApproval, questionId, responseId]);

    const handleUnassignClick = useCallback((event: React.MouseEvent, user: User) => {
        event.stopPropagation();
        event.preventDefault();
        unassign({ questionId, userId: user.id });
        refetch();
    }, [unassign, questionId, refetch]);

    const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value);
    }, []);

    const hasAssignedUsers = assignedUsers.length;
    const PersonIconComponent = hasAssignedUsers ? GroupIcon : PersonIcon;

    return (
        <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    disabled={disabled}
                    className={cn(
                        "min-w-auto px-1.5 py-0.5 rounded-md border border-divider hover:border-primary-main"
                    )}
                >
                    <PersonIconComponent className="w-2 h-2" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                align="start"
                className="w-80 p-0 z-1000"
                onInteractOutside={handleClose}
            >
                <div className="flex items-start justify-between space-x-2 p-3 pb-0">
                    <div>
                        <Typography variant="boldBody" className="text-sm">
                            Collaborators/Approvers ({users.filter((user: User) => user.isAssigned).length})
                        </Typography>
                        <Typography variant="body">
                            Collaborators will be notified of new responses and are able to edit and submit responses for approval.
                        </Typography>
                    </div>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleClose}
                        className="h-6 w-6 p-0"
                    >
                        <CloseIcon className="h-4 w-4" />
                    </Button>
                </div>

                {/* Search Input */}
                <div className="flex items-center justify-between space-x-2 p-3 border-b border-border">
                    <div className="relative flex-1">
                        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search users..."
                            value={searchQuery}
                            onChange={handleSearchChange}
                            className="pl-9 h-8 text-sm"
                        />
                    </div>
                </div>

                {/* Users List */}
                <div className="max-h-60 overflow-auto">
                    {isLoading ? (
                        <div className="p-3 text-center">
                            <Typography variant="caption" className="text-muted-foreground">
                                Loading...
                            </Typography>
                        </div>
                    ) : users.length === 0 ? (
                        <div className="p-3 text-center">
                            <Typography variant="caption" className="text-muted-foreground">
                                {searchQuery ? 'No users found' : 'No users available'}
                            </Typography>
                        </div>
                    ) : (
                        <>
                            <div>
                                {users.filter((user: User) => user.isAssigned).map((user: User, index: number) => (
                                    <DropDownItem
                                        key={user.id}
                                        user={user}
                                        isAssigning={isAssigning}
                                        isUnassigning={isUnassigning}
                                        handleUserClick={handleUserClick}
                                        handleSendForApproval={handleSendForApproval}
                                        handleUnassignClick={handleUnassignClick}
                                        className={index === users.filter((user: User) => user.isAssigned).length - 1 ? "border-b border-border" : undefined}
                                    />
                                ))}
                            </div>
                            <div>
                                {users.filter((user: User) => !user.isAssigned).map((user: User) => (
                                    <DropDownItem
                                        key={user.id}
                                        user={user}
                                        isAssigning={isAssigning}
                                        isUnassigning={isUnassigning}
                                        handleUserClick={handleUserClick}
                                        handleSendForApproval={handleSendForApproval}
                                        handleUnassignClick={handleUnassignClick}
                                    />
                                ))}
                            </div>
                        </>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

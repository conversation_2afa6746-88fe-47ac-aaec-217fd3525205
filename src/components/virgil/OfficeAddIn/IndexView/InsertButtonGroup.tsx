import { useCallback, useEffect, useMemo, useState } from "react";
import { findAndInsertAfter, getTableInsertOptions, TableInsertOption, WordInsertOptions } from "../addInUtils/word/word";
import { toast } from "~/components/snackbar";
import { QuestionStatusType } from "@prisma/client";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import { useUserMSAddInSettings } from "../../hooks/useUserMSAddInSettings";
import { useUpdateQuestionStatus } from "../../hooks/useUpdateQuestionStatus";
import { ActionButton } from "~/v2/components/composit/ActionButton";
import { Button } from "~/v2/components";

type InsertOption = {
    type: WordInsertOptions;
    address?: { rowIndex: number; colIndex: number };
}

const TYPE_TO_LABEL: Record<WordInsertOptions, string> = {
    AtCursor: "Insert at cursor",
    AfterQuestion: "Insert after question",
    CellToRight: "Insert to the right",
    CellBelow: "Insert below",
};
export default function InsertButtonGroup({
    responseText,
    question,
    disabled
}: {
    question: DDQuestionWithFeedback;
    responseText: string;
    disabled: boolean;
}) {
    const [loading, setLoading] = useState<boolean>(false);
    const DEFAULT_INSERT_OPTIONS: InsertOption[] = [{
        type: "AtCursor",
    },
    {
        type: "AfterQuestion",
    },
    ];
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();
    const updateQuestionStatusMutation = useUpdateQuestionStatus({
        onSuccess: () => { },
        onError: (error: any) => {
            toast.error(`Error updating question status: ${error}`);
        },
    });
    const { text: questionText, id: questionId } = question;
    const findAndInsertAfterCB = useCallback(findAndInsertAfter, []);
    const handleInsert = useCallback(async (insertype: WordInsertOptions, address?: { rowIndex: number; colIndex: number }) => {
        if (loading) return;
        try {
            setLoading(true);
            await findAndInsertAfterCB(
                questionText,
                responseText,
                msAddInSettings?.insertTextColor ?? 'green',
                msAddInSettings?.fontSize,
                msAddInSettings?.bold,
                msAddInSettings?.italic,
                msAddInSettings?.underline,
                !msAddInSettings?.sameAsDocument,
                insertype,
                address,
            );
            // await scrollToTextFuzzy(questionText);
            await updateQuestionStatusMutation.mutateAsync({
                id: questionId,
                newStatus: QuestionStatusType.ANSWERED,
                oldStatus: question.status,
            });
            setLoading(false);
        } catch (error) {
            toast.error(`Error inserting response: ${error}`);
            setLoading(false);
        }

    }, [questionText, msAddInSettings?.insertTextColor, questionId, responseText, loading, findAndInsertAfterCB, updateQuestionStatusMutation]);

    const undoInsert = useCallback(async () => {
        await updateQuestionStatusMutation.mutateAsync({
            id: questionId,
            newStatus: QuestionStatusType.NEW,
            oldStatus: QuestionStatusType.ANSWERED,
        });
    }, [questionId, updateQuestionStatusMutation]);

    const [tableInsertOptions, setTableInsertOptions] = useState<InsertOption[]>([]);

    useEffect(() => {
        getTableInsertOptions(questionText).then((options: TableInsertOption[]) => {
            setTableInsertOptions(options.map((option: TableInsertOption) => ({
                type: option.type,
                address: option.address,
            })));
        });
    }, [questionText]);

    return useMemo(() => {
        if (question.status === QuestionStatusType.ANSWERED) {
            return <Button
                onClick={() => {
                    undoInsert();
                }}
                disabled={updateQuestionStatusMutation.isPending}
            >
                Undo
            </Button>
        }
        return <ActionButton
            options={
                [
                    ...DEFAULT_INSERT_OPTIONS,
                    ...tableInsertOptions,
                ].map((option) => ({
                    label: TYPE_TO_LABEL[option.type],
                    action: () => handleInsert(option.type, option.address),
                }))
            }
            label="Insert"
            disabled={loading || disabled}
            menuClassName="z-1000"
        />
    }, [loading, handleInsert, tableInsertOptions, question.status, updateQuestionStatusMutation, questionId, undoInsert]);
}
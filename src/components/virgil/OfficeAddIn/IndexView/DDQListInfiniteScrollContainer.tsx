import React from "react";
import { ActionsBar } from "./ActionsBar";
import { useDDQuestionsInfinite } from "../../hooks/useDDQuestionsInfinite";
import { DDQSelectProvider } from "../../contexts/DDQSelectContext";
import QuestionActions from "./QuestionActions";
import DDQSectionsWithActions from "./SectionsView/DDQSectionsWithActions";

const DDQListInfiniteScrollContainer: React.FC = () => {
  const { questionsBySection, questions } = useDDQuestionsInfinite();

  return (
    <DDQSelectProvider questions={questions} questionsBySection={questionsBySection}>
      <div className="flex flex-col h-full">
        <ActionsBar />
        <QuestionActions className="z-1" />
        <div className="flex-1 overflow-y-auto">
          <DDQSectionsWithActions />
        </div>
      </div>
    </DDQSelectProvider>

  );
};

export default DDQListInfiniteScrollContainer;

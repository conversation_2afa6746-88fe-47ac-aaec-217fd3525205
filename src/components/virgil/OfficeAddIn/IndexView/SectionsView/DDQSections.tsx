import { AccordionRoot, AccordionContent, AccordionHeader, AccordionItem, AccordionTrigger } from "~/v2/components/ui/Accordion/Accordion";
import { Typography } from "~/v2/components/ui/Typography";
import { DDQSectionWithQuestions, DDQuestionWithIndexAndFeedback } from "~/server/api/managers/questionManager";
import { CollapsedContent } from "../SingleDDQWithData/CollapsedContent";
import { useCallback, useRef } from "react";
import { useState } from "react";
import { ExpandedContent } from "../SingleDDQWithData/ExpandedContent";
import { useDDQSelect } from "~/components/virgil/contexts/DDQSelectContext";
import { Checkbox } from "~/v2/components";

export const DDQSections = ({
    questionsBySection,
    onExpand,
}: {
    questionsBySection: DDQSectionWithQuestions;
    onExpand: (item: DDQuestionWithIndexAndFeedback) => void;
}) => {
    const expandedIdRef = useRef<string>("");
    const activeItemRef = useRef<string>("");
    const [, forceUpdate] = useState({});
    const handleToggle = useCallback((item: DDQuestionWithIndexAndFeedback) => {
        expandedIdRef.current = expandedIdRef.current === item.id ? "" : item.id;
        activeItemRef.current = item.id;
        onExpand(item);
        forceUpdate({});
    }, []);
    const { selectedIds, handleSelectSingle, handleSelectSection, selectedSectionIds, selectedPerSection } = useDDQSelect();

    return (
        <AccordionRoot type="single" collapsible>
            {Object.values(questionsBySection).map((section) => (
                <AccordionItem key={section.sectionId} value={section.sectionId}>
                    <AccordionTrigger className="flex items-center gap-x-2">
                        <Checkbox
                            checked={selectedSectionIds.has(section.sectionId)}
                            onCheckedChange={() => {
                                handleSelectSection(section.sectionId);
                            }}
                            onClick={(e) => e.stopPropagation()}
                        />
                        <Typography variant="sectionHeader">
                            {section.title} ({selectedPerSection[section.sectionId] ?? 0 > 0 ? `${selectedPerSection[section.sectionId]}/${section.questions.length} selected` : `${section.questions.length}`})
                        </Typography>
                    </AccordionTrigger>
                    <AccordionContent>
                        {section.questions.map((question) => (
                            expandedIdRef.current === question.id ?
                                <ExpandedContent
                                    key={question.id}
                                    question={question}
                                    isChecked={selectedIds.has(question.id)}
                                    setIsChecked={handleSelectSingle}
                                />
                                :
                                <CollapsedContent
                                    key={question.id}
                                    question={question}
                                    isChecked={selectedIds.has(question.id)}
                                    setIsChecked={handleSelectSingle}
                                    handleToggle={handleToggle}
                                    isActive={question.id === activeItemRef.current}
                                />
                        ))}
                    </AccordionContent>
                </AccordionItem>
            ))}
        </AccordionRoot>
    );
};
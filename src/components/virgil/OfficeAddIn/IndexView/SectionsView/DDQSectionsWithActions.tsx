import React, { useCallback, useEffect, useMemo } from "react";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import { eventBus } from "~/lib/eventBus";
import { Searcher } from "fast-fuzzy";
import parseQuestionData from "../SingleDDQWithData/parseQuestionData";
import { DDQSections } from "./DDQSections";
import { useDDQuestionsInfinite } from "~/components/virgil/hooks/useDDQuestionsInfinite";
import { scrollToTextFuzzy } from "../../addInUtils/word/word";
import InfoBox from "../InfoBox";

const DDQSectionsWithActions: React.FC = () => {
    const { questions, questionsBySection, isLoading } = useDDQuestionsInfinite();
    const questionsSearcherMemo = useMemo(() => {
        return new Searcher(questions, { keySelector: (obj) => obj.text });
    }, [questions]);

    const wordSelectionChanged = useCallback(({
        paragraph,
    }: {
        paragraph: string;
    }) => {
        // user is in the word document and not in the plugin
        if (paragraph && !document.hasFocus()) {
            const matchingQuestion = questionsSearcherMemo.search(paragraph)?.[0];
            if (matchingQuestion) {
                eventBus.emit("virtualListScrollToIndexEvent", {
                    index: matchingQuestion.index,
                });
            }
        }
    }, [questionsSearcherMemo]);

    // this runs after expanded item was rendered and animation is complete
    const onExpandedItemChanged = useCallback(async (item: DDQuestionWithFeedback) => {
        if (document.hasFocus()) {
            const { questionText } = parseQuestionData(item);
            await scrollToTextFuzzy(questionText);
        }
    }, []);

    useEffect(() => {
        eventBus.on('wordSelectionChanged', wordSelectionChanged);
        return () => {
            eventBus.off('wordSelectionChanged', wordSelectionChanged);
        };
    }, [wordSelectionChanged]);

    if (isLoading || !questionsBySection) {
        return (
            <InfoBox text="Loading questions..." />
        );
    }

    if (questions.length === 0) {
        return (
            <InfoBox text="No due diligence questions found, please re-process document in the Data Room" />
        );
    }

    return (
        <DDQSections questionsBySection={questionsBySection} onExpand={onExpandedItemChanged} />
    );
};

export default DDQSectionsWithActions;
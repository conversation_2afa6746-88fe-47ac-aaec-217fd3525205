import React from "react";
import { useSelectAction } from "../../hooks/useSelectAction";
import { useDDQSelect } from "../../contexts/DDQSelectContext";
import { SelectActions } from "../SelectActions";

const QuestionActions: React.FC<{ className?: string }> = ({ className }) => {
    const { isAllSelected, handleSelectAll, totalSelected, totalCount } = useDDQSelect();
    const { SplitButtonMemo } = useSelectAction();

    if (totalSelected === 0) {
        return null;
    }

    return (
        <SelectActions
            className={className}
            actionButtonsComponent={SplitButtonMemo}
            isAllSelected={isAllSelected}
            totalSelected={totalSelected}
            totalCount={totalCount}
            handleSelectAll={handleSelectAll}
        />
    )
}

export default QuestionActions;
import { useRef, useCallback } from "react";

type AccordionProps = {
    children: React.ReactNode;
    index: number;
    itemId: string;
    measureElement: (node: HTMLElement) => void;
}

export default function VirtualizedAccordion({
    children,
    index,
    itemId,
    measureElement,
}: AccordionProps) {
    const nodeRef = useRef<HTMLElement>(null);
    const refCallback = useCallback((el: HTMLDivElement) => {
        if (el) {
            nodeRef.current = el;
            measureElement(el);
        }
    }, [measureElement]);


    return (
        <div
            ref={refCallback}
            data-index={index}
            key={itemId}
            id={`ddq-${index}`}
            data-testid="virtualized-accordion"
        >
            {children}
        </div>
    );
}

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import { useMemo, useState, useCallback } from "react";
import {
    Box,
    Typography,
    Paper,
    Chip,
    TextField,
    Button,
    Stack,
} from "@mui/material";
import { VirtualizedList } from "./VirtualizedList";
import { eventBus } from "~/lib/eventBus";

// Mock item type
type MockItem = {
    id: string;
    title: string;
    description: string;
    category: string;
    priority: "low" | "medium" | "high";
};

// Generate 500 random items
const generateRandomItems = (): MockItem[] => {
    const categories = [
        "Development",
        "Design",
        "Marketing",
        "Sales",
        "Support",
        "Research",
        "Planning",
    ];
    const priorities: ("low" | "medium" | "high")[] = ["low", "medium", "high"];
    const titles = [
        "Implement new feature",
        "Fix critical bug",
        "Update documentation",
        "Review code changes",
        "Design user interface",
        "Create marketing campaign",
        "Analyze user feedback",
        "Plan sprint",
        "Deploy to production",
        "Test application",
        "Optimize performance",
        "Refactor codebase",
        "Add unit tests",
        "Update dependencies",
        "Create wireframes",
        "Write blog post",
        "Conduct user research",
        "Prepare presentation",
        "Review pull requests",
        "Monitor system",
    ];

    const descriptions = [
        "This task involves implementing a new feature that will improve user experience.",
        "Critical bug that needs immediate attention to prevent data loss.",
        "Update the documentation to reflect recent changes in the API.",
        "Review code changes submitted by team members for quality assurance.",
        "Design a new user interface component that follows design system guidelines.",
        "Create a marketing campaign to promote the new product launch.",
        "Analyze user feedback to identify areas for improvement.",
        "Plan the upcoming sprint with team members and stakeholders.",
        "Deploy the latest version to production environment.",
        "Test the application thoroughly to ensure all features work correctly.",
        "Optimize the application performance to improve loading times.",
        "Refactor the codebase to improve maintainability and readability.",
        "Add comprehensive unit tests to increase code coverage.",
        "Update project dependencies to latest stable versions.",
        "Create wireframes for the new feature design.",
        "Write a blog post about the latest product updates.",
        "Conduct user research to understand user needs and pain points.",
        "Prepare a presentation for the upcoming stakeholder meeting.",
        "Review pull requests and provide constructive feedback.",
        "Monitor system performance and identify potential issues.",
    ];

    return Array.from({ length: 500 }, (_, index) => {
        const randomTitle = titles[Math.floor(Math.random() * titles.length)]!;
        const randomDescription =
            descriptions[Math.floor(Math.random() * descriptions.length)]!;
        const randomCategory =
            categories[Math.floor(Math.random() * categories.length)]!;
        const randomPriority =
            priorities[Math.floor(Math.random() * priorities.length)]!;

        return {
            id: `item-${index + 1}`,
            title: `${randomTitle} #${index + 1}`,
            description: randomDescription,
            category: randomCategory,
            priority: randomPriority,
        };
    });
};

// Component that includes the scroll form
function VirtualizedListWithScrollForm(props: {
    estimatedRowHeight: number;
    containerHeight: number;
    containerWidth: number;
    itemsCount: number;
}) {
    const [scrollIndex, setScrollIndex] = useState<string>("");
    const [randomIndexes, setRandomIndexes] = useState<number[]>([0, 0, 0, 0, 0]);
    const randomItems = useMemo(() => generateRandomItems(), []);

    const getItemKey = useCallback(
        (item: MockItem) => item.id,
        [],
    );

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case "high":
                return "error";
            case "medium":
                return "warning";
            case "low":
                return "success";
            default:
                return "default";
        }
    };

    const renderExpanded = useCallback((item: MockItem, handleToggle: (item: MockItem) => void) => {
        return (
            <Paper
                sx={{
                    p: 2,
                    m: 1,
                    cursor: "pointer",
                    border: "1px solid",
                    borderColor: "divider",
                    "&:hover": {
                        borderColor: "primary.main",
                        boxShadow: 1,
                    },
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                    }}
                >
                    <Typography variant="h6" component="h3" sx={{ flex: 1 }}>
                        {item.title}
                    </Typography>
                    <Chip
                        label={item.priority}
                        color={getPriorityColor(item.priority) as any}
                        size="small"
                        sx={{ ml: 1 }}
                    />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {item.description}
                </Typography>

                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                    }}
                >
                    <Chip label={item.category} variant="outlined" size="small" />
                    <Typography variant="caption" color="text.secondary">
                        ID: {item.id}
                    </Typography>
                </Box>

                <Box
                    sx={{ mt: 2, pt: 2, borderTop: "1px solid", borderColor: "divider" }}
                >
                    <Typography variant="body2">
                        This is the expanded content for item {item.id}. You can see additional
                        details here. The content can be much longer and include various UI
                        elements like forms, tables, or other components.
                    </Typography>
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ mt: 1 }}
                        onClick={(e) => {
                            e.stopPropagation();
                            handleToggle(item);
                        }}
                    >
                        Collapse
                    </Button>
                </Box>
            </Paper>
        );
    }, []);

    const renderCollapsed = useCallback((item: MockItem, handleToggle: (item: MockItem) => void) => {
        return (
            <Paper
                sx={{
                    p: 2,
                    m: 1,
                    cursor: "pointer",
                    border: "1px solid",
                    borderColor: "divider",
                    "&:hover": {
                        borderColor: "primary.main",
                        boxShadow: 1,
                    },
                }}
                onClick={() => handleToggle(item)}
            >
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                    }}
                >
                    <Typography variant="h6" component="h3" sx={{ flex: 1 }}>
                        {item.title}
                    </Typography>
                    <Chip
                        label={item.priority}
                        color={getPriorityColor(item.priority) as any}
                        size="small"
                        sx={{ ml: 1 }}
                    />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {item.description}
                </Typography>

                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                    }}
                >
                    <Chip label={item.category} variant="outlined" size="small" />
                    <Typography variant="caption" color="text.secondary">
                        ID: {item.id}
                    </Typography>
                </Box>
            </Paper>
        );
    }, []);

    const handleScrollToIndex = useCallback(() => {
        const index = parseInt(scrollIndex, 10);
        if (!isNaN(index) && index >= 0 && index < props.itemsCount) {
            eventBus.emit("virtualListScrollToIndexEvent", { index });
        }
    }, [scrollIndex, props.itemsCount]);

    const handleKeyPress = useCallback(
        (event: React.KeyboardEvent) => {
            if (event.key === "Enter") {
                handleScrollToIndex();
            }
        },
        [handleScrollToIndex],
    );

    const handleRandomScroll = useCallback((buttonIndex: number) => {
        const randomIndex = Math.floor(Math.random() * props.itemsCount);
        setRandomIndexes(prev => {
            const newIndexes = [...prev];
            newIndexes[buttonIndex] = randomIndex;
            return newIndexes;
        });
        eventBus.emit("virtualListScrollToIndexEvent", { index: randomIndex });
    }, [props.itemsCount]);

    const handleRandomScrolls = useCallback(() => {
        const newRandomIndexes = Array.from({ length: 5 }, () =>
            Math.floor(Math.random() * props.itemsCount)
        );
        setRandomIndexes(newRandomIndexes);

        newRandomIndexes.forEach((index, i) => {
            setTimeout(() => {
                eventBus.emit("virtualListScrollToIndexEvent", { index });
            }, i * 200); // Stagger the scrolls by 200ms
        });
    }, [props.itemsCount]);

    return (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Stack direction="row" spacing={2} alignItems="center">
                <TextField
                    label="Scroll to Index"
                    type="number"
                    value={scrollIndex}
                    onChange={(e) => setScrollIndex(e.target.value)}
                    onKeyPress={handleKeyPress}
                    inputProps={{ min: 0, max: props.itemsCount - 1 }}
                    size="small"
                    sx={{ width: 150 }}
                />
                <Button
                    variant="contained"
                    onClick={handleScrollToIndex}
                    disabled={!scrollIndex || isNaN(parseInt(scrollIndex, 10))}
                >
                    Scroll to Item
                </Button>
                <Typography variant="body2" color="text.secondary">
                    (0 - {props.itemsCount - 1})
                </Typography>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                    Quick Scroll:
                </Typography>
                {Array.from({ length: 5 }, (_, i) => (
                    <Button
                        key={i}
                        variant="outlined"
                        size="small"
                        onClick={() => handleRandomScroll(i)}
                        sx={{ minWidth: 'auto', px: 1 }}
                    >
                        {randomIndexes[i]}
                    </Button>
                ))}
                <Button
                    variant="outlined"
                    size="small"
                    onClick={handleRandomScrolls}
                    sx={{ ml: 1 }}
                >
                    Random Sequence
                </Button>
            </Stack>

            <Box
                sx={{ border: "1px solid", borderColor: "divider", borderRadius: 1 }}
            >
                <VirtualizedList
                    {...props}
                    getItemKey={getItemKey}
                    renderExpanded={renderExpanded}
                    renderCollapsed={renderCollapsed}
                    items={randomItems}
                />
            </Box>
        </Box>
    );
}

const meta: Meta<typeof VirtualizedListWithScrollForm> = {
    title: "Components/VirtualizedList",
    component: VirtualizedListWithScrollForm,
    parameters: {
        layout: "centered",
        docs: {
            description: {
                component:
                    "A virtualized list component that efficiently renders large datasets by only rendering visible items. Uses renderExpanded and renderCollapsed functions to render content.",
            },
        },
    },
    argTypes: {
        estimatedRowHeight: {
            control: { type: "number", min: 50, max: 500, step: 10 },
            description: "Estimated height of each row in pixels",
        },
        containerHeight: {
            control: { type: "number", min: 200, max: 1000, step: 50 },
            description: "Height of the container in pixels",
        },
        containerWidth: {
            control: { type: "number", min: 200, max: 1000, step: 50 },
            description: "Width of the container in pixels",
        },
        itemsCount: {
            control: { type: "number", min: 1, max: 1000, step: 1 },
            description: "Total number of items in the list",
        },
    },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    render: (args) => <VirtualizedListWithScrollForm {...args} />,
    args: {
        estimatedRowHeight: 120,
        containerHeight: 800,
        containerWidth: 600,
        itemsCount: 500,
    },
};

export const WithCustomHeight: Story = {
    ...Default,
    args: {
        ...Default.args,
        estimatedRowHeight: 80,
        containerHeight: 600,
    },
};

export const WithCustomWidth: Story = {
    ...Default,
    args: {
        ...Default.args,
        containerWidth: 800,
    },
};

export const SmallDataset: Story = {
    ...Default,
    args: {
        ...Default.args,
        itemsCount: 50,
    },
};

export const LargeDataset: Story = {
    ...Default,
    args: {
        ...Default.args,
        itemsCount: 500,
    },
};

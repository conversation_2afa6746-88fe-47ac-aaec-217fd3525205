import React, { useCallback, useRef, useState, useMemo, useEffect } from 'react'

import { useVirtualizer } from '@tanstack/react-virtual'
import VirtualizedAccordion from './VirtualizedAccordion';
import { eventBus, EventBusEvents } from '~/lib/eventBus';

export type VirtualizedListProps<ItemType extends { id: string }> = {
    estimatedRowHeight: number;
    containerHeight: number | string;
    containerWidth: number | string;
    itemsCount: number;
    getItemKey: (item: ItemType) => string;
    renderExpanded: (item: ItemType, handleToggle: (item: ItemType) => void, isActive: boolean) => React.ReactNode;
    renderCollapsed: (item: ItemType, handleToggle: (item: ItemType) => void, isActive: boolean) => React.ReactNode;
    items: ItemType[];
    overscan?: number;
}

export function VirtualizedList<ItemType extends { id: string }>({
    estimatedRowHeight,
    containerWidth,
    containerHeight,
    itemsCount,
    getItemKey,
    renderExpanded,
    renderCollapsed,
    items,
    overscan = 1,
}: VirtualizedListProps<ItemType>) {
    const parentRef = useRef<HTMLDivElement>(null);
    const expandedIdRef = useRef<string>("");
    const activeItemRef = useRef<string>("");
    const [, forceUpdate] = useState({});
    const virtualizerOptions = {
        count: itemsCount,
        getScrollElement: () => parentRef.current,
        estimateSize: () => estimatedRowHeight,
        overscan,
        getItemKey: (index: number) => getItemKey(items[index]!),
    }

    const virtualizer = useVirtualizer(virtualizerOptions);
    const virtualItems = virtualizer.getVirtualItems();

    const handleToggle = useCallback((item: ItemType) => {
        expandedIdRef.current = expandedIdRef.current === item.id ? "" : item.id;
        activeItemRef.current = item.id;
        forceUpdate({});
    }, []);

    const scrollToIndex = useCallback((index: number) => {
        virtualizer.scrollToIndex(index, {
            align: 'start',
        });
    }, [virtualizer]);

    const handleScrollToIndexEvent = useCallback((event: EventBusEvents["virtualListScrollToIndexEvent"]) => {
        handleToggle(items[event.index]!);
        virtualizer.measure();
        setTimeout(() => {
            scrollToIndex(event.index);
        }, 10);
    }, [scrollToIndex, handleToggle, items, virtualizer]);

    useEffect(() => {
        eventBus.on("virtualListScrollToIndexEvent", handleScrollToIndexEvent);
        return () => {
            eventBus.off("virtualListScrollToIndexEvent", handleScrollToIndexEvent);
        };
    }, [handleScrollToIndexEvent]);

    return (
        <div
            ref={parentRef}
            style={{
                overflowY: "auto",
                height: containerHeight,
                width: containerWidth,
                contain: 'strict'
            }}
            data-testid="virtualized-list-container"
            className="virtualized-list-container"
            id="virtualized-list-container"
        >
            <div
                style={{
                    height: virtualizer.getTotalSize(),
                    width: '100%',
                    position: 'relative',
                }}
            >
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        transform: `translateY(${virtualItems[0]?.start ?? 0}px)`,
                    }}
                >
                    {virtualItems.map((virtualRow) => {
                        return (
                            <VirtualizedAccordion
                                measureElement={virtualizer.measureElement}
                                key={getItemKey(items[virtualRow.index]!)}
                                index={virtualRow.index}
                                itemId={getItemKey(items[virtualRow.index]!)}
                            >
                                {expandedIdRef.current === getItemKey(items[virtualRow.index]!) ?
                                    renderExpanded(items[virtualRow.index]!, handleToggle, items[virtualRow.index]!.id === activeItemRef.current) :
                                    renderCollapsed(items[virtualRow.index]!, handleToggle, items[virtualRow.index]!.id === activeItemRef.current)}
                            </VirtualizedAccordion>
                        );
                    })}
                </div>
            </div>
        </div>)
}
import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { VirtualizedList } from './VirtualizedList';
import { faker } from '@faker-js/faker';
import { eventBus } from '~/lib/eventBus';

export type ItemType = {
    id: string;
    name: string;
    text: string;
};

describe('VirtualizedList', () => {
    const mockItems: ItemType[] = [
        { id: '1', name: 'Item 1', text: faker.lorem.paragraphs(1) },
        { id: '2', name: 'Item 2', text: faker.lorem.paragraphs(2) },
        { id: '3', name: 'Item 3', text: faker.lorem.paragraphs(1) },
        { id: '4', name: 'Item 4', text: faker.lorem.paragraphs(1) },
        { id: '5', name: 'Item 5', text: faker.lorem.paragraphs(2) },
        { id: '6', name: 'Item 6', text: faker.lorem.paragraphs(1) },
        { id: '7', name: 'Item 7', text: faker.lorem.paragraphs(1) },
        { id: '8', name: 'Item 8', text: faker.lorem.paragraphs(1) },
        { id: '9', name: 'Item 9', text: faker.lorem.paragraphs(2) },
    ];

    const defaultProps = {
        overscan: 0,
        estimatedRowHeight: 47,
        containerHeight: 140,
        containerWidth: 400,
        itemsCount: mockItems.length,
        getItemKey: (item: ItemType) => item.id,
        renderExpanded: (item: ItemType, handleToggle: (item: ItemType) => void) => (
            <div data-testid={`expanded-${item.id}`}>
                Expanded: {item.text}
                <button data-testid={`toggle-${item.id}`} onClick={() => handleToggle(item)}>Toggle</button>
            </div>
        ),
        renderCollapsed: (item: ItemType, handleToggle: (item: ItemType) => void) => (
            <div data-testid={`collapsed-${item.id}`} onClick={() => handleToggle(item)}>
                Collapsed: {item.name}
            </div>
        ),
        items: mockItems,
    };

    beforeEach(() => {
        jest.clearAllMocks();
        Object.defineProperty(Element.prototype, 'getBoundingClientRect', {
            value: jest.fn(() => ({
                width: 400,
                height: 150, // This should match containerHeight from defaultProps
                top: 0,
                left: 0,
                bottom: 150,
                right: 400,
                x: 0,
                y: 0,
                offsetHeight: 150,
                offsetWidth: 400,
                toJSON: () => { },
            })),
            writable: true,
            configurable: true,
        });
    });

    it('renders without crashing', () => {
        render(<VirtualizedList {...defaultProps} />);
        expect(screen.getByTestId('virtualized-list-container')).toBeInTheDocument();
    });

    it('renders virtual items', async () => {
        render(<VirtualizedList {...defaultProps} />);
        expect(screen.getByTestId('virtualized-list-container')).toBeInTheDocument();
    });

    it('renders virtual items with overscan 0', async () => {
        render(<VirtualizedList {...defaultProps} overscan={0} />);

        expect(screen.getByTestId('virtualized-list-container')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-1')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-2')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-3')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-4')).not.toBeInTheDocument();
    });

    it('renders virtual items with different overscan', async () => {
        render(<VirtualizedList {...defaultProps} overscan={2} />);

        expect(screen.getByTestId('virtualized-list-container')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-1')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-2')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-3')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-4')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-5')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-6')).not.toBeInTheDocument();
    });
    it('expands on click', async () => {
        render(<VirtualizedList {...defaultProps} />);
        const collapsed = screen.getByTestId('collapsed-1');
        fireEvent.click(collapsed);
        expect(screen.getByTestId('expanded-1')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-1')).not.toBeInTheDocument();
        const collapseBtn = screen.getByTestId('toggle-1');
        fireEvent.click(collapseBtn);
        expect(screen.getByTestId('collapsed-1')).toBeInTheDocument();
        expect(screen.queryByTestId('expanded-1')).not.toBeInTheDocument();
    });

    it('expands on click, one at the time', async () => {
        render(<VirtualizedList {...defaultProps} />);
        const collapsed = screen.getByTestId('collapsed-1');
        fireEvent.click(collapsed);
        expect(screen.getByTestId('expanded-1')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-1')).not.toBeInTheDocument();
        const collapsed2 = screen.getByTestId('collapsed-2');
        fireEvent.click(collapsed2);
        expect(screen.getByTestId('expanded-2')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-2')).not.toBeInTheDocument();
        const collapsed3 = screen.getByTestId('collapsed-3');
        fireEvent.click(collapsed3);
        expect(screen.getByTestId('expanded-3')).toBeInTheDocument();
        expect(screen.queryByTestId('collapsed-3')).not.toBeInTheDocument();
    });
    // since JSDON doesnt support layout, we cant test scroll position and visibility
});

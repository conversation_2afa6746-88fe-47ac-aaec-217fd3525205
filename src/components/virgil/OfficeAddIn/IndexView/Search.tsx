import { debounce } from "@mui/material";

import { Input } from "~/v2/components/ui/Input";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setSearchDebounced, setSearchInput } from "~/lib/features/documentDDQSlice";
import { selectSearchInput } from "~/lib/features/stateSelecrors";

const DEBOUNCE_TIME = 300;
export const Search: React.FC = () => {
    const dispatch = useDispatch();
    const searchInput = useSelector(selectSearchInput);
    const debouncedSearch = debounce((value: string) => {
        dispatch(setSearchDebounced(value.toLowerCase().trim()));
    }, DEBOUNCE_TIME);

    useEffect(() => {
        return () => {
            debouncedSearch.clear();
        };
    }, []);

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        dispatch(setSearchInput(event.target.value));
        debouncedSearch(event.target.value);
    };

    const handleClearSearch = (e: React.MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
        dispatch(setSearchInput(""));
        dispatch(setSearchDebounced(""));
    };

    return (
        <Input
            value={searchInput}
            onChange={handleSearchChange}
            placeholder="Search"
            className="w-full bg-white"
        />
    );
}

import InsertButtonGroup from "../InsertButtonGroup"
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager"
import parseQuestionData from "./parseQuestionData";
import useRegenerateBtn from "../../../hooks/useRegenerateBtn";
import { setSelectedQuestionId, setSelectedQuestionText } from "~/lib/features/documentDDQSlice";
import { useCallback, useMemo } from "react";
import { setDisplayDetailedAnswer, setDisplayIndexView } from "~/lib/features/addInSlice";
import { useDispatch } from "react-redux";
import { UserCollaboration } from "../../UserCollaboration";
import useDisplaySimilarAction from "./useDisplaySimilarAction";
import { ActionButton } from "~/v2/components/composit/ActionButton";
import { Button } from "~/v2/components/ui";

type ActionButtonsProps = {
    question: DDQuestionWithFeedback;
    view: "detailed" | "index";
}

export const useActionButtons = ({
    question,
    view = "index"
}: ActionButtonsProps) => {
    const { responseText, responseStatus, responseReason, responseCanonicalId, documentId, questionId } = parseQuestionData(question);
    const { regenerateBtn, isPending: isGenerating } = useRegenerateBtn({
        questionId,
        documentId,
    });
    const displaySimilarPopup = useDisplaySimilarAction({
        questionId,
    });

    const dispatch = useDispatch();
    const handleDetailedView = useCallback(
        () => {
            dispatch(setDisplayDetailedAnswer());
            dispatch(setSelectedQuestionId(question.id));
            dispatch(setSelectedQuestionText(question.text));
        },
        [
            question.text,
            question.id,
            dispatch,
            setDisplayDetailedAnswer,
            setSelectedQuestionId,
        ],
    );
    const navigateToIndexView = useCallback(() => {
        dispatch(setDisplayIndexView());
    }, [dispatch, setDisplayIndexView]);

    const ActionButtonsMemo = useMemo(() => (
        <div className="flex gap-1">
            <InsertButtonGroup
                responseText={responseText}
                question={question}
                disabled={!responseText || !responseStatus}
            />
            {view === "index" ? <ActionButton
                variant="outline"
                options={[
                    {
                        label: "View details",
                        action: () => handleDetailedView(),
                    },
                    {
                        label: "View similar",
                        action: () => displaySimilarPopup?.(),
                    }
                ]}
                disabled={!responseText.trim()}
                label="View"
            /> : <Button
                variant="outline"
                onClick={navigateToIndexView}
            >
                Close
            </Button>
            }
            <div className="ml-auto flex gap-1">
                {regenerateBtn}
                <UserCollaboration questionId={question.id} responseId={responseCanonicalId} />
            </div>
        </div>
    ), [responseText, responseStatus, responseReason, responseCanonicalId, question, regenerateBtn, handleDetailedView, displaySimilarPopup, navigateToIndexView, view]);
    return {
        ActionButtonsMemo,
        isGenerating,
    }
}

export default useActionButtons;
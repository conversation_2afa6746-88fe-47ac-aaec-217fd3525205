import { useCallback } from "react";
import { useBaseUrl } from "../../../hooks/useBaseUrl";
import { useSelector } from "react-redux";
import { selectAddInMode } from "~/lib/features/addInSelectors";
import { openResponseLibrary } from "../../addInUtils/common";

export default function useDisplaySimilarAction({
  questionId,
}: {
  questionId: string;
}) {
  const orgDomain = useBaseUrl();
  const mode = useSelector(selectAddInMode);

  const callback = useCallback(() => {
    if (!orgDomain || !questionId) {
      return;
    }

    openResponseLibrary({
      domain: orgDomain,
      previewHeight: 50,
      previewWidth: 50,
      questionId,
      mode,
    });
  }, [orgDomain, questionId, mode]);

  return callback;
}

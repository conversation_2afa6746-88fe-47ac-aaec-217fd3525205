import React, { memo, useCallback, useMemo } from "react";
import { AnswerGenerationType, QuestionStatusType } from "@prisma/client";
import { convertMarkdownToPreview } from "../../markdownUtils/markdownUtils";
import parseQuestionData from "./parseQuestionData";
import { DDQuestionWithIndexAndFeedback } from "~/server/api/managers/questionManager";
import AnsweredIcon from "../../../Icons/AnsweredIcon";
import { isResponseValid } from "../../isResponseValid";
import { cn } from "~/v2/lib/utils";
import { Typography, Checkbox } from "~/v2/components";
import {
  AIGeneratedResponseIcon,
  InsufficientDataIcon,
  ResponseLibraryResponseIcon,
  NoResponseIcon,
} from "~/components/virgil/Icons";

type CollapsedContentProps = {
  isChecked: boolean;
  setIsChecked: (questionId: string) => void;
  question: DDQuestionWithIndexAndFeedback;
  handleToggle: (item: DDQuestionWithIndexAndFeedback) => void;
  isActive: boolean;
};
export const CollapsedContent = memo(
  ({
    setIsChecked,
    isChecked,
    question,
    handleToggle,
    isActive,
  }: CollapsedContentProps) => {
    const {
      questionText,
      questionId,
      answerGenerationType,
      responseText,
      insufficientData,
    } = parseQuestionData(question);
    const SourceResponseIcon = useMemo(() => {
      if (question.status === QuestionStatusType.ANSWERED) {
        return <AnsweredIcon />;
      }
      if (insufficientData) {
        return <InsufficientDataIcon />;
      }
      if (
        !isResponseValid(responseText)
      ) {
        return <NoResponseIcon />;
      }
      if (answerGenerationType === AnswerGenerationType.EXTRACTED) {
        return <ResponseLibraryResponseIcon />;
      }
      if (answerGenerationType === AnswerGenerationType.GENERATED) {
        return <AIGeneratedResponseIcon />;
      }

      return null;
    }, [answerGenerationType, question.status, responseText]);
    const handleExpandClick = useCallback(
      (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        handleToggle(question);
      },
      [handleToggle, question],
    );
    return (
      <div
        onClick={handleExpandClick}
        data-testid={`collapsed-${question.id}`}
        className={cn(
          "w-full",
          "last:border-b-0",
          "first:border-t-1 first:border-t-[var(--border)]",
          "border-b border-b-[1px] border-b-[var(--border)]",
          "relative",
          "hover:bg-[var(--colors-component-selected)]",
          "active:bg-[var(--colors-component-selected)]",
          "flex",
          "p-[var(--spacing-2)]",
          "gap-x-[var(--spacing-2)]",
          "relative",
          "hover:before:content-['']",
          "hover:before:absolute",
          "hover:before:top-0",
          "hover:before:left-0",
          "hover:before:w-[var(--spacing-1)]",
          "hover:before:h-full",
          "hover:before:bg-[var(--primary)]",
          isActive ? "active" : ""
        )}
      >
        <Checkbox
          checked={isChecked}
          onCheckedChange={(checked) => {
            setIsChecked(questionId);
          }}
          onClick={(e) => e.stopPropagation()}
        />
        <div>
          <Typography
            variant="boldBody"
            style={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: 1,
              WebkitBoxOrient: "vertical",
              paddingRight: 4,
              cursor: "pointer",
            }}
          >
            {questionText}
          </Typography>
          <Typography
            variant="body"
            color="text.secondary"
            className="overflow-hidden text-ellipsis"
            style={{
              display: "-webkit-box",
              WebkitLineClamp: 1,
              WebkitBoxOrient: "vertical",
            }}
          >
            {convertMarkdownToPreview(responseText)}
          </Typography>
        </div>
        <div className="ml-auto">
          {SourceResponseIcon}
        </div>
      </div>
    );
  },
);

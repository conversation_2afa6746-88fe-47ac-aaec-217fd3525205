import { QuestionStatusType } from "@prisma/client";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";

export default function parseQuestionData(question: DDQuestionWithFeedback) {
  const { text: questionText, id: questionId, response } = question;
  const answerGenerationType =
    response?.responseContents?.[0]?.answerGenerationType;

  const responseContent = response?.responseContents?.[0]?.content as
    | { reason?: string; text?: string; insufficientData?: boolean }
    | undefined;
  const responseReason = responseContent?.reason ?? "";
  const responseStatus = response?.status;
  const responseText = responseContent?.text ?? "";
  const responseCanonicalId = response?.id ?? "";
  const responseId = response?.responseContents?.[0]?.id ?? "";
  const documentId = response?.documents[0]?.documentId ?? "";
  const insufficientData = responseContent?.insufficientData ?? false;

  return {
    questionText,
    questionId,
    answerGenerationType,
    responseReason,
    responseStatus,
    responseText,
    responseId,
    responseCanonicalId,
    documentId,
    insufficientData,
    questionStatus: question.status,
    isAnswered: question.status === QuestionStatusType.ANSWERED,
  };
}

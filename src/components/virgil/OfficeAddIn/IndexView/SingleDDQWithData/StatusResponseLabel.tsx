
import { ResponseStatus } from "@prisma/client";
import { DraftStatusLabel } from "../../../Labels/DraftStatusLabel";
import { PendingReviewStatusLabel } from "../../../Labels/PendingReviewStatusLabel";
import { ApprovedStatusLabel } from "../../../Labels/ApprovedStatusLabel";
import { RejectedStatusLabel } from "../../../Labels/RejectedStatusLabel";

export const StatusResponseLabel = ({ status }: { status?: ResponseStatus }) => {
    switch (status) {
        case ResponseStatus.DRAFT:
            return <DraftStatusLabel />;
        case ResponseStatus.PENDING_APPROVAL:
            return <PendingReviewStatusLabel />;
        case ResponseStatus.APPROVED:
            return <ApprovedStatusLabel />;
        case ResponseStatus.REJECTED:
            return <RejectedStatusLabel />;
        default:
            return null;
    }
}
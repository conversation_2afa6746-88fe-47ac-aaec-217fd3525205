import { ResponseLibraryResponseLabel } from "../../../Labels/ResponseLibraryResponseLabel";
import { AIGeneratedResponseLabel } from "../../../Labels/AIGeneratedResponseLabel";
import { AnswerGenerationType } from "@prisma/client";

export const SourceResponseLabel = ({ answerGenerationType }: { answerGenerationType?: AnswerGenerationType | null }) => {
    switch (answerGenerationType) {
        case AnswerGenerationType.EXTRACTED:
            return <ResponseLibraryResponseLabel />;
        case AnswerGenerationType.GENERATED:
            return <AIGeneratedResponseLabel />;
        default:
            return null;
    }
}
import React from "react";
import { Box, Tab, Tabs } from "@mui/material";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { selectTabType } from "~/lib/features/documentDDQSelector";
import { setTabType, TabType } from "~/lib/features/documentDDQSlice";
import { useDDQuestionsCount } from "../../hooks/useDDQuestionsCount";

export const TabHeaders: React.FC = () => {
    const tabType = useSelector(selectTabType);
    const { totalQuestions } = useDDQuestionsCount();
    const dispatch = useDispatch();

    const handleTabChange = (event: React.SyntheticEvent, newValue: TabType) => {
        event.preventDefault();
        dispatch(setTabType(newValue));
    };

    return (
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
                value={tabType}
                onChange={handleTabChange}
                variant="fullWidth"
                aria-label="detailed answer tabs"
                sx={{
                    minHeight: '30px',
                    height: '30px',
                    '& .MuiTabs-indicator': {
                        backgroundColor: 'primary.main'
                    },
                    '& .Mui-selected.MuiTab-root': {
                        color: 'primary.main'
                    },
                }}
            >
                <Tab label={`${typeof totalQuestions === "number" ? `${totalQuestions} Questions` : ''}`} value={"questions"} sx={{ height: '30px', minHeight: '30px' }} />
            </Tabs>
        </Box>
    )
}
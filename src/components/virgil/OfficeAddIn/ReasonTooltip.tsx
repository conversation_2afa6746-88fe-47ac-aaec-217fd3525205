import { Typography } from "@mui/material"
import { Box } from "@mui/material"
import { HtmlTooltip } from "../HtmlTooltip"
import { CONFIG } from 'src/config-global';
import { Markdown } from "./Markdown";

export const ReasonTooltip = ({ reasonText, children }: { reasonText: string, children: React.ReactNode }) => {
    return (
        <HtmlTooltip placement="top" title={
            <Box sx={{ p: 1 }}>
                <Box sx={{ display: "flex", alignItems: "flex-end", mb: 1, gap: 1 }}>
                    <Box
                        alt="logo"
                        component="img"
                        src={`${CONFIG.site.basePath}/logo/virgil-square-tr-80.png`}
                        width={15}
                        height={13}
                        sx={{ display: 'block' }}
                    />
                    <Typography variant="h6" color="text.secondary" sx={{ lineHeight: 1 }}>Virgil Reason<PERSON></Typography>
                </Box>
                <Typography variant="body2" color="text.secondary"><Markdown markdown={reasonText} /></Typography>
            </Box>
        }>
            <Box>
                {children}
            </Box>
        </HtmlTooltip>
    )
}
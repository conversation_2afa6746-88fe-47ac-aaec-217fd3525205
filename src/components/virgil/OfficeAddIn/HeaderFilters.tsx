"use client";

import { api } from "~/trpc/react";
import { useAuth } from "@clerk/nextjs";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { DDQFilter } from "./DDQFilter";

export const HeaderFilters = () => {
    const { orgId } = useAuth();
    const documentId = useSelector(selectDocumentId);
    const tags = api.tag.getAllTags.useQuery(undefined, {
        enabled: !!orgId,
    });

    const funds = api.fund.getAllFunds.useQuery(undefined, {
        enabled: !!orgId,
    });

    if (!tags.data || !funds.data || !documentId) return null;

    return <DDQFilter
        tags={tags.data}
        funds={funds.data}
    />
};

"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { QuestionCategory, ResponseStatus } from "@prisma/client";
import { JsonObject } from "@prisma/client/runtime/library";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { TransitionGroup } from "react-transition-group";
import { QuestionDetails } from "~/views/response/QuestionDetails";
import type { QuestionWithSearchType } from "~/server/api/routers/question.common";
import { api } from "~/trpc/react";
import Loading from "../dashboard/loading";
import { QuestionContentType } from "~/lib/types";
import { toast } from "~/components/snackbar";

function ResponseLibraryView({ questionId }: { questionId: string }) {
  const updateResponseById = api.response.updateResponseById.useMutation();

  const question = api.question.getQuestionById.useQuery({
    id: questionId,
  });

  const response = api.response.getResponseById.useQuery({
    id: question.data?.responseId ?? "",
  });
  const query = (question.data?.questionContents[0]?.content as JsonObject)
    ?.text as string;

  const { data: questions, isLoading: isLoadingQuestions } =
    api.question.getAllQuestions.useQuery(
      {
        category: [
          QuestionCategory.OTHER,
          QuestionCategory.DATA_ASSURANCE,
          QuestionCategory.STANDARDS_AND_FRAMEWORKS,
          QuestionCategory.INVESTMENT_PROCESS,
        ],
        search: query,
        startDate: undefined,
        endDate: undefined,
        ddq: [],
        tagIds: [],
        fundIds: [],
        page: 0,
        pageSize: 10,
        responseStatus: [ResponseStatus.APPROVED],
      },
      {
        enabled: !!question.data,
      },
    );

  const searchedQuestions =
    (questions?.questions as (QuestionWithSearchType & {
      group: number | undefined;
    })[]) ?? [];

  const [visibleGroup, setVisibleGroup] = useState<string | null>(null);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(
    null,
  );
  const [selectingResponse, setSelectingResponse] = useState<boolean>(false);

  if (isLoadingQuestions) {
    return <Loading />;
  }

  const groupedQuestions = searchedQuestions.reduce(
    (acc, question, index) => {
      if (question.group === undefined) {
        return acc;
      }

      const group = question.group
        ? `G-${question.group}`
        : `NG-${question.id}`;
      const questionText =
        (question.questionContents[0]?.content as QuestionContentType).text ??
        "";

      if (!acc[group]) {
        acc[group] = {
          id: group,
          name: questionText,
          questionIds: [],
        };
      }

      acc[group].questionIds.push(question.id);

      return acc;
    },
    {} as Record<string, { id: string; name: string; questionIds: string[] }>,
  );

  const selectResponse = (text: string, questionId: string) => {
    if (!question.data?.responseId) {
      toast.error("Question has no response");
      return;
    }

    const responseContentId = response?.data?.responseContents[0]?.id;
    if (!responseContentId) {
      toast.error("Response has no response content");
      return;
    }

    setSelectingResponse(true);
    setSelectedQuestionId(questionId);

    updateResponseById.mutate(
      {
        id: responseContentId,
        responseText: text,
      },
      {
        onSuccess: () => {
          toast.success("Response updated successfully");
        },
        onError: (error: any) => {
          toast.error("Error updating response", error.message);
          setSelectedQuestionId(null);
        },
        onSettled: () => {
          setSelectingResponse(false);
        },
      },
    );
  };

  return (
    <Stack alignItems="left" spacing={0} sx={{ width: 1, height: "100%" }}>
      <div
        style={{
          marginBottom: "2px",
          paddingLeft: "15px",
          paddingTop: "10px",
          paddingBottom: "10px",
          paddingRight: "10px",
          border: "1px solid #e0e0e0",
          backgroundColor: "grey.800",
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold", color: "grey.800" }}>
          Response Library results for:{" "}
          <span style={{ fontWeight: 200, color: "grey.800" }}>
            &quot;
            {query}
            &quot;{" "}
          </span>
        </Typography>
      </div>
      <Box
        gap={2}
        display="grid"
        gridTemplateColumns={{
          xs: "repeat(1, 1fr)",
          sm: "repeat(1, 1fr)",
          md: "repeat(1, 1fr)",
        }}
        sx={{
          width: 1,
          overflow: "auto",
          maxHeight: "100%",
          padding: 2,
          backgroundColor: "grey.200",
        }}
      >
        <TransitionGroup>
          {Object.entries(groupedQuestions ?? {}).map(([key, group], index) => {
            const firstQuestionId = group.questionIds[0]!;
            const firstQuestion = searchedQuestions?.find(
              (q) => q.id === firstQuestionId,
            ) as QuestionWithSearchType;

            const questionContent = firstQuestion?.response?.responseContents[0]
              ?.content as QuestionContentType;
            const text = questionContent.text;

            return (
              <>
                <QuestionDetails
                  key={key}
                  question={firstQuestion}
                  expanded={visibleGroup}
                  setExpanded={setVisibleGroup}
                  tagEntityConnections={[]}
                  search={group.name}
                  accordion={true}
                  index={index}
                  similarQuestionCount={group.questionIds.length - 1}
                  allowEdit={false}
                  actions={
                    <Button
                      loading={
                        selectingResponse &&
                        selectedQuestionId === firstQuestion.id
                      }
                      disabled={
                        !text?.length || selectedQuestionId === firstQuestion.id
                      }
                      key="use-this-response"
                      size="small"
                      variant="outlined"
                      color="primary"
                      onClick={() => {
                        if (text?.length) {
                          selectResponse(text, firstQuestion.id);
                        }
                      }}
                    >
                      Use this response
                    </Button>
                  }
                />

                {group.questionIds
                  .slice(1)
                  .map((id: string, innerIndex: number) => {
                    const q = searchedQuestions?.find(
                      (q) => q.id === id,
                    ) as QuestionWithSearchType;

                    const questionContent = firstQuestion?.response
                      ?.responseContents[0]?.content as QuestionContentType;
                    const text = questionContent.text;

                    return (
                      <Collapse
                        in={visibleGroup === firstQuestionId}
                        key={q.id}
                      >
                        <Box sx={{ p: 1 }}>
                          <QuestionDetails
                            key={q.id}
                            question={q}
                            tagEntityConnections={[]}
                            search={
                              (q.questionContents[0]?.content as JsonObject)
                                ?.text as string
                            }
                            accordion={true}
                            index={innerIndex + index}
                            allowEdit={false}
                            actions={
                              <Button
                                loading={
                                  selectingResponse &&
                                  selectedQuestionId === q.id
                                }
                                disabled={
                                  !text?.length || selectedQuestionId === q.id
                                }
                                key="use-this-response"
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={() => {
                                  if (text?.length) {
                                    selectResponse(text, q.id);
                                  }
                                }}
                              >
                                Use this response
                              </Button>
                            }
                          />
                        </Box>
                      </Collapse>
                    );
                  })}
              </>
            );
          })}
        </TransitionGroup>
      </Box>
    </Stack>
  );
}

export default function Page() {
  // Get documentId from URL
  const questionId = useSearchParams().get("questionId");
  if (!questionId) {
    return <div>No question ID provided</div>;
  }
  return <ResponseLibraryView questionId={questionId} />;
}

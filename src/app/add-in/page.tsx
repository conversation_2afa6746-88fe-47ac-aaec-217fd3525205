"use client";

import React from "react";
import { useSelector } from "react-redux";
import AddInRootExcel from "~/components/virgil/AddInRootExcel";
import AddInRootWord from "~/components/virgil/AddInRootWord";
import { selectHostType } from "~/lib/features/addInSelectors";
import { EXCEL_HOST_TYPE, WORD_HOST_TYPE } from "~/lib/features/addInSlice";

export default function Page() {
  const hostType = useSelector(selectHostType);

  switch (hostType) {
    case WORD_HOST_TYPE:
      return <AddInRootWord />;
    case EXCEL_HOST_TYPE:
      return <AddInRootExcel />;
    default:
      return null;
  }
}

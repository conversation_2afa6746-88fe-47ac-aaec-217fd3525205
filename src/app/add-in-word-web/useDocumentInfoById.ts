import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { api } from "~/trpc/react";
import { setDocumentMetadata } from "~/lib/features/documentMetadataSlice";
import {
  setAddInNavigationPath,
  setDocumentId,
} from "~/lib/features/addInSlice";
import { DocumentStatus as DocumentStatusEnum } from "@prisma/client";
import { selectDocumentId } from "~/lib/features/addInSelectors";

// This hook should be used in the browser and not in the office add-in
export const useDocumentInfoById = () => {
  const dispatch = useDispatch();
  const documentId = useSelector(selectDocumentId);

  const {
    data: documentInfoById,
    isSuccess: isSuccessById,
    isError: isErrorById,
  } = api.document.getDocumentById.useQuery(
    { id: documentId ?? "" },
    {
      enabled: !!documentId,
    },
  );

  useEffect(() => {
    if (documentInfoById && isSuccessById) {
      dispatch(setDocumentId(documentInfoById.id));
      dispatch(
        setDocumentMetadata({
          createdByName: "Created by Demo User",
          summary: documentInfoById.summary ?? "",
          createdAt: documentInfoById.createdAt.toLocaleDateString(),
          dueDate: documentInfoById.dueDate?.toISOString() ?? "",
          title: documentInfoById?.title ?? "",
          documentStatus: documentInfoById.status,
        }),
      );
      if (
        documentInfoById.status !== DocumentStatusEnum.READY &&
        documentInfoById.status !== DocumentStatusEnum.GENERATING_ANSWERS
      ) {
        dispatch(setAddInNavigationPath("document-status"));
      } else {
        dispatch(setAddInNavigationPath("ddq"));
      }
    } else {
      dispatch(setAddInNavigationPath("document-status"));
    }
  }, [documentInfoById, isSuccessById, isErrorById]);
};

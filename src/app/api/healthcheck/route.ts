import type { NextRequest } from "next/server";
import { env } from "~/env";
import { db as prisma } from "~/server/db";
import * as Sentry from "@sentry/nextjs";

type HealthCheck = {
  db: "ok" | "error";
  azureAccessToken: "ok" | "error";
  azureDrive: "ok" | "error";
  azureSubscription: "ok" | "error";
};

export async function GET(request: NextRequest) {
  return await Sentry.startSpan(
    {
      name: "Healthcheck",
      op: "healthcheck",
    },
    async () => {
      const healthCheck: HealthCheck = {
        db: "error",
        azureAccessToken: "error",
        azureDrive: "error",
        azureSubscription: "error",
      };

      const authHeader = request.headers.get("authorization");
      if (authHeader !== `Bearer ${env.CRON_SECRET}`) {
        console.log("Unauthorized");
        Sentry.captureMessage("Unauthorized", {
          level: "error",
        });

        return new Response("Unauthorized", {
          status: 401,
        });
      }

      // Check if the database is connected
      try {
        await prisma.$connect();
        healthCheck.db = "ok";
      } catch (error) {
        Sentry.captureException(error, {
          level: "error",
        });

        return new Response("Database connection error", {
          status: 500,
        });
      }

      // Check if the org exists
      const org = await prisma.org.findFirst({
        where: {
          clerkId: env.CLERK_ORG_ID,
        },
        include: {
          azureAccessToken: true,
          AzureDrive: {
            include: {
              subscriptions: true,
            },
          },
        },
      });

      if (!org) {
        console.log("Org not found");
        Sentry.captureMessage("Org not found", {
          level: "error",
        });

        return new Response("Org not found", {
          status: 404,
        });
      }

      // Check if the azure access token exists
      const azureAccessToken = org.azureAccessToken;
      if (!azureAccessToken) {
        return new Response("Azure access token not found", {
          status: 404,
        });
      }
      healthCheck.azureAccessToken = "ok";

      // Check if the azure drive exists
      const azureDrive = org.AzureDrive?.[0];
      if (!azureDrive) {
        console.log("Azure drive not found");
        Sentry.captureMessage("Azure drive not found", {
          level: "error",
        });

        return new Response("Azure drive not found", {
          status: 404,
        });
      }
      healthCheck.azureDrive = "ok";

      // Check if the azure subscription exists
      const subscription = azureDrive.subscriptions?.[0];
      if (!subscription) {
        return new Response("Azure subscription not found", {
          status: 404,
        });
      }

      // Check if the azure subscription is expired
      const isSubscriptionExpired =
        !subscription.expirationDateTime ||
        subscription.expirationDateTime < new Date();

      if (isSubscriptionExpired) {
        return Response.json(
          {
            message: "Azure subscription expired",
            data: {
              subscriptionId: subscription.subscriptionId,
              expirationDateTime: subscription.expirationDateTime,
            },
          },
          {
            status: 400,
          },
        );
      }

      healthCheck.azureSubscription = "ok";

      return Response.json(
        {
          healthCheck,
        },
        {
          status: 200,
        },
      );
    },
  );
}

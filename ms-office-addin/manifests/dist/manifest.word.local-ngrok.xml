<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="TaskPaneApp">
  <Id>67C793E3-EF1F-4535-B183-9F3FB2232323</Id>
  <Version>*******</Version>
  <ProviderName>Virgil <PERSON>(local-ngrok)</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  <DisplayName DefaultValue="Virgil AI(local-ngrok)" />
  <Description DefaultValue="Virgil AI is a tool that helps you get answers to your questions about your data." />
  <IconUrl DefaultValue="https://virgil-ai-dev.ngrok.app/logo/virgil-square-64.png" />
  <HighResolutionIconUrl DefaultValue="https://virgil-ai-dev.ngrok.app/logo/virgil-square.png" />
  <SupportUrl DefaultValue="https://virgil.ai" />
  <AppDomains>
    <AppDomain>https://localhost:3000</AppDomain>
    <AppDomain>https://localhost:4000</AppDomain>
    <AppDomain>https://accounts.google.com</AppDomain>
    <AppDomain>https://clerk.shared.lcl.dev</AppDomain>
    <AppDomain>https://virgil-ai-dev.ngrok.app</AppDomain>
    <AppDomain>https://together-slug-7.clerk.accounts.dev</AppDomain>
  </AppDomains>
  <Hosts>
    <Host Name="Document" />
  </Hosts>
  <DefaultSettings>
    <SourceLocation DefaultValue="https://virgil-ai-dev.ngrok.app/sign-in?redirect_url=https%3A%2F%2Fvirgil-ai-dev.ngrok.app%2Fadd-in-word" />
  </DefaultSettings>
  <Permissions>ReadWriteDocument</Permissions>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
    xsi:type="VersionOverridesV1_0">
    <Hosts>
      <Host xsi:type="Document">
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title" />
            <Description resid="GetStarted.Description" />
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl" />
          </GetStarted>
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label" />
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16" />
                  <bt:Image size="32" resid="Icon.32x32" />
                  <bt:Image size="80" resid="Icon.80x80" />
                </Icon>

                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="TaskpaneButton.SupertipTitle" />
                    <Description resid="TaskpaneButton.SupertipText" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://virgil-ai-dev.ngrok.app/logo/virgil-square-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="https://virgil-ai-dev.ngrok.app/logo/virgil-square-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="https://virgil-ai-dev.ngrok.app/logo/virgil-square-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://virgil-ai-dev.ngrok.app/ms-office-addin-learn-more" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://virgil-ai-dev.ngrok.app/sign-in?redirect_url=https%3A%2F%2Fvirgil-ai-dev.ngrok.app%2Fadd-in-word" />
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="Get started with the Virgil AI add-in!" />
        <bt:String id="CommandsGroup.Label" DefaultValue="Virgil AI(local-ngrok)" />
        <bt:String id="TaskpaneButton.Label" DefaultValue="Virgil AI(local-ngrok)" />
        <bt:String id="TaskpaneButton.SupertipTitle" DefaultValue="Virgil AI(local-ngrok)" />
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="Your add-in loaded successfully. Go to the HOME tab and click the 'Virgil AI' button to get started." />
        <bt:String id="TaskpaneButton.SupertipText" DefaultValue="Open the Virgil AI add-in" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="TaskPaneApp">
  <Id>{{addInId}}</Id>
  <Version>{{addInVersion}}</Version>
  <ProviderName>{{providerName}}</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  <DisplayName DefaultValue="{{displayName}}" />
  <Description DefaultValue="{{description}}" />
  <IconUrl DefaultValue="https://{{hostname}}/logo/virgil-square-64.png" />
  <HighResolutionIconUrl DefaultValue="https://{{hostname}}/logo/virgil-square.png" />
  <SupportUrl DefaultValue="{{supportUrl}}" />
  <AppDomains>
    {{#each appDomains}}
    <AppDomain>{{this}}</AppDomain>
    {{/each}}
  </AppDomains>
  <Hosts>
    {{#each hosts}}
    <Host Name="{{this}}" />
    {{/each}}
  </Hosts>
  <DefaultSettings>
    <SourceLocation DefaultValue="https://{{hostname}}/sign-in?redirect_url=https%3A%2F%2F{{hostname}}%2Fadd-in" />
  </DefaultSettings>
  <Permissions>ReadWriteDocument</Permissions>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
    xsi:type="VersionOverridesV1_0">
    <Hosts>
        {{#each hosts}}
        <Host xsi:type="{{this}}">
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title" />
            <Description resid="GetStarted.Description" />
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl" />
          </GetStarted>
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label" />
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16" />
                  <bt:Image size="32" resid="Icon.32x32" />
                  <bt:Image size="80" resid="Icon.80x80" />
                </Icon>

                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="TaskpaneButton.SupertipTitle" />
                    <Description resid="TaskpaneButton.SupertipText" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
        {{/each}}
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://{{hostname}}/logo/virgil-square-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="https://{{hostname}}/logo/virgil-square-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="https://{{hostname}}/logo/virgil-square-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://{{hostname}}/ms-office-addin-learn-more" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://{{hostname}}/sign-in?redirect_url=https%3A%2F%2F{{hostname}}%2Fadd-in" />
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="Get started with the Virgil AI add-in!" />
        <bt:String id="CommandsGroup.Label" DefaultValue="{{displayName}}" />
        <bt:String id="TaskpaneButton.Label" DefaultValue="{{displayName}}" />
        <bt:String id="TaskpaneButton.SupertipTitle" DefaultValue="{{displayName}}" />
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="{{getStartedDescription}}" />
        <bt:String id="TaskpaneButton.SupertipText" DefaultValue="{{taskpaneButtonSupertipText}}" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>
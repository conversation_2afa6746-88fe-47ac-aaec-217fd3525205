import { expect, type Locator, type Page } from "@playwright/test";

export class ChatPage {
    constructor(private page: Page) { }

    // Locators
    messageInput: Locator = this.page.locator("#chat-message-input");
    private userMessages = this.page.locator('.markdown-body p');
    private virgilLabels = this.page.locator('span:has-text("Virgil")');
    private loadingIndicator = this.page.locator('input[placeholder="Generating answer"]');

    // Actions
    async sendMessage(text: string) {
        await expect(this.messageInput).toBeVisible({ timeout: 10000 });
        await this.messageInput.fill(text);
        await this.page.keyboard.press("Enter");
    }

    async getLatestVirgilResponse(timeout = 120_000): Promise<string> {
        await this.loadingIndicator.waitFor({ state: "detached" });
        const label = this.virgilLabels.last();
        const markdown = label
            .locator('xpath=following-sibling::div//div[contains(@class,"markdown-body")]')
            .first();
        await markdown.waitFor({ timeout });
        return (await markdown.innerText())?.trim() ?? "";
    }

    // Verifications
    async verifyInputReady() {
        await expect(this.messageInput).toBeVisible({ timeout: 10000 });
        await this.messageInput.click();
        await expect(this.messageInput).toBeFocused();
        await expect(this.messageInput).toHaveValue("");
    }

    async verifyAllMessages(questions: string[], responses: string[]) {
        // Verify user messages
        const userMessages = await this.userMessages.all();
        for (let i = 0; i < questions.length; i++) {
            const messageText = await userMessages[i].textContent();
            expect(messageText).toBe(questions[i]);
        }

        // Verify Virgil responses
        const virgilResponses = await this.virgilLabels.all();
        for (let i = 0; i < responses.length; i++) {
            const responseContent = await virgilResponses[i]
                .locator('xpath=following-sibling::div//div[contains(@class,"markdown-body")]')
                .first();
            await expect(responseContent).toBeVisible();
            const responseText = await responseContent.textContent();
            expect(responseText?.trim()).toBe(responses[i]);
        }
    }

    async clearChatHistory() {
        // Find and click the clear history button in the header
        const clearButton = this.page.getByRole('button', { name: 'Clear chat history' });
        await clearButton.click();

        // Wait for the empty state message to confirm chat was cleared
        await this.page.getByText('Chat history cleared').waitFor();
        await this.page.getByText('The new conversation will contain no previous context.').waitFor();
    }
}

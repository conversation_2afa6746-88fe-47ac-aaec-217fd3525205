import type { Locator, <PERSON> } from "@playwright/test";
export class WordPluginPage {
	constructor(private page: Page) { }

	selectDocumentContainer: Locator = this.page.locator(
		".MuiBox-root.css-qi0l1j",
	);
	selectDocumentHeader: Locator = this.page.locator('h5[data-slot="button"]');
	statusFilterContainer: Locator = this.page.locator(
		".MuiFormControl-root.MuiFormControl-fullWidth.css-1ik6sfx-MuiFormControl-root",
	);
	statusFilterLabel: Locator = this.page.locator('label[data-shrink="true"]');
	statusFilterDropdown: Locator = this.page.locator(
		".MuiSelect-select.MuiSelect-outlined.MuiSelect-multiple",
	);
	statusFilterInput: Locator = this.page.locator('input[value="READY"]');
	statusFilterIcon: Locator = this.page.locator(
		".MuiSvgIcon-root.MuiSvgIcon-fontSizeMedium.MuiSelect-icon",
	);
	statusFilterFieldset: Locator = this.page.locator(
		'fieldset[aria-hidden="true"]',
	);
	statusFilterLegend: Locator = this.page.locator("legend.css-w1u3ce");
	searchContainer: Locator = this.page.locator(
		".MuiAutocomplete-root.MuiAutocomplete-hasClearIcon.MuiAutocomplete-hasPopupIcon",
	);
	searchFormControl: Locator = this.page.locator(
		".MuiFormControl-root.MuiFormControl-fullWidth.MuiTextField-root",
	);
	searchInputContainer: Locator = this.page.locator(
		".MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-colorPrimary.MuiInputBase-fullWidth.MuiInputBase-formControl.MuiInputBase-sizeSmall.MuiInputBase-adornedEnd.MuiAutocomplete-inputRoot",
	);
	searchInput: Locator = this.page.locator(
		'input[placeholder="Search documents..."]',
	);
	searchEndAdornment: Locator = this.page.locator(
		".MuiAutocomplete-endAdornment",
	);
	clearButton: Locator = this.page.locator(
		'.MuiAutocomplete-clearIndicator[aria-label="Clear"]',
	);
	clearIcon: Locator = this.page.locator('[data-testid="CloseIcon"]');
	popupIndicatorButton: Locator = this.page.locator(
		'.MuiAutocomplete-popupIndicator[aria-label="Open"]',
	);
	popupIndicatorIcon: Locator = this.page.locator(
		".MuiAutocomplete-popupIndicator svg",
	);
	searchFieldset: Locator = this.page.locator(
		".MuiOutlinedInput-notchedOutline.css-p51vrg-MuiOutlinedInput-notchedOutline",
	);
	searchLegend: Locator = this.page.locator("legend.css-w4cd9x");
	accordionRoot: Locator = this.page.locator('[data-slot="accordion"]');
	accordionItem: Locator = this.page.locator('[data-slot="accordion-item"]');
	accordionTrigger: Locator = this.page.locator(
		'[data-slot="accordion-trigger"]',
	);
	accordionContent: Locator = this.page.locator(
		'[data-slot="accordion-content"]',
	);
	accordionHeader: Locator = this.page.locator(
		'h3[data-orientation="vertical"]',
	);
	accordionButton: Locator = this.page.locator('button[aria-expanded="true"]');
	accordionChevron: Locator = this.page.locator(".lucide-chevron-down");
	// Accordion sections
	accordionSections: Locator = this.page.locator(
		'[data-slot="accordion-item"]',
	);
	accordionSectionButton: Locator = this.page.locator(
		'[data-slot="accordion-trigger"]',
	);
	accordionSectionLabel: Locator = this.page.locator(
		'[data-slot="accordion-trigger"] p[data-slot="button"]',
	);
	accordionSectionCheckbox: Locator = this.page.locator(
		'[data-slot="accordion-trigger"] .MuiCheckbox-root',
	);

	// Questions within sections
	questionItem: Locator = this.page.locator('[data-testid^="collapsed-cmd"]');
	questionCheckbox: Locator = this.page.locator(
		".MuiCheckbox-root.css-1smoula",
	);
	questionIcon: Locator = this.page.locator(".iconify.iconify--solar");
	questionTitle: Locator = this.page.locator("h6.MuiTypography-h6");
	questionAnswer: Locator = this.page.locator("p.MuiTypography-body2");
	async selectStatus(status: string) {
		await this.statusFilterDropdown.click();
		await this.page.locator(`text=${status}`).click();
	}

	async searchDocument(searchTerm: string) {
		await this.searchInput.click();
		await this.searchInput.fill(searchTerm);
	}

	async clearSearch() {
		await this.clearButton.click();
	}

	async openDropdown() {
		await this.popupIndicatorButton.click();
	}

	async getSelectedDocument() {
		return await this.searchInput.inputValue();
	}

	async isSearchFocused() {
		return await this.searchInput.evaluate(
			(el) => el === document.activeElement,
		);
	}

	async getQuestionByTestId(testId: string) {
		return this.page.locator(`[data-testid="${testId}"]`);
	}

	async getQuestionTitleByText(text: string) {
		return this.page.locator("h6.MuiTypography-h6").filter({ hasText: text });
	}

	async getQuestionAnswerByText(text: string) {
		return this.page.locator("p.MuiTypography-body2").filter({ hasText: text });
	}

	async selectQuestionCheckbox(testId: string) {
		const specificCheckbox = this.page.locator(
			`[data-testid="${testId}"] .MuiCheckbox-root.css-1smoula input`,
		);
		const specificCount = await specificCheckbox.count();

		if (specificCount > 0) {
			await specificCheckbox.click();
		} else {
			const checkbox = this.page.locator(
				`[data-testid="${testId}"] .MuiCheckbox-root input`,
			);
			await checkbox.click();
		}
	}

	async isQuestionCheckboxChecked(testId: string) {
		const specificCheckbox = this.page.locator(
			`[data-testid="${testId}"] .MuiCheckbox-root.css-1smoula input`,
		);
		const specificCount = await specificCheckbox.count();

		if (specificCount > 0) {
			return await specificCheckbox.isChecked();
		} else {
			const checkbox = this.page.locator(
				`[data-testid="${testId}"] .MuiCheckbox-root input`,
			);
			return await checkbox.isChecked();
		}
	}

	async toggleAccordion() {
		await this.accordionTrigger.click();
	}

	async getQuestionCount() {
		return await this.questionItem.count();
	}

	async isAccordionExpanded() {
		return (
			(await this.accordionButton.getAttribute("aria-expanded")) === "true"
		);
	}

	async selectDocumentFromDropdown(documentName: string) {
		// Wait for the dropdown to appear
		await this.page.waitForSelector(
			'.MuiAutocomplete-popper[data-popper-placement="bottom"]',
		);

		// Click on the document option that contains the document name
		await this.page
			.locator(`.MuiAutocomplete-option:has-text("${documentName}")`)
			.click();
	}

	async waitForDocumentLoad() {
		// Wait for document to load after selection
		await this.page.waitForTimeout(2000);
	}

	async waitForSectionContentLoad() {
		// Wait for section content to be fully loaded
		await this.page.waitForTimeout(1000);
	}

	async waitForCheckboxInteraction() {
		// Wait after checkbox interaction
		await this.page.waitForTimeout(500);
	}

	async getSectionCount() {
		return await this.accordionSections.count();
	}

	async getSectionLabel(index: number) {
		return await this.accordionSectionLabel.nth(index).textContent();
	}

	async getSectionLabels() {
		const count = await this.getSectionCount();
		const labels = [];
		for (let i = 0; i < count; i++) {
			const label = await this.getSectionLabel(i);
			labels.push(label);
		}
		return labels;
	}

	async expandSection(index: number) {
		const section = this.accordionSections.nth(index);
		const isExpanded = (await section.getAttribute("data-state")) === "open";

		if (!isExpanded) {
			await this.accordionSectionButton.nth(index).click();
			await this.page.waitForTimeout(1500);
		}
	}

	async collapseSection(index: number) {
		const section = this.accordionSections.nth(index);
		const isExpanded = (await section.getAttribute("data-state")) === "open";

		if (isExpanded) {
			await this.accordionSectionButton.nth(index).click();
		}
	}

	async isSectionExpanded(index: number) {
		const section = this.accordionSections.nth(index);
		return (await section.getAttribute("data-state")) === "open";
	}

	async selectSectionCheckbox(index: number) {
		await this.accordionSectionCheckbox.nth(index).click();
	}

	async getQuestionsInSection(sectionIndex: number) {
		await this.expandSection(sectionIndex);
		const section = this.accordionSections.nth(sectionIndex);

		// Wait for the section content to be visible
		const sectionContent = section.locator('[data-slot="accordion-content"]');
		await sectionContent.waitFor({ state: "visible", timeout: 5000 });

		return section.locator('[data-testid^="collapsed-cmd"]');
	}

	async getQuestionCountInSection(sectionIndex: number) {
		const questions = await this.getQuestionsInSection(sectionIndex);
		return await questions.count();
	}

	async isQuestionAlreadyAssigned(expandedQuestion: any): Promise<boolean> {
		const assignmentIndicators = [
			expandedQuestion.locator('span:has-text("Assigned to")'),
			expandedQuestion.locator('[data-testid*="assignee"]'),
			expandedQuestion.locator(".MuiAvatar-root"),
			expandedQuestion.locator('span:contains("@")'),
		];

		for (const indicator of assignmentIndicators) {
			if (await indicator.isVisible().catch(() => false)) {
				return true;
			}
		}

		return false;
	}

	async findAndExpandUnassignedDraftQuestion(): Promise<{
		question: any;
		title: string;
	} | null> {
		const sectionCount = await this.getSectionCount();
		console.log(
			`Searching for DRAFT questions across ${sectionCount} sections`,
		);

		let totalQuestionsChecked = 0;
		let draftQuestionsFound = 0;

		for (let i = 0; i < sectionCount; i++) {
			const sectionLabel = await this.getSectionLabel(i);
			console.log(`Checking section ${i + 1}: ${sectionLabel}`);

			await this.expandSection(i);
			const questionsInSection = await this.getQuestionsInSection(i);
			const questionCount = await questionsInSection.count();

			console.log(`  Found ${questionCount} questions in section ${i + 1}`);
			totalQuestionsChecked += questionCount;

			for (let j = 0; j < questionCount; j++) {
				const question = questionsInSection.nth(j);

				try {
					console.log(
						`  Checking question ${j + 1} of ${questionCount} in section ${i + 1}`,
					);

					// Add a check to ensure the question is visible and clickable
					await question.waitFor({ state: "visible", timeout: 5000 });

					// Use a shorter timeout for clicking to prevent hanging
					await question.click({ timeout: 10000 });
					await this.page.waitForTimeout(500);

					const expandedQuestion = this.page
						.locator(`[data-testid^="expanded-"]`)
						.first();
					if (await expandedQuestion.isVisible()) {
						const draftBadge = expandedQuestion.locator(
							'[data-slot="badge"]:has-text("DRAFT")',
						);
						if (await draftBadge.isVisible()) {
							draftQuestionsFound++;
							const questionTitleElement = expandedQuestion
								.locator("h6.MuiTypography-h6")
								.first();
							const questionTitle =
								(await questionTitleElement.textContent()) ?? "";
							console.log(`  Found DRAFT question: "${questionTitle}"`);

							const isAlreadyAssigned =
								await this.isQuestionAlreadyAssigned(expandedQuestion);
							if (!isAlreadyAssigned) {
								console.log(
									`  ✓ DRAFT question is unassigned - using this one`,
								);
								return {
									question: expandedQuestion,
									title: questionTitle,
								};
							} else {
								console.log(
									`  ✗ DRAFT question is already assigned - continuing search`,
								);
							}
						}

						// Click the question again to collapse it before moving to the next one
						try {
							await question.click({ timeout: 5000 });
							await this.page.waitForTimeout(300);
						} catch (collapseError) {
							console.log(
								`  Warning: Could not collapse question ${j + 1}, continuing...`,
							);
						}
					} else {
						console.log(`  Question ${j + 1} did not expand, skipping...`);
					}
				} catch (questionError) {
					const errorMessage =
						questionError instanceof Error
							? questionError.message
							: String(questionError);
					console.log(
						`  Error with question ${j + 1} in section ${i + 1}: ${errorMessage}`,
					);
					console.log(`  Skipping this question and continuing...`);
					// Continue to next question instead of failing the entire test
					continue;
				}
			}

			// Collapse the section after checking all its questions
			await this.collapseSection(i);
		}

		console.log(
			`Search completed: Checked ${totalQuestionsChecked} questions across ${sectionCount} sections`,
		);
		console.log(
			`Found ${draftQuestionsFound} DRAFT questions total, but none were unassigned`,
		);
		return null;
	}

	async assignExpandedQuestionToUserViaPerson(
		expandedQuestion: any,
		assigneeEmail: string,
	): Promise<boolean> {
		try {
			// Find and click the button containing the lucide-user icon
			const personButton = expandedQuestion
				.locator("button:has(.lucide-user)")
				.first();
			await personButton.click();
			await this.page.waitForTimeout(1000);

			// Wait for tooltip and fill search
			const tooltip = this.page.locator('[role="tooltip"]').first();
			await tooltip.waitFor({ timeout: 3000 });

			const searchInput = tooltip.getByPlaceholder("Search users...");
			await searchInput.fill(assigneeEmail.split("@")[0]);
			await this.page.waitForTimeout(500);

			// Find user and send for approval
			const userItem = tooltip.getByRole("menuitem").first();
			await userItem.waitFor({ timeout: 2000 });

			const sendButton = userItem.getByTitle("Send for approval");
			await sendButton.click();

			// Verify success toast appears
			await this.verifyAssignmentSuccessToast();

			return true;
		} catch (error) {
			console.log(`Assignment failed: ${error}`);
			return false;
		}
	}

	async verifyAssignmentSuccessToast(): Promise<boolean> {
		try {
			// Wait for the success toast to appear
			const successToast = this.page.locator('[data-sonner-toast][data-type="success"]');
			await successToast.waitFor({ timeout: 5000 });

			// Verify the toast has the correct message
			const toastMessage = successToast.locator('[data-title]');
			const messageText = await toastMessage.textContent();
			
			if (messageText?.includes("Question assigned with approval request")) {
				console.log(`✓ Success toast appeared: "${messageText}"`);
				return true;
			} else {
				console.log(`✗ Unexpected toast message: "${messageText}"`);
				return false;
			}
		} catch (error) {
			console.log(`Failed to verify success toast: ${error}`);
			return false;
		}
	}

	async getQuestionStatus(questionElement: any) {
		const statusBadge = questionElement.locator('[data-slot="badge"]').first();
		if (await statusBadge.isVisible()) {
			return await statusBadge.textContent();
		}
		return null;
	}

	async findApprovedQuestion(): Promise<string> {
		// Get all sections and look for questions with APPROVED status
		const sectionCount = await this.getSectionCount();
		if (sectionCount === 0) {
			throw new Error("No sections found");
		}

		let foundApprovedQuestion = false;
		let approvedQuestionTitle = "";

		// Search through all sections for APPROVED questions
		for (let i = 0; i < sectionCount; i++) {
			await this.expandSection(i);
			const questionsInSection = await this.getQuestionsInSection(i);
			const questionCount = await questionsInSection.count();

			for (let j = 0; j < questionCount; j++) {
				const question = questionsInSection.nth(j);

				try {
					// Click to expand the question
					await question.click({ timeout: 5000 });
					await this.page.waitForTimeout(500);

					const expandedQuestion = this.page
						.locator(`[data-testid^="expanded-"]`)
						.first();
					if (await expandedQuestion.isVisible()) {
						// Check for APPROVED badge
						const approvedBadge = expandedQuestion.locator(
							'[data-slot="badge"]:has-text("APPROVED")',
						);
						if (await approvedBadge.isVisible()) {
							foundApprovedQuestion = true;
							const questionTitleElement = expandedQuestion
								.locator("h6.MuiTypography-h6")
								.first();
							approvedQuestionTitle =
								(await questionTitleElement.textContent()) ?? "";

							console.log(
								`✓ Found APPROVED question: "${approvedQuestionTitle}"`,
							);

							// Collapse the question and break out of loops
							await question.click({ timeout: 5000 });
							await this.page.waitForTimeout(300);
							break;
						}

						// Collapse the question if it's not approved
						await question.click({ timeout: 5000 });
						await this.page.waitForTimeout(300);
					}
				} catch (error) {
					console.log(
						`Skipping question ${j + 1} in section ${i + 1} due to error: ${error}`,
					);
					continue;
				}
			}

			await this.collapseSection(i);

			if (foundApprovedQuestion) {
				break;
			}
		}

		if (!foundApprovedQuestion) {
			throw new Error("No APPROVED questions found in Word Plugin");
		}

		return approvedQuestionTitle;
	}

	async searchAndVerifyQuestionStatus(targetQuestionTitle: string): Promise<boolean> {
		// Use the search functionality to find the specific question
		console.log(`Searching for question in Word Plugin: "${targetQuestionTitle}"`);
		
		// Find the search input in the Word Plugin
		const searchInput = this.page.locator('[data-slot="input"][placeholder="Search"]');
		await searchInput.waitFor({ state: 'visible', timeout: 5000 });
		
		// Clear any existing search and enter the question title
		await searchInput.clear();
		await searchInput.fill(targetQuestionTitle);
		await this.page.keyboard.press('Enter');
		
		// Wait for search to execute and results to load
		await this.page.waitForTimeout(2000);
		
		// Wait for accordion sections to appear in search results
		const accordionSections = this.page.locator('[data-slot="accordion-item"]');
		try {
			await accordionSections.first().waitFor({ state: 'visible', timeout: 10000 });
		} catch (error) {
			console.log(`No sections appeared in search results for: "${targetQuestionTitle}"`);
			return false;
		}
		
		const sectionCount = await accordionSections.count();
		console.log(`Found ${sectionCount} sections in search results`);
		
		if (sectionCount === 0) {
			console.log(`No sections found in search results for: "${targetQuestionTitle}"`);
			return false;
		}
		
		// Go through each section and expand it to look for the question
		for (let i = 0; i < sectionCount; i++) {
			const section = accordionSections.nth(i);
			const sectionButton = section.locator('[data-slot="accordion-trigger"]');
			
			// Expand the section
			await sectionButton.click();
			await this.page.waitForTimeout(1000);
			
			// Look for questions in this expanded section
			const questionItems = section.locator('[data-testid^="collapsed-cmd"]');
			const questionCount = await questionItems.count();
			
			if (questionCount > 0) {
				// Look through questions in this section
				for (let j = 0; j < questionCount; j++) {
					const question = questionItems.nth(j);
					
					// Click to expand the question
					await question.click({ timeout: 5000 });
					await this.page.waitForTimeout(500);
					
					// Check if the expanded question matches our target
					const expandedQuestion = this.page.locator(`[data-testid^="expanded-"]`).first();
					if (await expandedQuestion.isVisible()) {
						// Get the question title to verify it's the right one
						const questionTitleElement = expandedQuestion.locator("h6.MuiTypography-h6").first();
						const questionTitle = (await questionTitleElement.textContent()) ?? "";
						
						console.log(`Found question: "${questionTitle}"`);
						
						// Check if this is the question we're looking for
						if (questionTitle.includes(targetQuestionTitle) || targetQuestionTitle.includes(questionTitle)) {
							// Check for APPROVED badge
							const approvedBadge = expandedQuestion.locator('[data-slot="badge"]:has-text("APPROVED")');
							const isApproved = await approvedBadge.isVisible();
							
							// Try to collapse the question, but don't fail if it doesn't work
							try {
								await question.click({ timeout: 3000 });
								await this.page.waitForTimeout(300);
							} catch (collapseError) {
								console.log(`Warning: Could not collapse question, continuing...`);
							}
							
							if (isApproved) {
								console.log(`✓ Question "${questionTitle}" has APPROVED status in Word Plugin`);
								return true;
							} else {
								console.log(`✗ Question "${questionTitle}" does NOT have APPROVED status in Word Plugin`);
								return false;
							}
						}
						
						// Try to collapse the question if it's not the target, but don't fail if it doesn't work
						try {
							await question.click({ timeout: 3000 });
							await this.page.waitForTimeout(300);
						} catch (collapseError) {
							console.log(`Warning: Could not collapse non-target question, continuing...`);
						}
					}
				}
			}
			
			// Collapse the section after checking
			await sectionButton.click();
			await this.page.waitForTimeout(500);
		}
		
		console.log(`✗ Target question "${targetQuestionTitle}" not found in search results`);
		return false;
	}

	async verifySpecificQuestionIsApproved(targetQuestionTitle: string): Promise<boolean> {
		// Use the search method instead of iterating through all questions
		return await this.searchAndVerifyQuestionStatus(targetQuestionTitle);
	}

	async navigateToWordPlugin(pluginUrl?: string) {
		// Navigate to Word Plugin page
		const url = pluginUrl || process.env.E2E_PLAYWRIGHT_PLUGIN_BASEURL || '';
		await this.page.goto(url);
		await this.page.waitForTimeout(2000);
	}
}

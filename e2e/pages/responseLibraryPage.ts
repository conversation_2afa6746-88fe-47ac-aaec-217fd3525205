import { expect, type Locator, type Page } from "@playwright/test";

export class ResponseLibraryPage {
	constructor(private page: Page) { }

	// Page Title
	title: Locator = this.page.getByRole("heading", {
		name: "Response Library",
		level: 3,
	});

	// Buttons
	addNewQuestionButton: Locator = this.page.getByRole("button", {
		name: "Add New Question",
	});
	filtersButton: Locator = this.page.getByRole("button", {
		name: /filters/i,
	});

	// Tabs
	allTab: Locator = this.page.getByRole("tab", { name: "All" });
	collaborationsTab: Locator = this.page.getByRole("tab", {
		name: "Pending Approval",
	});

	// Search Input
	searchInput: Locator = this.page.getByTestId("response-library-search-input");

	// View Toggle Buttons
	listViewButton: Locator = this.page.locator(
		'button[aria-pressed="false"][value="list"]',
	);
	gridViewButton: Locator = this.page.locator(
		'button[aria-pressed="true"][value="grid"]',
	);

	// Response Cards
	responseCards: Locator = this.page.locator('[data-slot="card"]');

	// Pagination
	paginationControls: Locator = this.page.locator(".MuiTablePagination-root");
	nextPageButton: Locator = this.page.getByRole("button", {
		name: "Go to next page",
	});
	previousPageButton: Locator = this.page.getByRole("button", {
		name: "Go to previous page",
	});

	// Updated locators based on the provided HTML structure
	questionsListContainer: Locator = this.page.getByTestId(
		"questions-list-container",
	);
	questionDetailsHeader: Locator = this.page.getByTestId(
		"question-details-header",
	);
	resultQuestionDetailsTitle: Locator = this.page.getByTestId(
		"question-details-header-content",
	);
	questionDetailsFooter: Locator = this.page.getByTestId(
		"question-details-footer",
	);

	rowsPerPageSelector: Locator = this.page.locator(
		'[aria-labelledby="«rp» «ro»"]',
	);

	// Methods
	async searchQuestion(query: string) {
		await this.searchInput.fill(query);
	}

	async openAddNewQuestionModal() {
		await this.addNewQuestionButton.click();
	}

	async selectTab(tabName: "All" | "Pending Approval") {
		const tab = tabName === "All" ? this.allTab : this.collaborationsTab;
		await tab.click();
		
		// Wait for tab content to load
		await this.page.waitForTimeout(2000);
		
		// Wait for any loading indicators to disappear
		await this.waitForTabToLoad();
	}

	async switchToListView() {
		if (await this.listViewButton.isVisible()) {
			await this.listViewButton.click();
		}
	}

	async switchToGridView() {
		if (await this.gridViewButton.isVisible()) {
			await this.gridViewButton.click();
		}
	}

	async getNumberOfResponses() {
		return await this.responseCards.count();
	}

	async searchAndFindCard(question: string) {
		console.log(`Searching for question: "${question}"`);
		await this.searchInput.fill(question);
		await this.page.keyboard.press("Enter");

		await this.page.waitForTimeout(500);

		// Wait for loader to appear and disappear
		const loader = this.page.locator('[role="progressbar"]');
		if (await loader.isVisible({ timeout: 2000 }).catch(() => false)) {
			console.log("Loader appeared, waiting for it to disappear...");
			await loader.waitFor({ state: "hidden", timeout: 10000 });
			console.log("Loader disappeared");
		}

		// Wait for search results to be present
		await this.resultQuestionDetailsTitle
			.first()
			.waitFor({
				state: "visible",
				timeout: 5000,
			})
			.catch(() => {
				throw new Error(`No search results found for question: "${question}"`);
			});

		// Get all question texts
		const allTexts = await this.resultQuestionDetailsTitle.allTextContents();
		console.log(`Found ${allTexts.length} search results`);
		console.log(`Search result texts:`, allTexts);

		// Check if the question is found
		const found = allTexts.some((text) => text.includes(question));

		// Provide detailed error message if not found
		expect(
			found,
			`Question "${question}" was not found in search results. ` +
			`Available results: ${allTexts.join(", ")}`,
		).toBeTruthy();

		console.log(`Question "${question}" found in search results: ${found}`);
	}

	async getQuestionsList() {
		return await this.questionsListContainer
			.locator('[data-slot="card"]')
			.allTextContents();
	}

	async getQuestionDetails(index: number) {
		const questionCard = this.questionsListContainer
			.locator('[data-slot="card"]')
			.nth(index);
		const header = await questionCard
			.locator('[data-testid="question-details-header-title"]')
			.textContent();
		const footer = await questionCard
			.locator('[data-testid="question-details-footer"]')
			.textContent();
		return { header, footer };
	}

	async selectRowsPerPage(rows: number) {
		console.log(`Setting rows per page to: ${rows}`);
		const rowsPerPageDropdown = this.page.getByRole("combobox", {
			name: "Rows per page:",
		});
		await rowsPerPageDropdown.click();
		await this.page.waitForTimeout(500);

		// Select the specified number of rows
		const option = this.page
			.locator(`li[data-value="${rows}"]`)
			.or(this.page.locator(`li:has-text("${rows}")`))
			.first();

		if (await option.isVisible()) {
			await option.click();
			console.log(`Successfully set rows per page to: ${rows}`);
		} else {
			console.log(
				`Option for ${rows} rows per page not found, keeping current setting`,
			);
		}

		await this.page.waitForTimeout(1000);
	}

	async openFilters() {
		await this.filtersButton.click();
		await this.page.waitForTimeout(1000);
	}

	async selectDDQFilter(ddqName: string) {
		await this.openFilters();

		const alreadySelected = this.page.locator(
			`button[data-slot="popover-trigger"]`,
			{
				has: this.page.locator(`span:has-text("${ddqName}")`),
			},
		);

		if (await alreadySelected.isVisible({ timeout: 1000 })) {
			console.log(`DDQ filter "${ddqName}" is already selected`);
			await this.page.keyboard.press("Escape");
			return;
		}

		const ddqDropdown = this.page
			.locator('button[aria-haspopup="dialog"]', {
				has: this.page.locator("span", { hasText: "Select DDQs" }),
			})
			.first();

		if (!(await ddqDropdown.isVisible({ timeout: 2000 }))) {
			throw new Error("DDQ filter dropdown not visible");
		}

		await ddqDropdown.click();

		const ddqOption = this.page
			.locator(`text="${ddqName}"`)
			.or(this.page.getByRole("option", { name: ddqName }))
			.first();

		if (!(await ddqOption.isVisible({ timeout: 2000 }))) {
			throw new Error(`DDQ option "${ddqName}" not found`);
		}

		await ddqOption.click();
		await this.page.keyboard.press("Escape");
	}

	async selectPendingApprovalTab() {
		const pendingTab = this.page.getByRole("tab", { name: "Pending Approval" });
		await pendingTab.click();
		await this.page.waitForTimeout(1000);
	}

	async selectAllTab() {
		await this.allTab.click();
		await this.page.waitForTimeout(1000);
	}

	async getTotalPages(): Promise<number> {
		// Get pagination info from "1–10 of 54" format
		const paginationText = await this.page
			.locator(".MuiTablePagination-displayedRows")
			.textContent();
		if (!paginationText) return 1;

		const match = /of (\d+)/.exec(paginationText);
		if (!match) return 1;

		const totalItems = parseInt(match[1]);
		const itemsPerPageText = await this.page
			.locator(".MuiTablePagination-select input")
			.getAttribute("value");
		const itemsPerPage = parseInt(itemsPerPageText ?? "10");

		return Math.ceil(totalItems / itemsPerPage);
	}

	async getCurrentPage(): Promise<number> {
		const paginationText = await this.page
			.locator(".MuiTablePagination-displayedRows")
			.textContent();
		if (!paginationText) return 1;

		const match = /(\d+)–\d+/.exec(paginationText);
		if (!match) return 1;

		const startItem = parseInt(match[1]);
		const itemsPerPageText = await this.page
			.locator(".MuiTablePagination-select input")
			.getAttribute("value");
		const itemsPerPage = parseInt(itemsPerPageText ?? "10");

		return Math.ceil(startItem / itemsPerPage);
	}

	async navigateToNextPage(): Promise<boolean> {
		const nextButton = this.page.getByRole("button", {
			name: "Go to next page",
		});
		const isDisabled = await nextButton.getAttribute("disabled");

		if (isDisabled !== null) {
			return false; // No next page available
		}

		await nextButton.click();
		await this.page.waitForTimeout(2000);
		return true;
	}

	async navigateToPreviousPage(): Promise<boolean> {
		const prevButton = this.page.getByRole("button", {
			name: "Go to previous page",
		});
		const isDisabled = await prevButton.getAttribute("disabled");

		if (isDisabled !== null) {
			return false; // No previous page available
		}

		await prevButton.click();
		await this.page.waitForTimeout(2000);
		return true;
	}

	async verifyAllDDQsHaveStatus(expectedStatus: string): Promise<{
		totalVerified: number;
		totalPages: number;
		statusMismatches: string[];
	}> {
		const totalPages = await this.getTotalPages();
		let totalVerified = 0;
		const statusMismatches: string[] = [];

		for (let page = 1; page <= totalPages; page++) {
			await this.page.waitForTimeout(2000);

			const questionRows = this.page.locator(".MuiDataGrid-row[data-id]");
			const questionCount = await questionRows.count();

			for (let i = 0; i < questionCount; i++) {
				const row = questionRows.nth(i);
				const statusCell = row.locator('[data-field="status"]');
				const statusText = await statusCell.textContent();

				const questionCell = row.locator('[data-field="question"]');
				const questionText = await questionCell.textContent();

				if (statusText && questionText) {
					const hasExpectedStatus = this.checkStatusInText(
						statusText,
						expectedStatus,
					);
					if (hasExpectedStatus) {
						totalVerified++;
					} else {
						const mismatchMsg = `Page ${page}, Question ${i + 1}: Expected "${expectedStatus}" but found status: "${statusText}"`;
						statusMismatches.push(mismatchMsg);
					}
				}
			}

			if (page < totalPages) {
				const navigated = await this.navigateToNextPage();
				if (!navigated) break;
			}
		}

		return { totalVerified, totalPages, statusMismatches };
	}

	private checkStatusInText(text: string, expectedStatus: string): boolean {
		const lowerText = text.toLowerCase();
		const lowerExpectedStatus = expectedStatus.toLowerCase();

		if (lowerExpectedStatus.includes("pending")) {
			return (
				lowerText.includes("pending") ||
				lowerText.includes("approval") ||
				lowerText.includes("review")
			);
		} else if (lowerExpectedStatus.includes("approved")) {
			return lowerText.includes("approved") && !lowerText.includes("pending");
		}

		return lowerText.includes(lowerExpectedStatus);
	}

	async verifyQuestionsByStatusAcrossPages(
		status: string,
		documentName?: string,
	) {
		await this.selectAllTab();

		if (documentName) {
			await this.selectDDQFilter(documentName);
			await this.page.waitForTimeout(3000);
		}

		await this.selectRowsPerPage(100);
		await this.page.waitForTimeout(3000);

		const questionRows = this.page.locator(".MuiDataGrid-row[data-id]");
		const questionCount = await questionRows.count();

		if (questionCount === 0) {
			return {
				totalVerified: 0,
				totalPages: 0,
				statusMismatches: [
					`No questions found for DDQ "${documentName ?? "N/A"}"`,
				],
			};
		}

		return this.verifyAllDDQsHaveStatus(status);
	}

	async findQuestionByTitle(questionTitle: string) {
		// Find a question card by its title text
		const questionCards = this.page.locator('[data-slot="card"]');
		const cardCount = await questionCards.count();

		for (let i = 0; i < cardCount; i++) {
			const card = questionCards.nth(i);
			const titleElement = card.locator('[data-testid="question-details-header-title"]');
			const titleText = await titleElement.textContent();

			if (titleText && titleText.includes(questionTitle)) {
				return card;
			}
		}

		throw new Error(`Question with title "${questionTitle}" not found`);
	}

	async hasStatus(questionCard: any, expectedStatus: string) {
		// Check if the question card has the expected status badge
		const statusBadge = questionCard.locator(`[data-slot="badge"]:has-text("${expectedStatus}")`);
		return await statusBadge.isVisible();
	}

	async approveQuestion(questionCard: any) {
		// Click the Approve button on a question card
		const approveButton = questionCard.locator('button:has-text("Approve")');
		await approveButton.click();
	}

	async hasSetAsDraftButton(questionCard: any) {
		// Check if the question card has the "Set as Draft" button
		const setAsDraftButton = questionCard.locator('button:has-text("Set as Draft")');
		try {
			// Wait for the button to appear after approval
			await setAsDraftButton.waitFor({ state: 'visible', timeout: 5000 });
			return true;
		} catch {
			// If button doesn't appear, check if it's already visible
			return await setAsDraftButton.isVisible();
		}
	}

	async approveFirstPendingQuestion(): Promise<string> {
		// Find the first question with PENDING APPROVAL status
		const questionCards = this.page.locator('[data-slot="card"]');
		const cardCount = await questionCards.count();

		if (cardCount === 0) {
			throw new Error("No question cards found");
		}

		// Use the first pending question
		const questionCard = questionCards.first();
		const questionTitle = await questionCard.locator('[data-testid="question-details-header-title"]').textContent() || "";

		// Verify it has PENDING APPROVAL status
		const hasPendingStatus = await this.hasStatus(questionCard, "PENDING APPROVAL");
		if (!hasPendingStatus) {
			throw new Error(`First question does not have PENDING APPROVAL status`);
		}

		console.log(`✓ Found question "${questionTitle}" with PENDING APPROVAL status`);

		// Approve the question
		await this.approveQuestion(questionCard);
		
		// Wait longer for the UI to update after approval
		console.log('Waiting for approval to process and UI to update...');
		await this.page.waitForTimeout(4000);
		
		// Verify it now has APPROVED status
		const hasApprovedStatus = await this.hasStatus(questionCard, "APPROVED");
		if (!hasApprovedStatus) {
			throw new Error(`Question "${questionTitle}" does not have APPROVED status after approval`);
		}
		
		// Verify the status changed and now shows "Set as Draft" button
		const hasSetAsDraftButton = await this.hasSetAsDraftButton(questionCard);
		if (!hasSetAsDraftButton) {
			throw new Error(`Question "${questionTitle}" does not have Set as Draft button after approval`);
		}

		return questionTitle;
	}

	async findApprovedQuestionWithSetAsDraftButton(): Promise<string> {
		// Find a question that is already APPROVED and has "Set as Draft" button
		const questionCards = this.page.locator('[data-slot="card"]');
		const cardCount = await questionCards.count();

		if (cardCount === 0) {
			throw new Error("No question cards found");
		}

		console.log(`Searching through ${cardCount} questions for APPROVED status with Set as Draft button...`);

		for (let i = 0; i < cardCount; i++) {
			const questionCard = questionCards.nth(i);
			const questionTitle = await questionCard.locator('[data-testid="question-details-header-title"]').textContent() || "";
			
			// Check if it has APPROVED status
			const hasApprovedStatus = await this.hasStatus(questionCard, "APPROVED");
			if (hasApprovedStatus) {
				// Check if it has "Set as Draft" button
				const hasSetAsDraftButton = await this.hasSetAsDraftButton(questionCard);
				if (hasSetAsDraftButton) {
					console.log(`✓ Found APPROVED question with Set as Draft button: "${questionTitle}"`);
					return questionTitle;
				}
			}
		}

		throw new Error("No APPROVED questions with 'Set as Draft' button found");
	}

	async verifySpecificQuestionIsApproved(questionTitle: string): Promise<boolean> {
		// Find the specific question by title and verify it's approved with Set as Draft button
		console.log(`Looking for specific question: "${questionTitle}"`);
		
		const questionCard = await this.findQuestionByTitle(questionTitle);
		
		// Check if it has APPROVED status
		const hasApprovedStatus = await this.hasStatus(questionCard, "APPROVED");
		if (!hasApprovedStatus) {
			console.log(`Question "${questionTitle}" does not have APPROVED status`);
			return false;
		}
		
		// Check if it has "Set as Draft" button
		const hasSetAsDraftButton = await this.hasSetAsDraftButton(questionCard);
		if (!hasSetAsDraftButton) {
			console.log(`Question "${questionTitle}" does not have Set as Draft button`);
			return false;
		}
		
		console.log(`✓ Question "${questionTitle}" has APPROVED status with Set as Draft button`);
		return true;
	}

	async navigateToResponseLibraryWithWaiting(baseUrl: string) {
		await this.page.goto(baseUrl + "/dashboard/response");
		await this.page.waitForURL("**/response");
		
		// Wait for the page title to be visible
		await this.title.waitFor({ state: 'visible', timeout: 10000 });
		
		// Wait for loading to complete - check if there's a loader
		const loader = this.page.locator('[role="progressbar"], .loading, .spinner');
		if (await loader.isVisible({ timeout: 2000 }).catch(() => false)) {
			console.log('Loader detected, waiting for it to disappear...');
			await loader.waitFor({ state: 'hidden', timeout: 15000 });
		}
		
		// Additional wait for content to load
		await this.page.waitForTimeout(3000);
		
		console.log('Response library page fully loaded');
	}

	async waitForTabToLoad() {
		// Wait for any loading indicators to disappear
		const loader = this.page.locator('[role="progressbar"], .loading, .spinner');
		if (await loader.isVisible({ timeout: 2000 }).catch(() => false)) {
			console.log('Tab loader detected, waiting for it to disappear...');
			await loader.waitFor({ state: 'hidden', timeout: 15000 });
		}
		
		// Wait for response cards to potentially load
		await this.page.waitForTimeout(2000);
		
		// Check if cards are loading
		const cardCount = await this.responseCards.count();
		console.log(`Found ${cardCount} response cards after waiting`);
	}

	async waitForApprovalProcessing() {
		// Wait for approval to process
		console.log('Waiting for approval to process...');
		await this.page.waitForTimeout(4000);
	}

	async debugPendingApprovalTab() {
		console.log('=== DEBUGGING PENDING APPROVAL TAB ===');
		
		// Check if we're on the right page
		const currentUrl = this.page.url();
		console.log(`Current URL: ${currentUrl}`);
		
		// Check tab state
		const pendingTab = this.collaborationsTab;
		const isTabVisible = await pendingTab.isVisible();
		const isTabSelected = await pendingTab.getAttribute('aria-selected');
		console.log(`Pending Approval tab visible: ${isTabVisible}, selected: ${isTabSelected}`);
		
		// Check for any error messages or empty states
		const emptyState = this.page.locator('text=No results found, text=No questions, text=Empty, [data-testid*="empty"]');
		const hasEmptyState = await emptyState.isVisible().catch(() => false);
		console.log(`Empty state visible: ${hasEmptyState}`);
		
		if (hasEmptyState) {
			const emptyText = await emptyState.textContent();
			console.log(`Empty state message: ${emptyText}`);
		}
		
		// Check for loading indicators
		const loader = this.page.locator('[role="progressbar"], .loading, .spinner, .MuiCircularProgress-root');
		const isLoading = await loader.isVisible().catch(() => false);
		console.log(`Loading indicator visible: ${isLoading}`);
		
		// Count response cards
		const cardCount = await this.responseCards.count();
		console.log(`Total response cards found: ${cardCount}`);
		
		// Check all tabs to see where questions might be
		console.log('Checking All tab for comparison...');
		await this.selectTab("All");
		const allTabCount = await this.responseCards.count();
		console.log(`Questions in All tab: ${allTabCount}`);
		
		// Go back to Pending Approval
		await this.selectTab("Pending Approval");
		
		console.log('=== END DEBUGGING ===');
	}
}

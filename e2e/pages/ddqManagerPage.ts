import { type Locator, type Page } from "@playwright/test";

export class DDQManagerPage {
	constructor(private page: Page) { }

	title: Locator = this.page.getByRole("heading", {
		name: "DDQ Manager",
		level: 3,
	});

	allTab: Locator = this.page.getByRole("tab", { name: "All" });
	newTab: Locator = this.page.getByRole("tab", { name: "New" });
	pendingTab: Locator = this.page.getByRole("tab", { name: "Pending" });
	reviewTab: Locator = this.page.getByRole("tab", { name: "In Review" });
	approvedTab: Locator = this.page.getByRole("tab", { name: "Approved" });

	listViewButton: Locator = this.page.locator('button[aria-label="List view"]');
	gridViewButton: Locator = this.page.locator('button[aria-label="Grid view"]');

	documentCards: Locator = this.page.locator('[data-slot="card"]');

	documentTable: Locator = this.page.locator(".MuiDataGrid-root");

	async selectTab(tabName: "All" | "New" | "Pending" | "In Review" | "Approved") {
		const tabs = {
			"All": this.allTab,
			"New": this.newTab,
			"Pending": this.pendingTab,
			"In Review": this.reviewTab,
			"Approved": this.approvedTab
		};
		await tabs[tabName].click();
	}

	async switchToListView() {
		if (await this.listViewButton.isVisible()) {
			await this.listViewButton.click();
		}
	}

	async switchToGridView() {
		if (await this.gridViewButton.isVisible()) {
			await this.gridViewButton.click();
		}
	}

	async changeDocumentStatus(documentName: string, toStatus: "NEW" | "IN PROGRESS" | "IN REVIEW" | "APPROVED") {
		const document = await this.findDocumentByName(documentName);

		const statusSelect = document.locator('button[data-slot="select-trigger"][role="combobox"]');

		await statusSelect.click();
		await this.page.waitForTimeout(1000);

		await this.page.getByRole("option", { name: toStatus }).click();
		await this.page.waitForTimeout(2000);
	}

	async findDocumentByName(documentName: string) {
		const cards = this.documentCards;
		const cardCount = await cards.count();

		for (let i = 0; i < cardCount; i++) {
			const card = cards.nth(i);
			const nameElement = card.locator('p[data-slot="button"]').first();

			if (await nameElement.isVisible({ timeout: 1000 }).catch(() => false)) {
				const name = await nameElement.textContent();
				if (name && name.trim() === documentName) {
					return card;
				}
			}
		}

		throw new Error(`Document "${documentName}" not found in current tab`);
	}

	async getDocumentName(index: number = 0): Promise<string> {
		await this.page.waitForTimeout(2000);

		const cards = this.documentCards;
		const cardCount = await cards.count();

		if (cardCount === 0) {
			throw new Error("No document cards found in DDQ Manager");
		}

		if (index >= cardCount) {
			throw new Error(`Requested card index ${index} but only ${cardCount} cards available`);
		}

		const card = cards.nth(index);

		const nameElement = card.locator('p[data-slot="button"]').first();

		if (await nameElement.isVisible({ timeout: 2000 }).catch(() => false)) {
			const name = await nameElement.textContent();
			if (name && name.trim().length > 0) {
				return name.trim();
			}
		}

		const cardText = await card.textContent();
		const lines = cardText?.split('\n').map(line => line.trim()).filter(line => line.length > 0) || [];
		const possibleName = lines.find(line =>
			line.includes('.') && (line.includes('.docx') || line.includes('.pdf') || line.includes('.xlsx'))
		) || lines[0];

		if (possibleName && possibleName.trim().length > 0) {
			return possibleName.trim();
		}

		throw new Error(`Could not find document name for card at index ${index}`);
	}

	async findDocumentWithDraftStatus(): Promise<{ name: string; element: Locator } | null> {
		await this.selectTab("New");
		await this.page.waitForTimeout(2000);

		const cards = this.documentCards;
		const cardCount = await cards.count();

		if (cardCount > 0) {
			try {
				const name = await this.getDocumentName(0);
				return {
					name: name,
					element: cards.first()
				};
			} catch (error) {
			}
		}

		await this.selectTab("All");
		await this.page.waitForTimeout(2000);

		const allCards = this.documentCards;
		const allCardCount = await allCards.count();

		for (let i = 0; i < Math.min(allCardCount, 3); i++) {
			const card = allCards.nth(i);
			const statusElement = card.locator('[data-slot="select-value"]');
			const statusValue = await statusElement.textContent().catch(() => null);

			if (statusValue === "NEW") {
				try {
					const name = await this.getDocumentName(i);
					return {
						name: name,
						element: card
					};
				} catch (error) {
					continue;
				}
			}
		}

		return null;
	}
}
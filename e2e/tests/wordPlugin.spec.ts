/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { WordPluginPage } from "../pages/wordPluginPage";
import { ResponseLibraryPage } from "../pages/responseLibraryPage";
import {
  E2E_PLAYWRIGHT_USER_NAME,
  E2E_PLAYWRIGHT_USER_PASSWORD,
  E2E_PLAYWRIGHT_PLUGIN_BASEURL,
  E2E_PLAYWRIGHT_BASEURL,
} from "../config";

const ASSIGNEE_EMAIL = "<EMAIL>";
const DOC_NAME = "DD Request.docx";

// Shared variable to track the question across tests
let assignedQuestionTitle = "";

test.describe.serial("Word Plugin Tests", () => {
  let context: BrowserContext;
  let page: Page;
  let loginPage: LoginPage;
  let wordPluginPage: WordPluginPage;
  let responseLibraryPage: ResponseLibraryPage;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      recordVideo: {
        dir: "./test-results/videos",
        size: { width: 1280, height: 720 },
      },
    });
    page = await context.newPage();
    loginPage = new LoginPage(page);
    wordPluginPage = new WordPluginPage(page);
    responseLibraryPage = new ResponseLibraryPage(page);
    await page.goto(E2E_PLAYWRIGHT_BASEURL);
    await loginPage.login(E2E_PLAYWRIGHT_USER_NAME, E2E_PLAYWRIGHT_USER_PASSWORD);
    await page.waitForURL("**/dashboard");
    await page.goto(E2E_PLAYWRIGHT_PLUGIN_BASEURL);
  });

  test.afterAll(async () => {
    const videoPath = await page.video()?.path();
    if (videoPath) {
      await test.info().attach("video", { path: videoPath });
    }
    await context.close();
  });

  test("should select 'Gilad test' document and verify answers", async () => {
    await expect(wordPluginPage.selectDocumentHeader).toBeVisible();
    await expect(wordPluginPage.selectDocumentHeader).toHaveText(
      "SelectDocument",
    );
    await wordPluginPage.searchDocument(DOC_NAME);
    await wordPluginPage.selectDocumentFromDropdown(DOC_NAME);
    const selectedDocument = await wordPluginPage.getSelectedDocument();
    expect(selectedDocument).toContain(DOC_NAME);
    await expect(wordPluginPage.accordionRoot).toBeVisible();

    // Get all sections and verify they exist
    const sectionCount = await wordPluginPage.getSectionCount();
    expect(sectionCount).toBeGreaterThan(0);

    // Verify sections are present (they should be collapsed)
    await expect(wordPluginPage.accordionSections.first()).toBeVisible();

    // Get section labels for verification
    const sectionLabels = await wordPluginPage.getSectionLabels();
    console.log(`Found ${sectionCount} sections:`, sectionLabels);

    let totalQuestions = 0;

    // Verify each section and its questions
    for (let i = 0; i < sectionCount; i++) {
      const sectionLabel = await wordPluginPage.getSectionLabel(i);
      expect(sectionLabel).toBeTruthy();

      // Expand the section to access questions
      await wordPluginPage.expandSection(i);

      // Verify section is expanded
      const isExpanded = await wordPluginPage.isSectionExpanded(i);
      expect(isExpanded).toBe(true);

      // Add a small delay to ensure section content is fully loaded
      await wordPluginPage.waitForSectionContentLoad();

      // Get questions in this section
      const questionsInSection = await wordPluginPage.getQuestionsInSection(i);
      const questionCount = await questionsInSection.count();

      console.log(
        `Section ${i} "${sectionLabel}" has ${questionCount} questions`,
      );

      if (questionCount > 0) {
        totalQuestions += questionCount;

        // Verify each question in the section
        for (let j = 0; j < questionCount; j++) {
          const questionItem = questionsInSection.nth(j);

          // Get the question title
          const questionTitle = await questionItem
            .locator("h6.MuiTypography-h6")
            .textContent();
          expect(questionTitle).toBeTruthy();

          // Get the question answer
          const questionAnswer = await questionItem
            .locator("p.MuiTypography-body2")
            .textContent();
          expect(questionAnswer).toBeTruthy();

          // Verify the answer is not empty or just whitespace
          expect(questionAnswer?.trim()).not.toBe("");

          // Verify the question has a checkbox
          const checkbox = questionItem.locator(".MuiCheckbox-root");
          await expect(checkbox).toBeVisible();

          // Verify the question has an icon (may not be present in all questions)
          const icon = questionItem.locator(".iconify.iconify--solar");
          const iconCount = await icon.count();
          if (iconCount > 0) {
            await expect(icon).toBeVisible();
          }
        }

        // Extract the number from the section label and verify it matches the actual count
        const labelMatch = sectionLabel?.match(/\((\d+)\)/);
        if (labelMatch) {
          const labelCount = parseInt(labelMatch[1]);
          expect(labelCount).toBe(questionCount);
        }
      }

      // Collapse the section after verification
      await wordPluginPage.collapseSection(i);
    }

    expect(totalQuestions).toBeGreaterThan(0);

    if (sectionCount > 0) {
      await wordPluginPage.expandSection(0);
      let isExpanded = await wordPluginPage.isSectionExpanded(0);
      expect(isExpanded).toBe(true);

      await wordPluginPage.collapseSection(0);
      isExpanded = await wordPluginPage.isSectionExpanded(0);
      expect(isExpanded).toBe(false);
      await wordPluginPage.selectSectionCheckbox(0);
      await wordPluginPage.expandSection(0);
      const questionsInFirstSection =
        await wordPluginPage.getQuestionsInSection(0);
      const firstSectionQuestionCount = await questionsInFirstSection.count();

      if (firstSectionQuestionCount > 0) {
        const firstQuestion = questionsInFirstSection.nth(0);
        const firstQuestionTestId =
          await firstQuestion.getAttribute("data-testid");
        if (firstQuestionTestId) {
          console.log(`Testing checkbox for question: ${firstQuestionTestId}`);
          const initiallyChecked =
            await wordPluginPage.isQuestionCheckboxChecked(firstQuestionTestId);
          console.log(`Initial checkbox state: ${initiallyChecked}`);
          await wordPluginPage.selectQuestionCheckbox(firstQuestionTestId);
          await wordPluginPage.waitForCheckboxInteraction();
          const isChecked =
            await wordPluginPage.isQuestionCheckboxChecked(firstQuestionTestId);
          console.log(`Checkbox state after click: ${isChecked}`);
          expect(isChecked).toBe(false);
        }
      }
    }

    console.log(
      `✓ Verified ${totalQuestions} questions across ${sectionCount} sections for 'Gilad test' document`,
    );
  });

  test(`should assign DRAFT question to ${ASSIGNEE_EMAIL} and verify in Pending Approvals`, async () => {
    await wordPluginPage.searchDocument(DOC_NAME);
    await wordPluginPage.selectDocumentFromDropdown(DOC_NAME);
    await wordPluginPage.waitForDocumentLoad();
    const draftQuestionResult = await wordPluginPage.findAndExpandUnassignedDraftQuestion();
    const { question: expandedDraftQuestion, title: questionTitle } = draftQuestionResult as any;
    const assignmentSuccessful = await wordPluginPage.assignExpandedQuestionToUserViaPerson(
      expandedDraftQuestion,
      ASSIGNEE_EMAIL
    );

    if (!assignmentSuccessful) {
      console.log("Failed to assign DRAFT question - skipping test");
      test.skip(true, "Failed to assign DRAFT question to user");
      return;
    }

    // Store the question title for other tests to use
    assignedQuestionTitle = questionTitle;
    console.log(`✓ Successfully assigned question "${questionTitle}" with success toast verification`);

    // Navigate to Response Library with proper waiting
    await responseLibraryPage.navigateToResponseLibraryWithWaiting(E2E_PLAYWRIGHT_BASEURL);
    await responseLibraryPage.selectTab("Pending Approval");

    // Wait for the tab content to fully load
    await responseLibraryPage.waitForTabToLoad();

    const responsesCount = await responseLibraryPage.getNumberOfResponses();
    console.log(`Found ${responsesCount} responses in Pending Approval tab`);
    expect(responsesCount).toBeGreaterThan(0);

    // Find and approve the specific question we just assigned
    const questionCard = await responseLibraryPage.findQuestionByTitle(questionTitle);
    expect(questionCard).toBeTruthy();

    // Verify it has PENDING APPROVAL status
    const hasPendingStatus = await responseLibraryPage.hasStatus(questionCard, "PENDING APPROVAL");
    expect(hasPendingStatus).toBe(true);

    // Click the Approve button
    await responseLibraryPage.approveQuestion(questionCard);

    // Wait longer for approval to process
    await responseLibraryPage.waitForApprovalProcessing();

    // Verify the status changed and now shows "Set as Draft" button
    const hasSetAsDraftButton = await responseLibraryPage.hasSetAsDraftButton(questionCard);
    console.log(`Set as Draft button visible: ${hasSetAsDraftButton}`);
    expect(hasSetAsDraftButton).toBe(true);

    console.log(`✓ Successfully approved question "${questionTitle}" and verified status change`);
  });

  test(`should verify approved question has Set as Draft button`, async () => {
    // Use the specific question that was assigned in the first test
    await responseLibraryPage.navigateToResponseLibraryWithWaiting(E2E_PLAYWRIGHT_BASEURL);
    await responseLibraryPage.selectTab("Pending Approval");

    const responsesCount = await responseLibraryPage.getNumberOfResponses();
    console.log(`Found ${responsesCount} responses in Pending Approval tab`);

    if (responsesCount === 0) {
      console.log("No responses found in Pending Approval tab");
      await responseLibraryPage.debugPendingApprovalTab();
      expect(responsesCount).toBeGreaterThan(0);
      return;
    }

    // Verify the specific question that was assigned and approved
    const isApproved = await responseLibraryPage.verifySpecificQuestionIsApproved(assignedQuestionTitle);
    expect(isApproved).toBe(true);

    console.log(`✓ Verified specific question "${assignedQuestionTitle}" has Set as Draft button in Pending Approval tab`);
  });

  test(`should verify approved question shows APPROVED status in Word Plugin`, async () => {
    // Navigate back to Word Plugin using page object method
    await wordPluginPage.navigateToWordPlugin(E2E_PLAYWRIGHT_PLUGIN_BASEURL);
    await wordPluginPage.searchDocument(DOC_NAME);
    await wordPluginPage.selectDocumentFromDropdown(DOC_NAME);

    // Verify the specific question that was assigned and approved
    const isApproved = await wordPluginPage.verifySpecificQuestionIsApproved(assignedQuestionTitle);
    expect(isApproved).toBe(true);

    console.log(`✓ Verified specific question "${assignedQuestionTitle}" has APPROVED status in Word Plugin`);
  });
});

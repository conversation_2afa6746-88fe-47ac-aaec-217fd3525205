import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";
import { DDQManagerPage } from "../pages/ddqManagerPage";
import { ResponseLibraryPage } from "../pages/responseLibraryPage";

const {
	E2E_PLAYWRIGHT_USER_NAME,
	E2E_PLAYWRIGHT_USER_PASSWORD,
	E2E_PLAYWRIGHT_BASEURL,
} = process.env;

if (
	!E2E_PLAYWRIGHT_USER_NAME ||
	!E2E_PLAYWRIGHT_USER_PASSWORD ||
	!E2E_PLAYWRIGHT_BASEURL
) {
	throw new Error("Missing required variables.");
}
test.describe.configure({ mode: "serial" });

test.describe("DDQ Status Workflow Tests", () => {
	let context: BrowserContext;
	let page: Page;
	let loginPage: LoginPage;
	let dashboardPage: DashboardPage;
	let ddqManagerPage: DDQManagerPage;
	let responseLibraryPage: ResponseLibraryPage;
	let selectedDocumentName: string;

	test.beforeAll(async ({ browser }) => {
		context = await browser.newContext({
			recordVideo: {
				dir: "videos/",
				size: { width: 1280, height: 720 },
			},
		});
		page = await context.newPage();
		loginPage = new LoginPage(page);
		dashboardPage = new DashboardPage(page);
		ddqManagerPage = new DDQManagerPage(page);
		responseLibraryPage = new ResponseLibraryPage(page);

		await page.goto(E2E_PLAYWRIGHT_BASEURL);
	});

	test.afterEach(async ({ }, testInfo) => {
		const videoPath = await page.video()?.path();
		if (videoPath) {
			await testInfo.attach("video", {
				path: videoPath,
				contentType: "video/webm",
			});
		}
	});

	test.afterAll(async () => {
		await context.close();
	});

	test("should login to the system and verify dashboard", async () => {
		await loginPage.login(
			E2E_PLAYWRIGHT_USER_NAME,
			E2E_PLAYWRIGHT_USER_PASSWORD,
		);

		await expect(dashboardPage.ddqManagerLink).toBeVisible();
		await expect(dashboardPage.responseLibraryLink).toBeVisible();
	});

	test("should navigate to DDQ Manager and find a document with NEW status", async () => {
		await dashboardPage.ddqManagerLink.click();

		await expect(ddqManagerPage.title).toBeVisible({ timeout: 15000 });

		const newDocument = await ddqManagerPage.findDocumentWithDraftStatus();

		if (!newDocument) {
			await ddqManagerPage.selectTab("New");
			selectedDocumentName = await ddqManagerPage.getDocumentName(0);
		} else {
			selectedDocumentName = newDocument.name;
		}

		expect(selectedDocumentName).toBeTruthy();
		expect(selectedDocumentName.length).toBeGreaterThan(0);
	});

	test("should change document status from NEW to IN REVIEW", async () => {
		await expect(ddqManagerPage.title).toBeVisible();
		await ddqManagerPage.selectTab("New");
		await page.waitForTimeout(1000);
		await ddqManagerPage.changeDocumentStatus(
			selectedDocumentName,
			"IN REVIEW",
		);
		await ddqManagerPage.selectTab("In Review");
		await page.waitForTimeout(2000);
		const reviewDocument =
			await ddqManagerPage.findDocumentByName(selectedDocumentName);
		await expect(reviewDocument).toBeVisible();
	});

	test("should navigate to Response Library and verify questions in Pending for Approval", async () => {
		await dashboardPage.responseLibraryLink.click();

		await expect(responseLibraryPage.title).toBeVisible({ timeout: 15000 });
		await expect(responseLibraryPage.filtersButton).toBeVisible();
		await responseLibraryPage.selectDDQFilter(selectedDocumentName);

		// Wait longer for questions to be processed
		await page.waitForTimeout(2000);

		const pendingResults =
			await responseLibraryPage.verifyQuestionsByStatusAcrossPages(
				"Pending Approval",
				selectedDocumentName,
			);
		console.log(`Pending results:`, pendingResults);

		// If no questions found, check if any questions exist for this DDQ at all
		if (pendingResults.totalVerified === 0) {
			// Check if there are any questions with any status for this DDQ
			const questionRows = page.locator(".MuiDataGrid-row[data-id]");
			const questionCount = await questionRows.count();
			console.log(`Total questions found for DDQ "${selectedDocumentName}": ${questionCount}`);

			if (questionCount > 0) {
				// Log the actual statuses of questions found
				for (let i = 0; i < Math.min(questionCount, 5); i++) {
					const row = questionRows.nth(i);
					const statusCell = row.locator('[data-field="status"]');
					const statusText = await statusCell.textContent();
					console.log(`Question ${i + 1} status: "${statusText}"`);
				}
			}
		}

		await page.waitForTimeout(2000);
		expect(pendingResults.totalVerified).toBeGreaterThan(0);
		expect(pendingResults.statusMismatches.length).toBeLessThanOrEqual(
			pendingResults.totalVerified * 0.1,
		);
	});

	test("should go back to DDQ Manager and change status to Approved", async () => {
		await dashboardPage.ddqManagerLink.click();

		await expect(ddqManagerPage.title).toBeVisible({ timeout: 15000 });
		await ddqManagerPage.selectTab("In Review");
		await page.waitForTimeout(1000);

		await ddqManagerPage.changeDocumentStatus(selectedDocumentName, "APPROVED");

		await ddqManagerPage.selectTab("Approved");
		await page.waitForTimeout(2000);

		const approvedDocument =
			await ddqManagerPage.findDocumentByName(selectedDocumentName);
		await expect(approvedDocument).toBeVisible();
	});

	test("should verify questions are now in Approved state in Response Library", async () => {
		await dashboardPage.responseLibraryLink.click();

		await expect(responseLibraryPage.title).toBeVisible({ timeout: 15000 });

		await page.waitForTimeout(3000);

		await responseLibraryPage.selectDDQFilter(selectedDocumentName);
		const approvedResults =
			await responseLibraryPage.verifyQuestionsByStatusAcrossPages(
				"APPROVED",
				selectedDocumentName,
			);

		expect(approvedResults.totalVerified).toBeGreaterThan(0);
		expect(approvedResults.statusMismatches.length).toBeLessThanOrEqual(
			approvedResults.totalVerified * 0.1,
		);
	});

	test("should go back to DDQ Manager and change status to NEW", async () => {
		await dashboardPage.ddqManagerLink.click();

		await expect(ddqManagerPage.title).toBeVisible({ timeout: 15000 });
		await ddqManagerPage.selectTab("Approved");
		await page.waitForTimeout(1000);

		await ddqManagerPage.changeDocumentStatus(selectedDocumentName, "NEW");

		await ddqManagerPage.selectTab("New");
		await page.waitForTimeout(2000);

		const newDocument =
			await ddqManagerPage.findDocumentByName(selectedDocumentName);
		await expect(newDocument).toBeVisible();
	});

	test("should verify questions are now in New state in Response Library", async () => {
		await dashboardPage.responseLibraryLink.click();

		await expect(responseLibraryPage.title).toBeVisible({ timeout: 15000 });

		await page.waitForTimeout(3000);

		await responseLibraryPage.selectDDQFilter(selectedDocumentName);
		const approvedResults =
			await responseLibraryPage.verifyQuestionsByStatusAcrossPages(
				"DRAFT",
				selectedDocumentName,
			);

		expect(approvedResults.totalVerified).toBeGreaterThan(0);
		expect(approvedResults.statusMismatches.length).toBeLessThanOrEqual(
			approvedResults.totalVerified * 0.1,
		);
	});
});

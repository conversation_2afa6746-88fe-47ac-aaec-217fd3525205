import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";
import { ChatPage } from "../pages/chatPage";
import { loadDdq } from "../utils/loadDdq";
import { llmSimilarity } from "../utils/anthropicJudge";
import {
    E2E_PLAYWRIGHT_USER_NAME,
    E2E_PLAYWRIGHT_USER_PASSWORD,
    E2E_PLAYWRIGHT_BASEURL,
} from "../config";
test.describe.configure({ mode: "serial" });

test.describe("Chat Test cases", () => {
    let context: BrowserContext;
    let page: Page;
    let chat: ChatPage;
    let qaList: { question: string; answer: string }[];

    test.beforeAll(async ({ browser }) => {
        context = await browser.newContext();
        page = await context.newPage();
        chat = new ChatPage(page);
        const login = new LoginPage(page);
        await page.goto(E2E_PLAYWRIGHT_BASEURL);
        await login.login(E2E_PLAYWRIGHT_USER_NAME, E2E_PLAYWRIGHT_USER_PASSWORD);
        const dash = new DashboardPage(page);
        await dash.chatLink.click();
        await page.waitForURL("**/dashboard/chat");
        await expect(chat.messageInput).toBeVisible({ timeout: 10000 });
        qaList = await loadDdq();
    });
    test.afterEach(async ({ }, testInfo) => {
        const videoPath = await page.video()?.path();
        if (videoPath) {
            await testInfo.attach("video", {
                path: videoPath,
                contentType: "video/webm",
            });
        }
    });

    test.afterAll(async () => {
        await context.close();
    });

    test("should answer all DDQ questions ≥ 85 % (LLM judge)", async () => {
        test.setTimeout(10 * 60 * 1000);
        for (const { question, answer } of qaList) {
            await chat.sendMessage(question);
            const reply = await chat.getLatestVirgilResponse();

            const score = await llmSimilarity(reply, answer);
            const good = score >= 85;
            test.info().annotations.push({
                type: "similarity",
                description: `Q: ${question} - Claude: ${score}% ${good ? "✅" : "❌"}`,
            });
            expect.soft(score).toBeGreaterThanOrEqual(85);
        }
    });

    test("should persist chat history through multiple tab switches", async () => {
        const dash = new DashboardPage(page);

        // Clear chat history at the start
        await chat.clearChatHistory();

        // Test data - investment related questions
        const questions = [
            "What is your investment strategy for emerging markets?",
            "How do you manage portfolio risk?",
            "What are your ESG investment criteria?"
        ];
        const responses: string[] = [];

        // Test each question with tab switching
        for (let i = 0; i < questions.length; i++) {
            const question = questions[i];

            // Send question and wait for response
            await chat.sendMessage(question);
            const response = await chat.getLatestVirgilResponse();
            expect(response.trim().length).toBeGreaterThan(0);
            responses.push(response.trim());

            // Navigate to response library and back to verify persistence
            await dash.responseLibraryLink.click();
            await page.waitForURL("**/dashboard/response");

            await dash.chatLink.click();
            await page.waitForURL("**/dashboard/chat");

            // Wait for chat to be ready
            await expect(chat.messageInput).toBeVisible({ timeout: 10000 });

            // Verify all previous messages are still there
            await chat.verifyAllMessages(questions.slice(0, i + 1), responses);
        }
    });
});

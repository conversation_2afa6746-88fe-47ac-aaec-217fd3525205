import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";
import { ResponseLibraryPage } from "../pages/responseLibraryPage";
import { loadQuestions } from "../utils/loadDdq";
import {
	E2E_PLAYWRIGHT_USER_NAME,
	E2E_PLAYWRIGHT_USER_PASSWORD,
	E2E_PLAYWRIGHT_BASEURL,
} from "../config";
const fileName = "response-library1.docx";

test.describe.configure({ mode: "serial" });

test.describe("Response Library Page", () => {
	test.setTimeout(10 * 60 * 1000);

	let context: BrowserContext;
	let page: Page;
	let loginPage: LoginPage;
	let dashboardPage: DashboardPage;
	let responseLibraryPage: ResponseLibraryPage;


	test.beforeAll(async ({ browser }) => {
		context = await browser.newContext({
			recordVideo: {
				dir: "videos/",
				size: { width: 1280, height: 720 },
			},
		});
		page = await context.newPage();
		loginPage = new LoginPage(page);
		dashboardPage = new DashboardPage(page);
		responseLibraryPage = new ResponseLibraryPage(page);
		await page.goto(E2E_PLAYWRIGHT_BASEURL);

	});

	test.afterEach(async ({ }, testInfo) => {
		const videoPath = await page.video()?.path();
		if (videoPath) {
			await testInfo.attach("video", {
				path: videoPath,
				contentType: "video/webm",
			});
		}
	});

	test.afterAll(async () => {
		await context.close();
	});

	test("should login to the system and verify dashboard", async () => {
		await loginPage.login(
			E2E_PLAYWRIGHT_USER_NAME,
			E2E_PLAYWRIGHT_USER_PASSWORD,
		);
		await page.waitForURL(/\/dashboard\/.+/, { timeout: 30000 });
		await expect(dashboardPage.ddqManagerLink).toBeVisible();
		await expect(dashboardPage.dataRoomLink).toBeVisible();
		await expect(dashboardPage.responseLibraryLink).toBeVisible();
		await expect(dashboardPage.chatLink).toBeVisible();

	});

	test("should navigate to the Response Library page", async () => {
		await dashboardPage.responseLibraryLink.click();
		await expect(responseLibraryPage.paginationControls).toBeVisible({ timeout: 25000 });
		await expect(responseLibraryPage.title).toBeVisible();
		await expect(responseLibraryPage.allTab).toBeVisible();
		await expect(responseLibraryPage.collaborationsTab).toBeVisible();
		await expect(responseLibraryPage.filtersButton).toBeVisible();
	});

	test("should verify questions from the document appear in search results", async () => {
		const questions = await loadQuestions(fileName);
		console.log(`Loaded ${questions.length} questions from document`);

		for (const { question } of questions) {
			console.log(`Testing question: ${question}`);
			await responseLibraryPage.searchAndFindCard(question);
			await responseLibraryPage.searchInput.clear();
		}
	});

});




/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
import { test, expect } from '@playwright/test';
import { readFileSync } from 'fs';
import { join } from 'path';
import { Pool } from 'pg';
import { DATABASE_URL } from '../config';

const JSON_FILES = [
	'ddqs.json',
	'TRUNCATED_Partners_Capital_Hunter_Point.json'
];
interface QuestionObject {
	question: string;
	type?: string;
	condition?: string;
	subquestions?: string[];
}

interface StructuredJsonData {
	questions: QuestionObject[];
}

type JsonData = string[] | StructuredJsonData;

// Helper function to extract questions from different JSON formats
function extractQuestions(jsonData: JsonData): string[] {
	if (Array.isArray(jsonData)) {
		// Format: ["Question: text", ...]
		return jsonData.map(q => q.replace(/^Question: /, ''));
	} else if (jsonData && typeof jsonData === 'object' && 'questions' in jsonData) {
		// Format: {questions: [{question: "text"}, ...]}
		return jsonData.questions.map(q => q.question);
	}
	throw new Error('Unsupported JSON format');
}

// Helper function to read and parse JSON files
function readJsonFile(filename: string): { questions: string[]; count: number } {
	const jsonFilePath = join(__dirname, '..', 'test-data', filename);
	const jsonContent = readFileSync(jsonFilePath, 'utf8');
	const jsonData = JSON.parse(jsonContent) as JsonData;
	const questions = extractQuestions(jsonData);
	return { questions, count: questions.length };
}

interface DatabaseResponse {
	results: Array<{
		rows: Array<Array<number | string>>;
	}>;
}

async function executeDatabaseQuery(query: string): Promise<DatabaseResponse> {
	const url = new URL(DATABASE_URL);

	// Check if this is a Neon database URL
	if (url.hostname.includes('neon.tech') || url.hostname.includes('neondb.net')) {
		// Use Neon Data API
		const neonEndpoint = `https://${url.hostname}/sql`;

		const response = await fetch(neonEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${url.password}`,
			},
			body: JSON.stringify({
				query,
				params: [],
			}),
		});

		if (!response.ok) {
			throw new Error(`Neon API request failed: ${response.status} ${response.statusText}`);
		}

		return response.json() as Promise<DatabaseResponse>;
	} else {
		// For local PostgreSQL, use pg client
		console.log(`Executing query: ${query}`);

		const pool = new Pool({
			connectionString: DATABASE_URL,
		});

		try {
			const result = await pool.query(query);
			await pool.end();

			return {
				results: [{
					rows: result.rows.map(row => Object.values(row as any))
				}]
			};
		} catch (error) {
			await pool.end();
			throw error;
		}
	}
}

test.describe('JSON vs Database Question Matching', () => {
	for (const filename of JSON_FILES) {
		test(`should verify question count comparison between ${filename} and database`, async () => {
			const { count: jsonQuestionCount } = readJsonFile(filename);
			console.log(`${filename} contains ${jsonQuestionCount} questions`);

			const query = 'SELECT COUNT(*) as count FROM "QuestionContent"';
			const result = await executeDatabaseQuery(query);

			const databaseQuestionCount = parseInt(result.results[0].rows[0][0] as string);
			console.log(`Database contains ${databaseQuestionCount} questions`);

			console.log(`\n Comparison:`);
			console.log(`   ${filename}: ${jsonQuestionCount} questions`);
			console.log(`   Database: ${databaseQuestionCount} questions`);
			console.log(`   Match: ${databaseQuestionCount === jsonQuestionCount ? '✅ Yes' : '❌ No'}`);
			expect(jsonQuestionCount).toBeGreaterThan(0);
			expect(databaseQuestionCount).toBeGreaterThan(0);
		});
	}

	// Test for each JSON file
	for (const filename of JSON_FILES) {
		test(`should verify ${filename} questions exist in database with detailed reporting`, async () => {
			const { questions, count: jsonQuestionCount } = readJsonFile(filename);

			let foundCount = 0;
			let notFoundCount = 0;
			const notFoundQuestions: string[] = [];

			console.log(`\n Checking ${jsonQuestionCount} questions from ${filename}...`);

			for (const questionText of questions) {
				const query = `SELECT COUNT(*) as count FROM "QuestionContent" WHERE content->>'text' = '${questionText.replace(/'/g, "''")}'`;
				const result = await executeDatabaseQuery(query);

				const count = parseInt(result.results[0].rows[0][0] as string);

				if (count === 0) {
					console.log(`❌ Question not found: ${questionText}`);
					notFoundCount++;
					notFoundQuestions.push(questionText);
				} else {
					console.log(`✅ Found question: ${questionText}`);
					foundCount++;
				}
			}

			console.log(`\n Final Results for ${filename}:`);
			console.log(`   ✅ Found: ${foundCount} questions`);
			console.log(`   ❌ Not Found: ${notFoundCount} questions`);
			console.log(`   Success Rate: ${((foundCount / jsonQuestionCount) * 100).toFixed(1)}%`);

			if (notFoundQuestions.length > 0) {
				console.log(`\n❌ Questions not found in database:`);
				notFoundQuestions.forEach((q, i) => console.log(`   ${i + 1}. ${q}`));
			}

			// Fail the test if ANY question is not found in the database
			expect(notFoundCount).toBe(0);
			expect(foundCount).toBe(jsonQuestionCount);
			expect(jsonQuestionCount).toBeGreaterThan(0);
		});
	}

	test('should verify database question structure and content', async () => {
		const query = 'SELECT "id", content, "createdAt", "updatedAt" FROM "QuestionContent" LIMIT 5';
		const result = await executeDatabaseQuery(query);

		expect(result.results[0].rows.length).toBeGreaterThan(0);

		console.log(`\n Database Structure Verification:`);
		console.log(`   Returned ${result.results[0].rows.length} sample questions`);

		const firstQuestion = result.results[0].rows[0];
		expect(firstQuestion).toHaveLength(4);
		expect(typeof firstQuestion[0]).toBe('string'); // id
		expect(typeof firstQuestion[1]).toBe('object'); // content (JSON object)
		expect(typeof firstQuestion[2]).toBe('object'); // createdAt (Date object)
		expect(typeof firstQuestion[3]).toBe('object'); // updatedAt (Date object)

		// Display sample question
		const questionText = ((firstQuestion[1] as any) as any).text;
		console.log(`   Sample question: "${questionText}"`);
		console.log(`   ✅ All structure validations passed`);
	});

	// Test for each JSON file
	for (const filename of JSON_FILES) {
		test(`should verify ${filename} structure and content`, async () => {
			const jsonFilePath = join(__dirname, '..', 'test-data', filename);
			console.log(`\n📁 Reading JSON file: ${jsonFilePath}`);

			const { questions, count: jsonQuestionCount } = readJsonFile(filename);

			expect(Array.isArray(questions)).toBe(true);
			expect(jsonQuestionCount).toBeGreaterThan(0);
			console.log(`${filename} contains ${jsonQuestionCount} questions`);

			// Verify all questions are valid strings
			for (const questionText of questions) {
				expect(typeof questionText).toBe('string');
				expect(questionText.length).toBeGreaterThan(0);
			}

			// Verify specific questions exist based on the file
			if (filename === 'TRUNCATED_Partners_Capital_Hunter_Point.json') {
				expect(questions).toContain('Has the IA made any changes to its insurance coverages?');
				expect(questions).toContain('Total number of employees as of December 31st, 2024.');
			} else if (filename === 'ddqs.json') {
				expect(questions).toContain('What is the firm name?');
				expect(questions).toContain('What is the legal fund name?');
			}

			console.log(`✅ ${filename} structure validation passed`);
		});
	}

	test('should fetch and display database questions with pagination', async () => {
		console.log('\n  Fetching questions from database...');
		const query = 'SELECT "id", content, "createdAt", "updatedAt" FROM "QuestionContent" ORDER BY "createdAt" DESC LIMIT 10';
		const result = await executeDatabaseQuery(query);

		console.log(`\n Database returned ${result.results[0].rows.length} recent questions:`);
		console.log('═'.repeat(80));

		result.results[0].rows.forEach((row, index) => {
			const [id, content, createdAt, updatedAt] = row;
			const questionText = ((content as any) as any).text;
			console.log(`\n${index + 1}. ID: ${id}`);
			console.log(`   Question: ${questionText}`);
			console.log(`   Created: ${createdAt}`);
			console.log(`   Updated: ${updatedAt}`);
			console.log('─'.repeat(80));
		});

		expect(result.results[0].rows.length).toBeGreaterThan(0);
		console.log(`✅ Database connection and query successful`);
	});
});
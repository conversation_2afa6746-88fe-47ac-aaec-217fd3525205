import path from "path";
import mammoth from "mammoth";

interface QaPair {
    question: string;
    answer: string;
    questionNumber: number;
}

// Interface for just questions (no answers)
export interface Question {
    question: string;
}

export class DocumentParsingError extends Error {
    constructor(message: string) {
        super(message);
        this.name = "DocumentParsingError";
    }
}

/**
 * Loads and parses a document file containing Q&A pairs.
 */

export async function loadDdq(
    fileName = "chat-ddq.docx",
    dir = path.resolve(__dirname, "../test-data"),
): Promise<QaPair[]> {
    try {
        const filePath = path.join(dir, fileName);
        const { value: rawText } = await mammoth.extractRawText({ path: filePath });
        if (!rawText) {
            throw new DocumentParsingError("Empty document or parsing failed");
        }
        const normalizedText = normalizeText(rawText);
        return extractQaPairs(normalizedText);
    } catch (error) {
        if (error instanceof DocumentParsingError) throw error;
        const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
        throw new DocumentParsingError(
            `Failed to parse ${fileName}: ${errorMessage}`,
        );
    }
}

/**
 * Loads and extracts just questions from a document file.
 * Use this for documents that don't follow the strict Q&A format.
 */
export async function loadQuestions(
    fileName: string,
    dir = path.resolve(__dirname, "../test-data"),
): Promise<Question[]> {
    try {
        const filePath = path.join(dir, fileName);
        const { value: rawText } = await mammoth.extractRawText({ path: filePath });
        if (!rawText) {
            throw new DocumentParsingError("Empty document or parsing failed");
        }

        return extractQuestionsOnly(rawText);
    } catch (error) {
        if (error instanceof DocumentParsingError) throw error;
        const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
        throw new DocumentParsingError(
            `Failed to parse ${fileName}: ${errorMessage}`,
        );
    }
}

function normalizeText(text: string): string {
    return text.replace(/\s+/g, " ").trim();
}

function extractQaPairs(text: string): QaPair[] {
    const QA_PATTERN =
        /Question\s*(\d+):\s*(.*?)\s*Answer\s*:\s*(.*?)(?=Question\s*\d+:|$)/gi;
    const pairs: QaPair[] = [];
    let match: RegExpExecArray | null;
    while ((match = QA_PATTERN.exec(text))) {
        const [, num, question, answer] = match;
        if (question?.trim() && answer?.trim()) {
            pairs.push({
                questionNumber: parseInt(num, 10),
                question: question.trim(),
                answer: answer.trim(),
            });
        }
    }

    if (pairs.length === 0) {
        throw new DocumentParsingError("No valid Q&A pairs found in document");
    }

    return pairs;
}

function extractQuestionsOnly(text: string): Question[] {
    // Split by newlines and filter out empty lines
    const lines = text.split(/\r?\n/)
        .map(line => line.trim())
        .filter(line => line.length > 0);

    const questions = lines.map(line => {
        const cleanedQuestion = line.replace(/^(Question\s*(\d+)?\s*:?\s*)|^(\d+\.\s*)/i, '').trim();
        return {
            question: cleanedQuestion
        };
    });

    if (questions.length === 0) {
        throw new DocumentParsingError("No questions found in document");
    }

    return questions;
}

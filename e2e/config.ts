// Environment variables
export const E2E_PLAYWRIGHT_USER_NAME = process.env.E2E_PLAYWRIGHT_USER_NAME!;
export const E2E_PLAYWRIGHT_USER_PASSWORD = process.env.E2E_PLAYWRIGHT_USER_PASSWORD!;
export const E2E_PLAYWRIGHT_PLUGIN_BASEURL = process.env.E2E_PLAYWRIGHT_PLUGIN_BASEURL!;
export const E2E_PLAYWRIGHT_BASEURL = process.env.E2E_PLAYWRIGHT_BASEURL!;
export const ANTHROPIC_MODEL = process.env.ANTHROPIC_MODEL!;
export const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY!;
export const E2E_TEST_ORG_ID = process.env.E2E_TEST_ORG_ID!;
export const DATABASE_URL = process.env.DATABASE_URL!;

// Validation
if (!E2E_PLAYWRIGHT_USER_NAME || !E2E_PLAYWRIGHT_USER_PASSWORD || !E2E_PLAYWRIGHT_PLUGIN_BASEURL || !E2E_PLAYWRIGHT_BASEURL) {
  throw new Error(
    "Missing required environment variables: E2E_PLAYWRIGHT_USER_NAME, E2E_PLAYWRIGHT_USER_PASSWORD, E2E_PLAYWRIGHT_PLUGIN_BASEURL, or E2E_PLAYWRIGHT_BASEURL"
  );
}

if (!ANTHROPIC_MODEL || !ANTHROPIC_API_KEY) {
  throw new Error(
    "Missing required environment variables: ANTHROPIC_MODEL or ANTHROPIC_API_KEY"
  );
}

if (!E2E_TEST_ORG_ID || !DATABASE_URL) {
  throw new Error(
    "Missing required environment variables: E2E_TEST_ORG_ID or DATABASE_URL"
  );
}
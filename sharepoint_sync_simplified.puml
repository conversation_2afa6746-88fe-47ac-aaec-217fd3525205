@startuml SharePoint Document Synchronization - Simplified

!theme plain
skinparam backgroundColor #FFFFFF
skinparam state {
    BackgroundColor #E8F4FD
    BorderColor #2E86AB
    FontColor #2E86AB
    FontSize 12
}

skinparam stateError {
    BackgroundColor #FFE6E6
    BorderColor #D32F2F
    FontColor #D32F2F
}

skinparam stateSuccess {
    BackgroundColor #E8F5E8
    BorderColor #388E3C
    FontColor #388E3C
}

title SharePoint Document Synchronization - Simplified State Machine

[*] --> WaitingForWebhook

state "Webhook Processing" as WebhookPhase {
    WaitingForWebhook --> ProcessingWebhook : Webhook Received
    ProcessingWebhook --> ValidatingWebhook : Parse Payload
    ValidatingWebhook --> WebhookValid : Valid
    ValidatingWebhook --> WaitingForWebhook : Invalid
    WebhookValid --> CallingDeltaAPI : Proceed
}

state "Delta API Processing" as DeltaPhase {
    CallingDeltaAPI --> ProcessingDeltaResponse : Success
    CallingDeltaAPI --> RetryDeltaAPI : API Error
    RetryDeltaAPI --> CallingDeltaAPI : Wait (exponential backoff)
    ProcessingDeltaResponse --> DocumentSyncPhase : Files Found
    ProcessingDeltaResponse --> WaitingForWebhook : No Changes
}

state "CRUD Document DB records" as SyncPhase {
    DocumentSyncPhase --> ProcessingDocument : Next Document
    ProcessingDocument --> CreateUpdateDeleteDocument : Process File
    CreateUpdateDeleteDocument --> SchedulingVectorization : Success
    CreateUpdateDeleteDocument --> DocumentSyncPhase : Error
    DocumentSyncPhase --> VectorizationPhase : All Documents Processed
}

state "Vectorization Processing" as VectorPhase {
    VectorizationPhase --> CheckingDocumentState : Next Task
    CheckingDocumentState --> DocumentReady : Not Processing
    CheckingDocumentState --> DocumentBusy : PROCESSING/GENERATING_ANSWERS
    DocumentBusy --> SchedulingRetry : Schedule Future Task
    SchedulingRetry --> VectorizationPhase : Continue
    DocumentReady --> VectorizingDocument : Process
    VectorizingDocument --> VectorizationSuccess : Success
    VectorizingDocument --> VectorizationError : Error
    VectorizationSuccess --> VectorizationPhase : Continue
    VectorizationError --> SchedulingRetry : Retry < Max
    VectorizationError --> VectorizationPhase : Max Retries
    VectorizationPhase --> WaitingForWebhook : All Tasks Complete
}

' Error handling
ProcessingWebhook --> WaitingForWebhook : Parse Error
CallingDeltaAPI --> WaitingForWebhook : Max Retries

note right of WaitingForWebhook
  Entry: Initialize webhook listener
  Exit: Clean up connections
end note

note right of ProcessingWebhook
  Entry: Validate webhook signature
  Exit: Parse webhook payload
end note

note right of CallingDeltaAPI
  Entry: Set up API client
  Exit: Store delta link
end note

note right of CreateUpdateDeleteDocument
  Entry: Determine operation type
  Exit: Update database record
end note

note right of CheckingDocumentState
  Entry: Query database state
  Exit: Determine next action
end note

note right of VectorizingDocument
  Entry: Set state to PROCESSING
  Exit: Update state to READY/ERROR
end note

@enduml 
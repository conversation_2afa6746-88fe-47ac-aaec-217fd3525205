{"name": "Notification", "steps": [{"branches": [{"conditions": {"all": [{"argument": "tag", "operator": "equal_to", "variable": "data.type"}]}, "name": "Tag", "steps": [{"ref": "batch_tag", "settings": {"batch_execution_mode": "accumulate", "batch_key": "batch_id", "batch_order": "asc", "batch_window": {"unit": "minutes", "value": 5}, "batch_window_type": "fixed"}, "type": "batch"}, {"branches": [{"conditions": {"all": [{"argument": "email", "operator": "contains", "variable": "data.medium"}]}, "name": "Resend channel", "steps": [{"channel_key": "resend", "channel_overrides": {"from_address": null, "reply_to_address": "{{ vars.reply_email }}"}, "ref": "tag_email", "template": {"settings": {"attachment_key": "attachments", "layout_key": "default"}, "subject": "{{ title }}", "text_body@": "tag_email/text_body.txt", "visual_blocks@": "tag_email/visual_blocks.json"}, "type": "channel"}], "terminates": false}, {"conditions": {"all": [{"argument": "slack", "operator": "contains", "variable": "data.medium"}]}, "name": "Slack channel", "steps": [{"channel_key": "slack", "ref": "chat_1", "template": {"markdown_body@": "chat_1/markdown_body.md"}, "type": "channel"}], "terminates": false}, {"conditions": {"all": [{"argument": "team", "operator": "contains", "variable": "data.medium"}]}, "name": "Team channel", "steps": [{"channel_key": "microsoft-teams", "ref": "chat_2", "template": {"markdown_body@": "chat_2/markdown_body.md"}, "type": "channel"}], "terminates": false}, {"name": "<PERSON><PERSON><PERSON>", "steps": [{"channel_key": "resend", "ref": "email_default", "template": {"preview_text": "", "settings": {"attachment_key": "attachments", "layout_key": "default"}, "subject": "{{ title }}", "text_body@": "email_default/text_body.txt", "visual_blocks@": "email_default/visual_blocks.json"}, "type": "channel"}], "terminates": false}], "name": "Notification channel - Tag", "ref": "branch_1", "type": "branch"}], "terminates": false}, {"conditions": {"all": [{"argument": "approval", "operator": "equal_to", "variable": "data.type"}]}, "name": "Approval", "steps": [{"ref": "batchhttps://dashboard.knock.app/sinh-asymmetry/development/workflows/notification/steps_approval", "settings": {"batch_execution_mode": "accumulate", "batch_key": "batch_id", "batch_order": "asc", "batch_window": {"unit": "minutes", "value": 5}, "batch_window_type": "fixed"}, "type": "batch"}, {"branches": [{"conditions": {"all": [{"argument": "email", "operator": "contains", "variable": "data.medium"}]}, "name": "Resend channel", "steps": [{"channel_key": "resend", "channel_overrides": {"from_address": null, "reply_to_address": "{{ vars.reply_email }}"}, "name": "Resend - Approval", "ref": "approval_email", "template": {"settings": {"attachment_key": "attachments", "layout_key": "default"}, "subject": "{{ title }}", "text_body@": "approval_email/text_body.txt", "visual_blocks@": "approval_email/visual_blocks.json"}, "type": "channel"}], "terminates": false}, {"name": "<PERSON><PERSON><PERSON>", "steps": [], "terminates": false}], "name": "Notification channel - Approval", "ref": "branch_3", "type": "branch"}], "terminates": false}, {"name": "<PERSON><PERSON><PERSON>", "steps": [], "terminates": false}], "name": "Notification type", "ref": "branch_2", "type": "branch"}], "trigger_data_json_schema": {"properties": {"link": {"description": "Link", "type": "string"}, "link_label": {"description": "Link Label", "type": "string"}, "medium": {"description": "email, slack or team", "type": "string"}, "message": {"description": "Message of actor", "type": "string"}, "objectId": {"description": "object id", "type": "string"}, "type": {"description": "tag", "type": "string"}}, "title": "Notification", "type": "object"}, "trigger_frequency": "every_trigger", "__readonly": {"environment": "development", "key": "notification", "active": true, "valid": true, "created_at": "2025-07-04T02:00:29.723652Z", "updated_at": "2025-07-17T05:42:15.904213Z", "sha": "CABj1+7m1dsUWu8pJnN4Gmv6bVDMG3A/hCsfOyKKt5M="}}
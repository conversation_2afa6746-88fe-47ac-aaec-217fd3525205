import * as z from "zod"
import { UserActivityType } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteUser, RelatedUserModel, CompleteResponse, RelatedResponseModel, CompleteQuestion, RelatedQuestionModel, CompleteDocument, RelatedDocumentModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const UserActivityModel = z.object({
  id: z.string(),
  type: z.nativeEnum(UserActivityType),
  memo: z.string().nullish(),
  metadata: jsonSchema,
  orgId: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  responseId: z.string().nullish(),
  questionId: z.string().nullish(),
  documentId: z.string().nullish(),
})

export interface CompleteUserActivity extends z.infer<typeof UserActivityModel> {
  org: CompleteOrg
  createdBy: CompleteUser
  response?: CompleteResponse | null
  question?: CompleteQuestion | null
  document?: CompleteDocument | null
}

/**
 * RelatedUserActivityModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedUserActivityModel: z.ZodSchema<CompleteUserActivity> = z.lazy(() => UserActivityModel.extend({
  org: RelatedOrgModel,
  createdBy: RelatedUserModel,
  response: RelatedResponseModel.nullish(),
  question: RelatedQuestionModel.nullish(),
  document: RelatedDocumentModel.nullish(),
}))

import * as z from "zod"
import { CompleteCategory, RelatedCategoryModel, CompleteDocument, RelatedDocumentModel } from "./index"

export const CategoryEntityConnectionModel = z.object({
  id: z.string(),
  reason: z.string(),
  categoryId: z.string(),
  documentId: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteCategoryEntityConnection extends z.infer<typeof CategoryEntityConnectionModel> {
  category: CompleteCategory
  document?: CompleteDocument | null
}

/**
 * RelatedCategoryEntityConnectionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedCategoryEntityConnectionModel: z.ZodSchema<CompleteCategoryEntityConnection> = z.lazy(() => CategoryEntityConnectionModel.extend({
  category: RelatedCategoryModel,
  document: RelatedDocumentModel.nullish(),
}))

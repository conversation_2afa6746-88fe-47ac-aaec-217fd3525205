import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteUser, RelatedUserModel, CompleteQuestion, RelatedQuestionModel, CompleteResponse, RelatedResponseModel } from "./index"

export const AssignedForApprovalModel = z.object({
  id: z.string(),
  orgId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deleted: z.boolean(),
  deletedAt: z.date().nullish(),
  createdById: z.string(),
  assignToUserId: z.string(),
  questionId: z.string(),
  responseId: z.string(),
})

export interface CompleteAssignedForApproval extends z.infer<typeof AssignedForApprovalModel> {
  org: CompleteOrg
  createdBy: CompleteUser
  assignToUser: CompleteUser
  question: CompleteQuestion
  response: CompleteResponse
}

/**
 * RelatedAssignedForApprovalModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAssignedForApprovalModel: z.ZodSchema<CompleteAssignedForApproval> = z.lazy(() => AssignedForApprovalModel.extend({
  org: RelatedOrgModel,
  createdBy: RelatedUserModel,
  assignToUser: RelatedUserModel,
  question: RelatedQuestionModel,
  response: RelatedResponseModel,
}))

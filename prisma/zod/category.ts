import * as z from "zod"
import { CategoryStatus } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel, CompleteCategoryEntityConnection, RelatedCategoryEntityConnectionModel } from "./index"

export const CategoryModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullish(),
  staleAfterDays: z.number().int().nullish(),
  seedId: z.string().nullish(),
  parentSeedId: z.string().nullish(),
  status: z.nativeEnum(CategoryStatus).nullish(),
  orgId: z.string(),
  parentId: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteCategory extends z.infer<typeof CategoryModel> {
  org: CompleteOrg
  parent?: CompleteCategory | null
  children: CompleteCategory[]
  documents: CompleteDocument[]
  CategoryEntityConnection: CompleteCategoryEntityConnection[]
}

/**
 * RelatedCategoryModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedCategoryModel: z.ZodSchema<CompleteCategory> = z.lazy(() => CategoryModel.extend({
  org: RelatedOrgModel,
  parent: RelatedCategoryModel.nullish(),
  children: RelatedCategoryModel.array(),
  documents: RelatedDocumentModel.array(),
  CategoryEntityConnection: RelatedCategoryEntityConnectionModel.array(),
}))

import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteUser, RelatedUserModel, CompleteQuestion, RelatedQuestionModel } from "./index"

export const AssignedQuestionModel = z.object({
  id: z.string(),
  orgId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  assignToUserId: z.string(),
  questionId: z.string(),
  createdById: z.string(),
  requiresApproval: z.boolean(),
})

export interface CompleteAssignedQuestion extends z.infer<typeof AssignedQuestionModel> {
  org: CompleteOrg
  assignToUser: CompleteUser
  question: CompleteQuestion
  createdBy: CompleteUser
}

/**
 * RelatedAssignedQuestionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAssignedQuestionModel: z.ZodSchema<CompleteAssignedQuestion> = z.lazy(() => AssignedQuestionModel.extend({
  org: RelatedOrgModel,
  assignToUser: RelatedUserModel,
  question: RelatedQuestionModel,
  createdBy: RelatedUserModel,
}))

import { faker } from "@faker-js/faker";
import {
  CorpType,
  Org,
  OrgType,
  ParticipantStatus,
  User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

const userList: User[] = [
  // Virgil and 25M users
  {
    id: faker.string.uuid(),
    clerkId: "user_2sUjTTykSMjgnKVlku2WgArrcqF",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Engineering Lead of Virgil AI",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXNSwZNmGr4hckRNdjKH0fcHxn",
    name: "<PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Crawford",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXhyaOYR8dhLUTxCOXi2gBSxIL",
    name: "Calista Reyes",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Calista",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sfowjPbIrwsdqlk5ZlwU74a84k",
    name: "Sanford Spivey",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Kathrine",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2tBa9CK2HuXrMLPGszT0AhWmRAx",
    name: "Dmitry Tokmakov",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Dmitry",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2vrf8GQfEtI06fTobjM5IYvU2Zg",
    name: "Sinh Nguyen",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Sinh",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // End Virgil and 25M users

  // HL Users

  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyQ9btxA980VqrKebInJ2Djz5",
    name: "Dave Gutheil",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Dave",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyTtIBELimzRmBr4gfgZVuLbH",
    name: "Jennifer Gold",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Jennifer",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyYAGCp4QrXHrbpCHT0eDA5Id",
    name: "Lauren David",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Lauren",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyaNEdW7VldP1PkwwC0qgjcFs",
    name: "Shahar Shpun",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Shahar",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyeUrVHGSy12SoFxS1PhjPKz6",
    name: "Tory Kulick",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Tory",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2ziyhhZ4Hb8pJ9FBEjVA1nmL6Qx",
    name: "Will Crooks",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Will",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // Chat bot user
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil AI",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserRole.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },
];

const org: Org = {
  id: faker.string.uuid(),
  name: "Hamilton Lane",
  clerkId: "org_2zioirtkvdmK0KfTypCrkfR4ROw",
  type: OrgType.Lender,
  corpType: CorpType.CCorp,
  createdById: "1",
  createdAt: new Date(),
  updatedAt: new Date(),
  incorporatedAt: faker.date.between({
    from: new Date("2020-01-01"),
    to: new Date("2022-01-01"),
  }),
  ein: faker.string.alphanumeric(10),
  bio: faker.lorem.paragraph(),
  addressId: "00ec2440-b084-4672-bc95-c9ba018520d1",
  webhookSecret: faker.string.alphanumeric(10),
  egnyteAccessTokenId: null,
  azureAccessTokenId: null,
};

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    console.log("user", user);

    await prisma.user.upsert({
      where: { email: user.email ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");
  await prisma.org.upsert({
    where: { id: org.id },
    update: {
      ...org,
    },
    create: {
      ...org,
      addressId: faker.helpers.arrayElement(addresses).id,
    },
  });

  const orgs = await prisma.org.findMany();

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: org.id,
        },
      },
      update: {
        userId: user.id,
        orgId: org.id,
      },
      create: {
        userId: user.id,
        orgId: org.id,
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  // Seed conversations
  for (const org of orgs) {
    for (const user of users) {
      const existingConversation = await prisma.conversation.count({
        where: {
          participants: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (existingConversation === 0) {
        await prisma.conversation.create({
          data: {
            createdAt: new Date(),
            updatedAt: new Date(),

            id: faker.string.uuid(),
            orgId: org.id,
            createdById: user.id,
            messages: {
              create: {
                body: "Hello, how can I help you today?",
                createdById: chatBotId ?? "",
                orgId: org.id,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            participants: {
              create: [
                {
                  userId: chatBotId ?? "",
                  status: ParticipantStatus.ONLINE,
                },
                {
                  userId: user.id,
                  status: ParticipantStatus.ONLINE,
                },
              ],
            },
          },
        });
      }
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });

// update Database connection string in .env.local
// npx tsx ./prisma/seed/add_user.ts --user-email <EMAIL>
import { Command } from "commander";
import { db as prisma } from "~/server/db";
import { userList } from "./users";

const CLERK_DEV_VIRGIL_AI_ORG_ID = "org_2lN93uPdFW3Ci7x0PiOyObyyAk5";
const write = true;

async function main() {
  const program = new Command();

  program
    .name("add-user")
    .description("Add a user to the database")
    .option(
      "-u, --user-email <address>",
      "user email to immitate, if not provided, will use the default testing user",
    )
    .version("1.0.0");

  program.parse();

  const options = program.opts();
  const userEmail = options.userEmail;
  if (!userEmail) {
    throw new Error("User email is required");
  }
  if (!write) {
    console.log("Skipping write");
    return;
  }

  const org = await prisma.org.findFirst({
    where: {
      clerkId: CLERK_DEV_VIRGIL_AI_ORG_ID,
    },
  });
  const orgId = org?.id;
  if (!orgId) {
    throw new Error("Org not found");
  }

  const user = userList.find((user) => user.email === userEmail);
  if (!user) {
    throw new Error("User not found");
  }

  const createdUser = await prisma.user.upsert({
    where: { email: user.email ?? "" },
    update: {
      ...user,
    },
    create: {
      ...user,
    },
  });

  console.log("createdUser: ", createdUser);

  const createdUserOrg = await prisma.userOrg.upsert({
    where: {
      userId_orgId: {
        userId: createdUser.id,
        orgId: orgId ?? "",
      },
    },
    update: {
      userId: createdUser.id,
      orgId: orgId ?? "",
    },
    create: {
      userId: createdUser.id,
      orgId: orgId ?? "",
    },
  });

  console.log("createdUserOrg: ", createdUserOrg);
}

main()
  .then(() => {
    console.log("User added to database");
  })
  .catch((error) => {
    console.error(error);
  });

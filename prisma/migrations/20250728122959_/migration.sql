-- CreateEnum
CREATE TYPE "WorkSheetStatus" AS ENUM ('PROCESSING', 'EXTRACTED', 'TABLES_EXTRACTED', 'GENERATING_ANSWERS', 'READY', 'ERROR');

-- AlterTable
ALTER TABLE "Response" ADD COLUMN     "workSheetTableId" TEXT;

-- AlterTable
ALTER TABLE "WorkSheet" ADD COLUMN     "status" "WorkSheetStatus" NOT NULL DEFAULT 'PROCESSING';

-- Add<PERSON><PERSON>ign<PERSON>ey
ALTER TABLE "Response" ADD CONSTRAINT "Response_workSheetTableId_fkey" FOREIGN KEY ("workSheetTableId") REFERENCES "WorkSheetTable"("id") ON DELETE CASCADE ON UPDATE CASCADE;

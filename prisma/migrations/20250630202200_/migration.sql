-- CreateTable
CREATE TABLE "AssignedQuestion" (
    "id" TEXT NOT NULL,
    "orgId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "assignToUserId" TEXT NOT NULL,
    "questionId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "AssignedQuestion_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AssignedQuestion" ADD CONSTRAINT "AssignedQuestion_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssignedQuestion" ADD CONSTRAINT "AssignedQuestion_assignToUserId_fkey" FOREIGN KEY ("assignToUserId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "AssignedQuestion" ADD CONSTRAINT "AssignedQuestion_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AssignedQuestion" ADD CONSTRAINT "AssignedQuestion_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

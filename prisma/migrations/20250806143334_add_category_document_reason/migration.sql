-- CreateTable
CREATE TABLE "CategoryEntityConnection" (
    "id" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "categoryId" TEXT NOT NULL,
    "documentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CategoryEntityConnection_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "CategoryEntityConnection" ADD CONSTRAINT "CategoryEntityConnection_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CategoryEntityConnection" ADD CONSTRAINT "CategoryEntityConnection_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDA<PERSON> CASCADE;

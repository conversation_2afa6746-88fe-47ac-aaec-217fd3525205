#!/bin/bash

# Document Classification Script
# This script runs the document classification for all datasets

echo "🚀 Starting document classification..."

# Create output directories
mkdir -p data/vistria/output
mkdir -p data/bottleneck-credit-fund/output  
mkdir -p data/gallatin/output

# Run classification for each dataset
echo "📁 Processing Vistria dataset..."
npx tsx --require dotenv/config 'src/lib/classifier.spec.ts' -o "data/vistria/output/document-categories.json"

echo "📁 Processing Bottleneck dataset..."
npx tsx --require dotenv/config 'src/lib/classifier.spec.ts' -o "data/bottleneck-credit-fund/output/document-categories.json"

echo "📁 Processing Gallatin dataset..."
npx tsx --require dotenv/config 'src/lib/classifier.spec.ts' -o "data/gallatin/output/document-categories.json"

echo "✅ Document classification complete!"
echo "📊 Results saved to:"
echo "   - data/vistria/output/document-categories.json"
echo "   - data/bottleneck-credit-fund/output/document-categories.json"
echo "   - data/gallatin/output/document-categories.json"
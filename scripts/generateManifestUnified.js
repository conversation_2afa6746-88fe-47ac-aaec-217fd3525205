import fs from "fs";
import Handlebars from "handlebars";
import path from "path";

// Function to read a file and return its content as a string
function readFileSync(filePath) {
  return fs.readFileSync(filePath, "utf8");
}

// Function to write a string to a file
function writeFileSync(filePath, data) {
  fs.writeFileSync(filePath, data);
}

// Get command-line arguments
const [env] = process.argv.slice(2);

// Validate arguments
if (!env) {
  console.error("Usage: node generateManifest.js <env>");
  process.exit(1);
}

try {
  const envConfigPath = path.join(
    "./ms-office-addin/manifests/env",
    `${env}.json`,
  );
  const commonConfigPath = path.join(
    "./ms-office-addin/manifests/env",
    "common.json",
  );
  const templatePath = path.join(
    "./ms-office-addin/manifests/templates/common.hbs",
  );
  const outputPath = path.join(
    "./ms-office-addin/manifests/dist/unified",
    `manifest.${env}.xml`,
  );

  // Ensure config file exists
  if (!fs.existsSync(envConfigPath)) {
    console.error(
      `Error: Environment config file not found at ${envConfigPath}`,
    );
    process.exit(1);
  }

  // Load the template
  const templateSource = readFileSync(templatePath);
  const template = Handlebars.compile(templateSource, {
    noEscape: true,
  });

  // Load and merge the config data
  const commonData = JSON.parse(readFileSync(commonConfigPath));
  const envData = JSON.parse(readFileSync(envConfigPath));
  const appDomains = new Set([...commonData.appDomains, ...envData.appDomains]);
  const data = {
    ...commonData,
    ...envData,
    appDomains: Array.from(appDomains),
  };

  // Generate the XML
  const xmlOutput = template(data);

  // Ensure output directory exists
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Write the XML to a file
  writeFileSync(outputPath, xmlOutput);

  console.log(`Manifest ${env} generated successfully: ${outputPath}`);
} catch (error) {
  console.error("Error generating manifest:", error);
  process.exit(1);
}

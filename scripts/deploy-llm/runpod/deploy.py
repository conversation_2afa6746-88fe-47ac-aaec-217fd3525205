import os
import runpod
import json

# --- Configuration ---
# 1. SET YOUR API KEY
# It's best practice to set this as an environment variable for security.
# On Linux/macOS: export RUNPOD_API_KEY="YOUR_API_KEY"
# On Windows: set RUNPOD_API_KEY="YOUR_API_KEY"

API_KEY = os.environ.get("RUNPOD_API_KEY")

# 2. HUGGING FACE TOKEN (Optional)
# Required only if the model is private or gated on Hugging Face.
HF_TOKEN = os.environ.get("HF_TOKEN", "")

# 3. ENDPOINT & TEMPLATE CONFIGURATION
# Give your endpoint and template unique names.
# ENDPOINT_NAME = "qwen3-235b-h200-endpoint"
# TEMPLATE_NAME = "qwen3-235b-vllm-template" # A name for the reusable worker config

# ENDPOINT_NAME = "qwen3-235b-gguf-h200-endpoint"
# TEMPLATE_NAME = "qwen3-235b-gguf-vllm-template" # A name for the reusable worker config


ENDPOINT_NAME = "qwen3-30b-h200-endpoint"
TEMPLATE_NAME = "qwen3-30b-vllm-template" # A name for the reusable worker config

# Specify the Hugging Face model you want to deploy.
MODEL_NAME = "Qwen/Qwen3-30B-A3B-Instruct-2507"
# MODEL_NAME = "Qwen/Qwen3-235B-A22B-Instruct-2507"
# MODEL_NAME = "unsloth/Qwen3-235B-A22B-Instruct-2507-GGUF"

# 4. GPU CONFIGURATION
# IMPORTANT: You must get the exact GPU ID from the RunPod UI when creating an endpoint.
# This value can change. Provide a comma-separated list for fallbacks.
GPU_IDS = "NVIDIA H200,HOPPER_141"

# 4. VOLUME CONFIGURATION
CONTAINER_DISK_IN_GB = 2000
VOLUME_NAME = "runpod-volume"

# 4. LOCATION CONFIGURATION
LOCATIONS = "US-KS-2,US-TX-3,US-MO-1,US-IL-1,US-CA-2,US-GA-2,US-NC-1,US-TX-4,US-WA-1"

# 5. vLLM WORKER CONFIGURATION
# The official RunPod vLLM worker image. Check for newer versions on Docker Hub.
DOCKER_IMAGE = "runpod/worker-v1-vllm:v2.7.0stable-cuda12.1.0"

# Tensor parallel size for the model. For a 30B model, 2-4 GPUs work well.
# A value of 2 is optimal for H200s with 30B model.
TENSOR_PARALLEL_SIZE = 2

# Maximum model length (context window).
MAX_MODEL_LEN = 32768



# --- Script Logic ---

def create_serverless_endpoint_with_sdk():
    """
    Uses the RunPod Python SDK to create a new serverless endpoint in two steps:
    1. Create a template for the worker configuration.
    2. Create an endpoint from that template.
    """
    if not API_KEY:
        print("🛑 ERROR: RUNPOD_API_KEY environment variable not set.")
        print("Please set your API key and try again.")
        return

    print(f"🚀 Initializing RunPod SDK...")
    runpod.api_key = API_KEY

    # Define the environment variables for the vLLM worker container.
    env_vars = {
        "MODEL_NAME": MODEL_NAME,
        "TENSOR_PARALLEL_SIZE": str(TENSOR_PARALLEL_SIZE),
        "MAX_MODEL_LEN": str(MAX_MODEL_LEN),
    }

    # Add Hugging Face token to env if it's provided.
    if HF_TOKEN:
        env_vars["HF_TOKEN"] = HF_TOKEN

    try:
        # --- Step 1: Create the Template ---
        print(f"📄 Creating template '{TEMPLATE_NAME}'...")
        new_template = runpod.create_template(
            name=TEMPLATE_NAME,
            image_name=DOCKER_IMAGE,
            env=env_vars,
            is_serverless=True,
            container_disk_in_gb=CONTAINER_DISK_IN_GB,
            volume_name=VOLUME_NAME,
        )
        template_id = new_template['id']
        print(f"   ✅ Template created with ID: {template_id}")

        # --- Step 2: Create the Endpoint from the Template ---
        print(f"🛰️  Creating endpoint '{ENDPOINT_NAME}' from template...")
        new_endpoint = runpod.create_endpoint(
            name=ENDPOINT_NAME,
            template_id=template_id,
            gpu_ids=GPU_IDS,
            workers_min=1,
            workers_max=1,
            gpu_count=2,
            locations=LOCATIONS,
        )

        print("\n✅ Success! Endpoint creation initiated.")
        print(json.dumps(new_endpoint, indent=4))
        print("\nIt will take a few minutes for the endpoint to become active.")
        print("You can monitor its status in the RunPod web console.")

    except Exception as err:
        print(f"\n🛑 An error occurred: {err}")


if __name__ == "__main__":
    create_serverless_endpoint_with_sdk()

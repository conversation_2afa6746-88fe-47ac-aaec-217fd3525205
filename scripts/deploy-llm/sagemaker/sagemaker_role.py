#!/usr/bin/env python3
"""
Fix SageMaker execution role trust policy
"""

import boto3
import json

def create_sagemaker_role():
    """Create SageMaker execution role"""
    
    iam = boto3.client('iam')
    role_name = 'VirgilSageMakerExecutionRole'
    
    # Trust policy for SageMaker
    trust_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "sagemaker.amazonaws.com"
                },
                "Action": "sts:AssumeRole"
            }
        ]
    }
    
    try:
        # Create role
        response = iam.create_role(
            RoleName=role_name,
            AssumeRolePolicyDocument=json.dumps(trust_policy),
            Description='Execution role for Virgil SageMaker endpoints'
        )
        
        role_arn = response['Role']['Arn']
        print(f"✅ Created role: {role_arn}")
        
        # Attach policies
        policies = [
            'arn:aws:iam::aws:policy/AmazonSageMakerFullAccess',
            'arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess'
        ]
        
        for policy in policies:
            iam.attach_role_policy(
                RoleName=role_name,
                PolicyArn=policy
            )
            print(f"✅ Attached policy: {policy}")
        
        return role_arn
        
    except Exception as e:
        print(f"❌ Error creating role: {str(e)}")
        raise

if __name__ == "__main__":
    role_arn = create_sagemaker_role()
    print(f"\n📝 Add this to your .env file:")
    print(f"SAGEMAKER_EXECUTION_ROLE={role_arn}")
#!/usr/bin/env python3
"""
AWS SageMaker LLM Deployment Script
Deploys vLLM-compatible models to SageMaker Real-time or Serverless Inference
"""

import boto3
import json
import time
import os

# Configuration
ENDPOINT_NAME = f"virgil-qwen3-endpoint-4b-instruct-2507"
MODEL_NAME = f"virgil-qwen3-model-4b-instruct-2507"
ENDPOINT_CONFIG_NAME = f"virgil-qwen3-config-4b-instruct-2507"

# Model configuration
HUGGING_FACE_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
INSTANCE_TYPE = "ml.g5.xlarge"  # Required for 4B model
INITIAL_INSTANCE_COUNT = 1

# Container image for vLLM on SageMaker
import boto3
region = boto3.Session().region_name
# Using vLLM container which supports Qwen3
CONTAINER_IMAGE = f"763104351884.dkr.ecr.{region}.amazonaws.com/djl-inference:0.28.0-deepspeed0.12.6-cu121"

def get_sagemaker_client():
    """Initialize SageMaker client"""
    return boto3.client('sagemaker')

def create_model(sagemaker_client, model_name, container_image, model_data_url=None):
    """Create SageMaker model"""
    
    # Environment variables for vLLM container
    environment = {
        'OPTION_MODEL_ID': HUGGING_FACE_MODEL,
        'OPTION_TENSOR_PARALLEL_DEGREE': '1',
        'OPTION_MAX_MODEL_LEN': '8192',
        'OPTION_DTYPE': 'auto',
        'OPTION_TRUST_REMOTE_CODE': 'true',
        'OPTION_ENFORCE_EAGER': 'true'
    }
    
    # If you have a custom model in S3, specify model_data_url
    container_def = {
        'Image': container_image,
        'Environment': environment
    }
    
    if model_data_url:
        container_def['ModelDataUrl'] = model_data_url
    
    try:
        response = sagemaker_client.create_model(
            ModelName=model_name,
            PrimaryContainer=container_def,
            ExecutionRoleArn=get_execution_role()
        )
        print(f"✅ Model created: {model_name}")
        return response
    except Exception as e:
        if "already exists" in str(e):
            print(f"⚠️  Model {model_name} already exists")
        else:
            raise e

def create_endpoint_config(sagemaker_client, endpoint_config_name, model_name, instance_type, initial_instance_count):
    """Create SageMaker endpoint configuration"""
    
    try:
        response = sagemaker_client.create_endpoint_config(
            EndpointConfigName=endpoint_config_name,
            ProductionVariants=[
                {
                    'VariantName': 'primary',
                    'ModelName': model_name,
                    'InitialInstanceCount': initial_instance_count,
                    'InstanceType': instance_type,
                    'InitialVariantWeight': 1.0
                }
            ]
        )
        print(f"✅ Endpoint configuration created: {endpoint_config_name}")
        return response
    except Exception as e:
        if "already exists" in str(e):
            print(f"⚠️  Endpoint configuration {endpoint_config_name} already exists")
        else:
            raise e

def create_endpoint(sagemaker_client, endpoint_name, endpoint_config_name):
    """Create SageMaker endpoint"""
    
    try:
        response = sagemaker_client.create_endpoint(
            EndpointName=endpoint_name,
            EndpointConfigName=endpoint_config_name
        )
        print(f"✅ Endpoint creation initiated: {endpoint_name}")
        return response
    except Exception as e:
        if "already exists" in str(e):
            print(f"⚠️  Endpoint {endpoint_name} already exists")
        else:
            raise e

def wait_for_endpoint(sagemaker_client, endpoint_name):
    """Wait for endpoint to be in service"""
    
    print(f"⏳ Waiting for endpoint {endpoint_name} to be in service...")
    
    while True:
        response = sagemaker_client.describe_endpoint(EndpointName=endpoint_name)
        status = response['EndpointStatus']
        
        if status == 'InService':
            print(f"✅ Endpoint {endpoint_name} is now in service!")
            break
        elif status == 'Failed':
            print(f"❌ Endpoint {endpoint_name} failed to deploy")
            print(f"Failure reason: {response.get('FailureReason', 'Unknown')}")
            break
        else:
            print(f"⏳ Endpoint status: {status}")
            time.sleep(30)

def get_execution_role():
    """Get SageMaker execution role ARN"""
    
    # Try to get from environment variable first
    role_arn = os.environ.get('SAGEMAKER_EXECUTION_ROLE')
    
    if role_arn:
        return role_arn
    
    # Try to get the Virgil-specific role
    try:
        iam = boto3.client("iam")
        response = iam.get_role(RoleName="VirgilSageMakerExecutionRole")
        return response["Role"]["Arn"]
    except:
        # Construct role ARN as fallback
        account_id = boto3.client('sts').get_caller_identity()['Account']
        return f"arn:aws:iam::{account_id}:role/VirgilSageMakerExecutionRole"

def test_endpoint(endpoint_name):
    """Test the deployed endpoint"""
    
    runtime_client = boto3.client('sagemaker-runtime')
    
    test_payload = {
        "inputs": "What is the capital of France?",
        "parameters": {
            "temperature": 0.7,
            "max_new_tokens": 100,
            "do_sample": True
        }
    }
    
    try:
        response = runtime_client.invoke_endpoint(
            EndpointName=endpoint_name,
            ContentType='application/json',
            Body=json.dumps(test_payload)
        )
        
        result = json.loads(response['Body'].read().decode())
        print(f"✅ Test successful!")
        print(f"Response: {result}")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")

def main():
    """Main deployment function"""
    
    print("🚀 Starting SageMaker LLM deployment...")
    print(f"Model: {HUGGING_FACE_MODEL}")
    print(f"Endpoint: {ENDPOINT_NAME}")
    print(f"Instance Type: {INSTANCE_TYPE}")
    
    # Ensure execution role exists
    try:
        role_arn = get_execution_role()
        print(f"📋 Using execution role: {role_arn}")
    except Exception as e:
        print(f"❌ Role issue: {str(e)}")
        print("💡 Run 'python create_role.py' first to create the execution role")
        return
    
    sagemaker_client = get_sagemaker_client()
    
    # Step 1: Create model
    print("\n📦 Creating SageMaker model...")
    create_model(sagemaker_client, MODEL_NAME, CONTAINER_IMAGE)
    
    # Step 2: Create endpoint configuration
    print("\n⚙️  Creating endpoint configuration...")
    create_endpoint_config(
        sagemaker_client, 
        ENDPOINT_CONFIG_NAME, 
        MODEL_NAME, 
        INSTANCE_TYPE, 
        INITIAL_INSTANCE_COUNT
    )
    
    # Step 3: Create endpoint
    print("\n🛰️  Creating endpoint...")
    create_endpoint(sagemaker_client, ENDPOINT_NAME, ENDPOINT_CONFIG_NAME)
    
    # Step 4: Wait for endpoint to be ready
    wait_for_endpoint(sagemaker_client, ENDPOINT_NAME)
    
    # Step 5: Test endpoint
    print("\n🧪 Testing endpoint...")
    test_endpoint(ENDPOINT_NAME)
    
    print(f"\n✅ Deployment complete!")
    print(f"Endpoint Name: {ENDPOINT_NAME}")
    print(f"Region: {boto3.Session().region_name}")
    
    print(f"\n📝 Update your .env file:")
    print(f"SAGEMAKER_ENDPOINT_NAME={ENDPOINT_NAME}")
    print(f"SAGEMAKER_REGION={boto3.Session().region_name}")
    print(f"SAGEMAKER_ENABLE=true")

if __name__ == "__main__":
    main()
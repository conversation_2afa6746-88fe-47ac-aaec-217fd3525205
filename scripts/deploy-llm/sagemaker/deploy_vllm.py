#!/usr/bin/env python3
"""
Deploy Qwen3-4B-Instruct-2507 using vLLM on SageMaker
"""

import boto3
import json
import time
from datetime import datetime
from sagemaker import Model
import os


def get_execution_role():
    """Get SageMaker execution role ARN"""
    
    # Try to get from environment variable first
    role_arn = os.environ.get('SAGEMAKER_EXECUTION_ROLE')
    
    if role_arn:
        return role_arn
    
    # Try to get the Virgil-specific role
    try:
        iam = boto3.client("iam")
        response = iam.get_role(RoleName="VirgilSageMakerExecutionRole")
        return response["Role"]["Arn"]
    except:
        # Construct role ARN as fallback
        account_id = boto3.client('sts').get_caller_identity()['Account']
        return f"arn:aws:iam::{account_id}:role/VirgilSageMakerExecutionRole"

# Configuration
timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
ENDPOINT_NAME = f"virgil-qwen3-vllm-{timestamp}"
MODEL_NAME = f"virgil-qwen3-vllm-model-{timestamp}"

# Model configuration - Choose one:
# For 4B model (1 GPU):
HUGGING_FACE_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
INSTANCE_TYPE = "ml.g5.xlarge"  # 1 GPU

# For 7B model (1 GPU) - fits in 24GB VRAM:
# HUGGING_FACE_MODEL = "Qwen/Qwen2.5-7B-Instruct"
# INSTANCE_TYPE = "ml.g5.xlarge"  # 1 GPU

# For 30B model (4 GPUs) - uncomment these lines:
# HUGGING_FACE_MODEL = "Qwen/Qwen3-30B-A3B-Instruct-2507"
# INSTANCE_TYPE = "ml.g5.12xlarge"  # 4 GPUs 

# vLLM container image
region = boto3.Session().region_name
CONTAINER_IMAGE = f"************.dkr.ecr.{region}.amazonaws.com/djl-inference:0.27.0-deepspeed0.12.6-cu121"

def deploy_model():
    """Deploy the model using SageMaker Python SDK"""
    
    print(f"🚀 Deploying {HUGGING_FACE_MODEL} with vLLM...")
    
    # Environment variables for vLLM
    # Adjust based on model size:
    tensor_parallel = '4' if '30B' in HUGGING_FACE_MODEL else '1'
    max_len = '32768' if '30B' in HUGGING_FACE_MODEL else '16384'
    gpu_util = '0.9'  # 4B model uses less memory
    
    env = {
        'OPTION_MODEL_ID': HUGGING_FACE_MODEL,
        'OPTION_TENSOR_PARALLEL_DEGREE': tensor_parallel,
        'OPTION_MAX_MODEL_LEN': max_len,
        'OPTION_DTYPE': 'bfloat16',
        'OPTION_TRUST_REMOTE_CODE': 'true',
        'OPTION_GPU_MEMORY_UTILIZATION': gpu_util
    }
    
    try:
        # Create model
        model = Model(
            image_uri=CONTAINER_IMAGE,
            model_data=None,  # Model will be downloaded from HuggingFace
            role=get_execution_role(),
            name=MODEL_NAME,
            env=env
        )
        
        print(f"📦 Created model: {MODEL_NAME}")
        
        # Deploy to endpoint
        print(f"🛰️ Deploying to endpoint: {ENDPOINT_NAME}")
        model.deploy(
            initial_instance_count=1,
            instance_type=INSTANCE_TYPE,
            endpoint_name=ENDPOINT_NAME,
            wait=True
        )
        print(f"✅ Deployment successful!")
        
        print(f"\n✅ Deployment complete!")
        print(f"Endpoint Name: {ENDPOINT_NAME}")
        print(f"Region: {region}")
        print(f"Model: {HUGGING_FACE_MODEL}")
        
        print(f"\n📝 Update your .env file:")
        print(f"SAGEMAKER_ENDPOINT_NAME={ENDPOINT_NAME}")
        print(f"SAGEMAKER_MODEL={HUGGING_FACE_MODEL}")
        
        return ENDPOINT_NAME
        
    except Exception as e:
        print(f"❌ Deployment failed: {str(e)}")
        print("\n💡 Troubleshooting tips:")
        print("- Check if you have quota for ml.g5.12xlarge instances")
        print("- Verify your execution role has proper permissions")
        print("- The 30B model requires significant resources and time to deploy")
        raise

if __name__ == "__main__":
    deploy_model()
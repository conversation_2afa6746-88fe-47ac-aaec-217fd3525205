import { Command } from "commander";
import fs from "fs";

async function main() {
  const program = new Command();

  program
    .name("manifest")
    .description("Generate Microsoft Add-in XML for new client")
    .option(
      "-s, --subdomain <subdomain>",
      "subdomain to generate manifest for, i.e. vistria",
    )
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const { subdomain } = options;

  if (!subdomain) {
    console.error("Subdomain is required");
    process.exit(1);
  }

  const manifestTemplate = fs.readFileSync(
    "./ms-office-addin/manifests/templates/client-template.json",
    "utf8",
  );

  const manifest = manifestTemplate.replaceAll("SUBDOMAIN", subdomain);

  fs.writeFileSync(
    `./ms-office-addin/manifests/env/${subdomain}.json`,
    manifest,
  );
}

main().catch(console.error);

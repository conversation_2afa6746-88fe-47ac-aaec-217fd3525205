#!/usr/bin/env node

const https = require("https");
const http = require("http");
require("dotenv").config();

// Configuration
const CRON_SECRET = process.env.CRON_SECRET;
const NGROK_DOMAIN = process.env.NGROK_DOMAIN;
const FREQUENCY_SECONDS = parseInt(process.argv[2] || "120", 10);
const API_ENDPOINT = process.argv[3] || "api/cron";

// Validation
if (!CRON_SECRET) {
  console.error("❌ CRON_SECRET environment variable is required");
  process.exit(1);
}

if (!NGROK_DOMAIN) {
  console.error("❌ NGROK_DOMAIN environment variable is required");
  process.exit(1);
}

if (FREQUENCY_SECONDS < 1) {
  console.error("❌ FREQUENCY_SECONDS must be at least 1 second");
  process.exit(1);
}

const cronUrl = `https://${NGROK_DOMAIN}/${API_ENDPOINT}`;

console.log(`🚀 Starting cron runner`);
console.log(`📍 Target URL: ${cronUrl}`);
console.log(`⏰ Frequency: ${FREQUENCY_SECONDS} seconds (from command line)`);
console.log(`🔐 Using Bearer token: ${CRON_SECRET.substring(0, 8)}...`);

let requestCount = 0;
let successCount = 0;
let errorCount = 0;

// Function to make the HTTP request
async function makeCronRequest() {
  requestCount++;
  const startTime = Date.now();

  console.log(
    `\n📡 Making request #${requestCount} at ${new Date().toISOString()}`,
  );

  return new Promise((resolve) => {
    const url = new URL(cronUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === "https:" ? 443 : 80),
      path: url.pathname,
      method: "GET",
      headers: {
        Authorization: `Bearer ${CRON_SECRET}`,
        "User-Agent": "CronRunner/1.0",
        Accept: "application/json",
      },
      timeout: 30000, // 30 second timeout
    };

    const client = url.protocol === "https:" ? https : http;

    const req = client.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        const duration = Date.now() - startTime;

        // @ts-ignore
        if (res.statusCode >= 200 && res.statusCode < 300) {
          successCount++;
          console.log(
            `✅ Request #${requestCount} successful (${res.statusCode}) - ${duration}ms`,
          );

          try {
            const response = JSON.parse(data);
            if (response.success) {
              console.log(`   📋 Response: ${JSON.stringify(response)}`);
            }
          } catch (e) {
            console.log(
              `   📋 Response: ${data.substring(0, 200)}${data.length > 200 ? "..." : ""}`,
            );
          }
        } else {
          errorCount++;
          console.log(
            `❌ Request #${requestCount} failed (${res.statusCode}) - ${duration}ms`,
          );
          console.log(
            `   📋 Response: ${data.substring(0, 200)}${data.length > 200 ? "..." : ""}`,
          );
        }

        // @ts-ignore
        resolve();
      });
    });

    req.on("error", (error) => {
      const duration = Date.now() - startTime;
      errorCount++;
      console.log(`❌ Request #${requestCount} error - ${duration}ms`);
      console.log(`   🔍 Error: ${error.message}`);
      // @ts-ignore
      resolve();
    });

    req.on("timeout", () => {
      const duration = Date.now() - startTime;
      errorCount++;
      console.log(`⏰ Request #${requestCount} timeout - ${duration}ms`);
      req.destroy();
      // @ts-ignore
      resolve();
    });

    req.end();
  });
}

// Main execution loop
// @ts-ignore
let intervalId;

async function startCronRunner() {
  // Calculate when the next interval should start
  const now = new Date();
  const nextInterval = new Date(now.getTime() + FREQUENCY_SECONDS * 1000);

  console.log(`⏰ Next request scheduled for: ${nextInterval.toISOString()}`);

  // Calculate delay until next interval
  const delayMs =
    FREQUENCY_SECONDS * 1000 - (now.getTime() % (FREQUENCY_SECONDS * 1000));

  console.log(
    `⏳ Waiting ${Math.round(delayMs / 1000)} seconds until next interval...`,
  );

  // Wait until the next interval before making the first request
  setTimeout(async () => {
    console.log(
      `🚀 Starting first request at scheduled time: ${new Date().toISOString()}`,
    );
    await makeCronRequest();

    // Set up interval for subsequent requests
    intervalId = setInterval(async () => {
      await makeCronRequest();
    }, FREQUENCY_SECONDS * 1000);
  }, delayMs);
}

// Graceful shutdown handling
// @ts-ignore
function shutdown(signal) {
  console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);

  // @ts-ignore
  if (intervalId) {
    clearInterval(intervalId);
  }

  console.log(`📊 Final stats:`);
  console.log(`   Total requests: ${requestCount}`);
  console.log(`   Successful: ${successCount}`);
  console.log(`   Failed: ${errorCount}`);
  console.log(
    `   Success rate: ${requestCount > 0 ? ((successCount / requestCount) * 100).toFixed(1) : 0}%`,
  );

  process.exit(0);
}

// Handle shutdown signals
process.on("SIGINT", () => shutdown("SIGINT"));
process.on("SIGTERM", () => shutdown("SIGTERM"));

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught exception:", error);
  shutdown("uncaughtException");
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled rejection at:", promise, "reason:", reason);
  shutdown("unhandledRejection");
});

// Start the cron runner
startCronRunner().catch((error) => {
  console.error("💥 Failed to start cron runner:", error);
  process.exit(1);
});

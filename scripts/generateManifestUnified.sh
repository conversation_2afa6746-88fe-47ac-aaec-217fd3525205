#!/bin/bash

# Get command line arguments
env=$1

# Validate arguments
if [ -z "$env" ]; then
  echo "Usage: $0 <env>"
  exit 1
fi

# Check if environment config exists
if [ ! -f "./ms-office-addin/manifests/env/${env}.json" ]; then
  echo "Error: Environment config file not found at ./ms-office-addin/manifests/env/${env}.json"
  echo "Available environments:"
  ls -1 ./ms-office-addin/manifests/env/*.json | sed 's/.*\/\([^/]*\)\.json$/\1/'
  exit 1
fi

# Generate manifest
node scripts/generateManifestUnified.js $env

# Validate manifest
office-addin-manifest validate ./ms-office-addin/manifests/dist/unified/manifest.$env.xml

// Usage:
// npx tsx scripts/download-remote-file.ts <documentId>
// Make sure to set the DATABASE_URL to the remote database

import { getDocumentContents } from "~/lib/fileUtils";
import { db } from "~/server/db";
import fs from "fs";
import path from "path";

const documentId = process.argv[2];

if (!documentId) {
  console.error("Document ID is required");
  process.exit(1);
}

async function downloadFile(documentId: string) {
  const document = await db.document.findUniqueOrThrow({
    where: { id: documentId },
  });

  console.log("document", document);

  const { body, contentType } = await getDocumentContents(
    document,
    db,
    process.env.AWS_S3_BUCKET_NAME,
  );

  console.log("contentType", contentType);

  fs.writeFileSync(
    path.basename(document.name) ?? "downloaded-file",
    Buffer.from(body),
  );
}

downloadFile(documentId)
  .then(() => {
    console.log("File downloaded");
    process.exit(0);
  })
  .catch((err) => {
    console.error(err);
    process.exit(1);
  });

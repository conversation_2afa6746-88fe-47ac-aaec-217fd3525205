name: Deploy All Customers (Manual)

on:
  workflow_dispatch:
    inputs:
      deploy_25m:
        description: "Deploy 25Madison"
        required: false
        default: true
        type: boolean
      deploy_alpine:
        description: "Deploy Alpine"
        required: false
        default: true
        type: boolean
      deploy_evercore:
        description: "Deploy Evercore"
        required: false
        default: true
        type: boolean
      deploy_hpc:
        description: "Deploy HPC"
        required: false
        default: true
        type: boolean
      deploy_oha:
        description: "Deploy OHA"
        required: false
        default: true
        type: boolean
      deploy_vistria:
        description: "Deploy Vistria"
        required: false
        default: true
        type: boolean

# All jobs run concurrently by default
jobs:
  deploy-25m:
    if: ${{ inputs.deploy_25m == true }}
    uses: ./.github/workflows/deploy-25m.yaml

  deploy-alpine:
    if: ${{ inputs.deploy_alpine == true }}
    uses: ./.github/workflows/deploy-alpine.yaml

  deploy-evercore:
    if: ${{ inputs.deploy_evercore == true }}
    uses: ./.github/workflows/deploy-evercore.yaml

  deploy-hpc:
    if: ${{ inputs.deploy_hpc == true }}
    uses: ./.github/workflows/deploy-hpc.yaml

  deploy-oha:
    if: ${{ inputs.deploy_oha == true }}
    uses: ./.github/workflows/deploy-oha.yaml

  deploy-vistria:
    if: ${{ inputs.deploy_vistria == true }}
    uses: ./.github/workflows/deploy-vistria.yaml

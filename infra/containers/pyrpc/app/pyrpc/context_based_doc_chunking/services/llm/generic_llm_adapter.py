import logging
import os
import random
import time

import numpy as np
import openai
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from openai import OpenAI
from pydantic import BaseModel

from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from pyrpc.rag_response.new_llm import new_llm
from pyrpc.utils.get_env_var import get_env_var

logging.basicConfig(level=logging.INFO)

class GenericLLMAdapter:
    def __init__(self):
        openai_api_key = (
                get_env_var("OPENAI_API_KEY_JUMBO_CHUNK")
                or get_env_var("OPENAI_API_KEY")
        )

        gemini_api_key = (
                get_env_var("GEMINI_API_KEY")
                or get_env_var("GOOGLE_API_KEY")
        )

        # Manually force-set the env var for AWS environments
        os.environ["OPENAI_API_KEY"] = openai_api_key
        os.environ["GEMINI_API_KEY"] = gemini_api_key

        self.fallback_models = [
            ("google/gemini-2.5-pro-preview-05-06", gemini_api_key, 1048576),
            ("openai/gpt-4.1-nano", openai_api_key, 1047576),
            ("openai/gpt-4.1-mini", openai_api_key, 1047576),
        ]

        # TODO DORON 070625 removing this, no backoff, we'll simply drop results that are bad
        # Wrap the generator with class-instance decoration
        # self.generate = self.retry_with_exponential_backoff(self.generate)

        # Start with default model
        self.model = self.fallback_models[0][0]
        self.llm = new_llm(streaming=False, model_hyperparams=ModelHyperparameters(model=self._model_name_to_model_enum(self.model)))

        # Embedding client
        openai.api_key = openai_api_key
        self.client = OpenAI(
            # This is the default and can be omitted
            api_key=openai_api_key,
        )

    def retry_with_exponential_backoff(self,
                                       func=None,
                                       *,
                                       initial_delay=1,
                                       exponential_base=2,
                                       jitter=False,
                                       max_retries=1,
                                       errors=Exception
    ):
        def decorator(f):
            def wrapper(*args, **kwargs):
                while True:
                    delay = initial_delay
                    for attempt in range(max_retries):
                        try:
                            return func(*args, **kwargs)
                        except errors as e:
                            sleep_time = delay * (exponential_base**attempt)
                            if jitter:
                                sleep_time *= random.uniform(0.5, 1.5)
                            logging.warning(f"Retrying after error: {e}. Sleeping for {sleep_time:.2f}s")
                            time.sleep(sleep_time)

                    model_index = [f[0] for f in self.fallback_models].index(self.model)
                    logging.warning(f"Selecting another model {self.fallback_models[model_index][0]}")
                    if model_index < len(self.fallback_models) - 1:
                        model_index += 1
                        self.model = self.fallback_models[model_index][0]
                        self.llm = new_llm(streaming=False, model_hyperparams=ModelHyperparameters(model=self._model_name_to_model_enum(self.model)))
                        logging.warning(f"Retrying with model: {self.model}")
                    else:
                        # Revert to the original top model, and bail, we failed
                        self.model = self.fallback_models[0][0]
                        self.llm = new_llm(streaming=False, model_hyperparams=ModelHyperparameters(model=self._model_name_to_model_enum(self.model)))
                        logging.info(f"Reverting back to first model {self.model}, and bailing out")
                        break

                return None

            return wrapper

        return decorator(func) if func else decorator

    def _model_name_to_model_enum(self, model_name: str):
        specific_model = None
        if model_name.startswith("google/"):
            specific_model = model_name.split("/")[1]
        elif model_name.startswith("openai/"):
            specific_model = model_name.split("/")[1]
        elif model_name.startswith("anthropic/"):
            raise Exception("Anthropic not yet implemented in GenericLLMAdapter")

        return specific_model

    def get_context_limit(self):
        return [m for m in self.fallback_models if m[0] == self.model][0][2]

    def embed(self, text, model="text-embedding-ada-002"):
        try:
            # Request an embedding for the input text using the text-embedding-ada-002 model
            response = self.client.embeddings.create(input=text, model=model)

            # Extract the embedding vector from the response
            embedding_vector = np.array(response.data[0].embedding)

            return embedding_vector
        except Exception as e:
            logging.error("An error occurred:", e)
            return None

    def call_llm_api(self, prompt, message, template, response_model):
        _prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(prompt),
            HumanMessagePromptTemplate.from_template(template),
        ])

        _chain = _prompt.pipe(
            self.llm.with_structured_output(response_model)
        )

        result = _chain.invoke(message)

        return result


    def generate(self, prompt, message, template, response_model: BaseModel, max_output_tokens=32 * 1024, role="user"):
        response = self.call_llm_api(prompt, message, template, response_model)
        if response is None or response.is_empty():
            raise Exception("Invalid response: No text chunks were found")

        return response


if __name__ == "__main__":
    adapter = GenericLLMAdapter()

    class Dummy(BaseModel):
        capital: str

        def is_empty(self):
            return False

    r = adapter.generate(
        prompt="You are provided with a name of a country as input. You need to state its capital.",
        message={"input": 'France'},
        template="{input}",
        response_model=Dummy,
    )

    print(r)

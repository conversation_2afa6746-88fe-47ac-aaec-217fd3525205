from pyrpc.utils.base_url import get_base_url
from typing import List, Dict, Any
import requests
import logging
from sklearn.cluster import AgglomerativeClustering
from sklearn.cluster import DBSCAN
from sklearn.metrics.pairwise import cosine_similarity

from pydantic import BaseModel, Field
from typing import List
import numpy as np


class ClusterQuestionInput(BaseModel):
    question_id: str = Field(description="The question id")
    question_vector: List = Field(description="The question content")


def cluster_questions(question_inputs: List[ClusterQuestionInput]):
    try:
        embeddings = np.array([q.question_vector for q in question_inputs], dtype=object)
    
        # model = AgglomerativeClustering(
        #     n_clusters=None,
        #     distance_threshold=0.2,
        #     metric='cosine',
        #     linkage='complete'
        # )

        model = DBSCAN(
            metric='cosine',
            eps=0.15,
            min_samples=2
        )
        groups = model.fit_predict(embeddings)
        
        result = [{"question_id": q.question_id, "group": int(group)} for q, group in zip(question_inputs, groups)]
        return result
        
    except Exception as e:
        logging.error(f"Cluster questions error: {e}")
        return []
    
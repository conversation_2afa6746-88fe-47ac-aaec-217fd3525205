import re
from typing import List

from bs4 import BeautifulSoup


def html_to_text(html_content, preserve_links=False, preserve_formatting=True):
    """
    Convert HTML to clean, readable text using BeautifulSoup

    Args:
        html_content (str): HTML string to convert
        preserve_links (bool): Whether to show URLs for links
        preserve_formatting (bool): Whether to preserve basic formatting with spacing

    Returns:
        str: Clean text content
    """

    # Parse HTML with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove script and style elements completely
    for script in soup(["script", "style"]):
        script.decompose()

    # Handle links if preserving them
    if preserve_links:
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href and not href.startswith('#'):
                link.string = f"{link.get_text()} ({href})"

    # Get text content
    if preserve_formatting:
        text = soup.get_text(separator='\n', strip=True)
    else:
        text = soup.get_text(strip=True)

    # Clean up whitespace
    # Replace multiple newlines with double newline (paragraph breaks)
    text = re.sub(r'\n\s*\n', '\n\n', text)

    # Remove excessive whitespace within lines
    text = re.sub(r'[ \t]+', ' ', text)

    # Remove leading/trailing whitespace from each line
    lines = [line.strip() for line in text.split('\n')]
    text = '\n'.join(lines)

    # Remove empty lines at start and end
    text = text.strip()

    return text


# Specialized function for table-heavy HTML
def html_to_text_table_focused(html_content):
    """
    Convert HTML to text with special focus on preserving table structure
    Tables are converted to tab-separated values with proper alignment
    """
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove unwanted elements
    for element in soup(["script", "style", "meta", "link", "noscript"]):
        element.decompose()

    # Process tables with enhanced formatting
    for table in soup.find_all('table'):
        table_rows = []

        # Process each row
        for row in table.find_all('tr'):
            cells = []
            for cell in row.find_all(['td', 'th']):
                # Get cell text and clean it
                cell_text = cell.get_text(strip=True)
                # Replace internal newlines/whitespace with single spaces
                cell_text = re.sub(r'\s+', ' ', cell_text)
                # Handle empty cells
                if not cell_text:
                    cell_text = ""
                cells.append(cell_text)

            if cells:  # Only add non-empty rows
                table_rows.append(cells)

        # Convert to tab-separated format
        if table_rows:
            # Calculate column widths for better alignment (optional)
            max_widths = []
            for row in table_rows:
                for i, cell in enumerate(row):
                    if i >= len(max_widths):
                        max_widths.append(0)
                    max_widths[i] = max(max_widths[i], len(cell))

            # Format table
            formatted_rows = []
            for row in table_rows:
                # Pad cells to ensure consistent tab alignment
                padded_cells = []
                for i, cell in enumerate(row):
                    padded_cells.append(cell)
                formatted_rows.append('\t'.join(padded_cells))

            formatted_table = '\n'.join(formatted_rows)
            table.replace_with(f'\n\n{formatted_table}\n\n')

    # Handle other block elements
    block_elements = ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                      'li', 'ul', 'ol', 'blockquote', 'pre', 'hr']

    for tag in soup.find_all(block_elements):
        if tag.name == 'br':
            tag.replace_with('\n')
        elif tag.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            tag.insert_before('\n\n')
            tag.insert_after('\n')
        elif tag.name == 'hr':
            tag.replace_with('\n' + '─' * 50 + '\n')
        elif tag.name in ['ul', 'ol']:
            tag.insert_before('\n')
            tag.insert_after('\n')
        elif tag.name == 'li':
            tag.insert_before('• ')
            tag.insert_after('\n')
        else:
            tag.insert_after('\n')

    # Get final text
    text = soup.get_text()

    # Clean up whitespace while preserving tabs
    lines = []
    for line in text.split('\n'):
        # Don't strip tabs, but clean up other whitespace
        cleaned_line = re.sub(r'[ ]{2,}', ' ', line.strip())
        lines.append(cleaned_line)

    # Remove excessive empty lines
    clean_lines = []
    prev_empty = False
    for line in lines:
        if line == '':
            if not prev_empty:
                clean_lines.append(line)
            prev_empty = True
        else:
            clean_lines.append(line)
            prev_empty = False

    return '\n'.join(clean_lines).strip()


# Function to extract just tables from HTML
def extract_tables_to_tsv(html_content):
    """
    Extract only tables from HTML and return as tab-separated values
    Returns list of tables, each as a string with tab-separated rows
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    tables = []

    for table in soup.find_all('table'):
        table_rows = []

        for row in table.find_all('tr'):
            cells = []
            for cell in row.find_all(['td', 'th']):
                cell_text = cell.get_text(strip=True)
                cell_text = re.sub(r'\s+', ' ', cell_text)
                cells.append(cell_text if cell_text else "")

            if cells:
                table_rows.append('\t'.join(cells))

        if table_rows:
            tables.append('\n'.join(table_rows))

    return tables


def html_to_text_advanced(html_content):
    """
    Advanced HTML to text conversion with better formatting preservation
    Handles tables with proper row/column structure
    """
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove unwanted elements
    for element in soup(["script", "style", "meta", "link", "noscript"]):
        element.decompose()

    # Handle tables first - convert to tab-separated format
    for table in soup.find_all('table'):
        table_text = []

        for row in table.find_all('tr'):
            cells = []
            for cell in row.find_all(['td', 'th']):
                # Get cell text and clean it
                cell_text = cell.get_text(strip=True)
                # Replace internal newlines with spaces in cells
                cell_text = re.sub(r'\s+', ' ', cell_text)
                cells.append(cell_text)

            if cells:  # Only add non-empty rows
                table_text.append('\t'.join(cells))

        # Replace table with formatted text
        if table_text:
            formatted_table = '\n'.join(table_text)
            table.replace_with(f'\n{formatted_table}\n')

    # Add spacing for block elements
    block_elements = ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                      'li', 'ul', 'ol', 'blockquote', 'pre', 'hr']

    for tag in soup.find_all(block_elements):
        if tag.name == 'br':
            tag.replace_with('\n')
        elif tag.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            # Add extra spacing around headers
            tag.insert_before('\n\n')
            tag.insert_after('\n')
        elif tag.name == 'hr':
            tag.replace_with('\n' + '─' * 50 + '\n')
        elif tag.name in ['ul', 'ol']:
            tag.insert_before('\n')
            tag.insert_after('\n')
        elif tag.name == 'li':
            tag.insert_before('• ')
            tag.insert_after('\n')
        else:
            tag.insert_after('\n')

    # Get text and clean up
    text = soup.get_text()

    # Clean up excessive whitespace but preserve tabs
    text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # Max 2 newlines
    text = re.sub(r'[ ]+', ' ', text)  # Multiple spaces to single (but not tabs)

    # Clean up lines while preserving tabs
    lines = []
    for line in text.split('\n'):
        # Strip leading/trailing spaces but keep tabs
        line = line.strip()
        lines.append(line)

    # Remove excessive empty lines
    clean_lines = []
    prev_empty = False
    for line in lines:
        if line == '':
            if not prev_empty:
                clean_lines.append(line)
            prev_empty = True
        else:
            clean_lines.append(line)
            prev_empty = False

    return '\n'.join(clean_lines).strip()


# Function to process HTML file
def convert_html_file(file_path, output_path=None):
    """
    Convert HTML file to text file
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        text_content = html_to_text_advanced(html_content)

        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            print(f"Text saved to: {output_path}")
        else:
            return text_content

    except Exception as e:
        print(f"Error processing file: {e}")
        return None


# Function to process multiple HTML files
def batch_convert_html_files(html_files, output_dir="converted_text"):
    """
    Convert multiple HTML files to text files
    """
    import os

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for html_file in html_files:
        if html_file.endswith('.html') or html_file.endswith('.htm'):
            base_name = os.path.splitext(os.path.basename(html_file))[0]
            output_file = os.path.join(output_dir, f"{base_name}.txt")
            convert_html_file(html_file, output_file)


async def pack_strings(items: List[str], max_len: int, prefix_len: int, sep: str = " ") -> List[str]:
    """
    Concatenate consecutive strings from `items` so that
    each output string's length is ≤ `max_len`.

    Parameters
    ----------
    items       : list of input strings
    max_len     : hard ceiling on the length of each packed string
    prefix_len  : the max prefix over all chunks, we'd like to pack
    sep         : separator inserted between items (default: single space)

    Returns
    -------
    List[str] : the packed strings in their original order
    """
    out, line = [], ""
    total = 0
    for s in items:
        # If we’d overflow the limit, flush the current line and start a new one
        if line and len(line) + len(sep) + len(s) > max_len:
            # If we crossed the requested prefix, bail with the current out
            if total + len(line) > prefix_len:
                # If no out lines, append the current
                if len(out) == 0:
                    out.append(line)
                break
            out.append(line)
            line = s
        else:
            line = s if not line else f"{line}{sep}{s}"
            total += len(line)

    total += len(line)
    if line and total < prefix_len:
        out.append(line)

    return out

from collections import defaultdict
from typing import Dict, List


# ── 1. ALL substrings ──────────────────────────────────────────────────────────
def all_substrings(s: str) -> List[str]:
    """Return every distinct substring of *s* (order = first occurrence)."""
    seen = set()
    ordered = []
    for i in range(len(s)):
        for j in range(i + 1, len(s) + 1):
            sub = s[i:j]
            if sub not in seen:
                seen.add(sub)
                ordered.append(sub)
    return ordered


# ── 2–3. Suffix-array core (shared by the next two helpers) ────────────────────
def _suffix_array(s: str) -> List[int]:
    """Naïve O(n² log n) suffix array (fine ≤≈50 kB)."""
    return sorted(range(len(s)), key=lambda i: s[i:])

def _lcp_array(s: str, sa: List[int]) -> List[int]:
    """LCP between adjacent suffixes in *sa*."""
    n, lcp = len(s), [0] * (len(sa) - 1)
    for k in range(len(sa) - 1):
        i, j = sa[k], sa[k + 1]
        m = 0
        while i + m < n and j + m < n and s[i + m] == s[j + m]:
            m += 1
        lcp[k] = m
    return lcp


# ── 2. Repeated substrings + counts ────────────────────────────────────────────
def repeated_substrings(s: str, min_freq: int = 2) -> Dict[str, int]:
    """
    Return {substring: frequency} for every substring occurring ≥ *min_freq* times.
    """
    if len(s) < 2:
        return {}

    sa = _suffix_array(s)
    lcp = _lcp_array(s, sa)

    # Each LCP entry gives us one repeated substring length.
    repeats: Dict[str, int] = defaultdict(int)
    for k, l in enumerate(lcp):
        if l == 0:
            continue
        i, j = sa[k], sa[k + 1]
        sub = s[i : i + l]          # the shared prefix
        repeats[sub] += 1           # we’ll refine this count below

    # Fix frequencies: scan every suffix and tally exact matches
    for sub in list(repeats):
        count = 0
        pos = s.find(sub)
        while pos != -1:
            count += 1
            pos = s.find(sub, pos + 1)
        if count >= min_freq:
            repeats[sub] = count
        else:                       # drop false positives (rare for >2)
            del repeats[sub]

    return dict(repeats)


# ── 3. ONLY the longest repeats (ties kept, original order) ────────────────────
def longest_repeated_substrings(s: str) -> List[str]:
    """
    Return all substrings of maximal length that appear at least twice.
    """
    sa = _suffix_array(s)
    lcp = _lcp_array(s, sa)
    if not lcp or max(lcp) == 0:
        return []

    max_len = max(lcp)
    # Collect distinct candidates whose LCP == max_len
    candidates = {s[sa[k] : sa[k] + max_len] for k, v in enumerate(lcp) if v == max_len}

    # Preserve order of first appearance for nice, deterministic output
    ordered = sorted(candidates, key=s.find)
    return ordered

def count_substring(s: str, sub: str, *, overlapping: bool = False) -> int:
    """
    Count how many times *sub* appears in *s*.

    Parameters
    ----------
    s : str
        The text to search in.
    sub : str
        The substring to count.
    overlapping : bool, default False
        • False  →  only non-overlapping matches are counted
                    (like str.count())
        • True   →  overlapping matches are allowed
                    (e.g. 'aaa' contains 'aa' twice: positions 0-1 and 1-2)

    Returns
    -------
    int
        Number of occurrences.
    """
    if sub == "":
        raise ValueError("substring must be non-empty")

    if not overlapping:
        return s.count(sub)

    # Overlapping scan
    count = start = 0
    while True:
        start = s.find(sub, start)
        if start == -1:
            break
        count += 1
        start += 1           # step forward just one char → allows overlaps
    return count

def replace_after_first(s: str, sub: str, repl: str = " ") -> str:
    """
    Keep the **first** appearance of *sub* in *s* untouched and replace every
    subsequent non-overlapping one with *repl*.

    Examples
    --------
    >>> replace_after_first("banana", "an")
    'ban  a'
    >>> replace_after_first("abracadabra", "abra", "_")
    'abra_c_d_'
    """
    if sub == "":
        raise ValueError("`sub` must be non-empty")

    first = s.find(sub)
    if first == -1:                   # nothing to replace
        return s

    head = s[: first + len(sub)]      # up to and incl. the first match
    tail = s[first + len(sub) :]      # everything after it
    return head + tail.replace(sub, repl)

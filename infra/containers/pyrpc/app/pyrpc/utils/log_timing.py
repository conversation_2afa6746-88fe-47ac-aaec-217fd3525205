import logging
import time

def log_timing(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()  # Record start time
        result = await func(*args, **kwargs)  # Call the decorated function
        end_time = time.time()  # Record end time
        duration = end_time - start_time  # Calculate duration
        logging.info(f"{func.__name__} took {duration:.4f} seconds to run.")
        return result
    return wrapper

def log_timing_sync(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()  # Record start time
        result = func(*args, **kwargs)  # Call the decorated function
        end_time = time.time()  # Record end time
        duration = end_time - start_time  # Calculate duration
        logging.info(f"{func.__name__} took {duration:.4f} seconds to run.")
        return result
    return wrapper
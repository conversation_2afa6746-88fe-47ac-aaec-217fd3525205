from contextlib import contextmanager, nullcontext
from typing import  Any, Optional, Callable, TypeVar, Literal, cast
from functools import wraps

# Make langfuse optional
try:
    from langfuse.langchain import Callback<PERSON>andler
    from langfuse import Langfuse as LangfuseClient
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    LangfuseClient = type('Langfuse', (), {})  # Dummy class for type hints
    import warnings
    warnings.warn("langfuse not installed. Tracing will be disabled.", ImportWarning)

from pyrpc.utils.aws_secret import get_secret_value
import logging
import contextvars
import inspect
import types
from pydantic import BaseModel
import json
import secrets;
import uuid

from pyrpc.utils.get_env_var import get_env_var;

# Context variable to hold the trace ID for the current request
_trace_id_var = contextvars.ContextVar('trace_id', default=None)
_parent_span_id_var = contextvars.ContextVar('parent_span_id', default=None)
_session_id_var = contextvars.ContextVar('session_id', default=None)

# Type variable for generic function type
F = TypeVar('F', bound=Callable[..., Any])

# Global instance to be used across the application
class Tracing:
    client: Optional[LangfuseClient] = None
    def __init__(self):
        self.client = None
        
        if not LANGCHAIN_AVAILABLE:
            logging.warning("langfuse not installed. Tracing is disabled.")
            return
            
        try:
            from langfuse import Langfuse
            self.langfuse_public_key = get_env_var("LANGFUSE_PUBLIC_KEY", "")
            self.langfuse_secret_key = get_env_var("LANGFUSE_SECRET_KEY", "")
            self.langfuse_host = get_env_var("LANGFUSE_HOST", "")

            if not all([self.langfuse_public_key, self.langfuse_secret_key, self.langfuse_host]):
                logging.warning("Langfuse keys not found. Tracing is disabled.")
            else:
                self.client = Langfuse(
                    public_key=self.langfuse_public_key,
                    secret_key=self.langfuse_secret_key,
                    host=self.langfuse_host
                )
        except Exception as e:
            logging.warning(f"Failed to initialize Langfuse client: {str(e)}. Tracing is disabled.")

    @contextmanager
    def start_trace(self, name: str, session_id: Optional[str] = None, user_id: Optional[str] = None):
        """
        A context manager to start a trace in Python.
        It creates a new root span and sets its trace_id in the context for subsequent steps.
        """
        if not self.client:
            yield
            return

        with self.client.start_as_current_span(name=name) as span:
            span.update_trace(user_id=user_id, session_id=session_id)
            trace_id = span.get_trace_id()
            parent_span_id = None
            tokenTraceId = _trace_id_var.set(trace_id)
            tokenParentSpanId = _parent_span_id_var.set(parent_span_id)
            logging.info(f"Trace started: {trace_id}:{parent_span_id} for session: {session_id}")
            try:
                yield
            finally:
                _trace_id_var.reset(tokenTraceId)
                _parent_span_id_var.reset(tokenParentSpanId)
                logging.info(f"Trace finished: {trace_id}:{parent_span_id}")

    @contextmanager
    def continue_trace(self, trace_id: Optional[str], session_id: Optional[str] = None, parent_span_id: Optional[str] = None):
        """
        A context manager to continue a trace from an external source (e.g., NodeJS).
        It only sets the trace_id in the context for subsequent steps.
        """
        if not self.client or not trace_id:
            yield
            return

        tokenSessionId = _session_id_var.set(session_id)
        tokenTraceId = _trace_id_var.set(trace_id)
        tokenParentSpanId = _parent_span_id_var.set(parent_span_id)
        logging.info(f"Continuing trace: {trace_id}:{parent_span_id} for session: {session_id}")
        try:
            yield
        finally:
            _session_id_var.reset(tokenSessionId)
            _trace_id_var.reset(tokenTraceId)
            _parent_span_id_var.reset(tokenParentSpanId)
            logging.info(f"Finished step on trace: {session_id}:{trace_id}:{parent_span_id}")

    def _prepare_input_dict(self, func: F, *args, **kwargs) -> dict:
        """Helper to capture and serialize function inputs."""
        try:
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            input_dict = {}
            for k, v in bound_args.arguments.items():
                if k in ['self', 'cls']:
                    continue
                if isinstance(v, BaseModel):
                    input_dict[k] = v.model_dump()
                else:
                    # Attempt to handle other types, fallback for unserializable ones
                    try:
                        json.dumps(v)
                        input_dict[k] = v
                    except (TypeError, OverflowError):
                        input_dict[k] = f"Unserializable value of type {type(v).__name__}"

            return input_dict
        except Exception as e:
            logging.warning(f"Failed to capture function inputs for {func.__name__}: {e}")
            return {"info": "Could not capture inputs"}

    def observe(
        self,
        func: Optional[Callable] = None,
        *,
        name: Optional[str] = None,
        as_type: Optional[Literal["generation"]] = None,
    ):
        """
        A decorator to mark a function as a span within the current trace.
        It reads the trace_id from the context, creates a new span,
        and captures inputs, outputs, and errors.
        """
        def decorator(func: F) -> F:
            if inspect.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    if not self.client: 
                        return await func(*args, **kwargs)
                    
                    input_dict = self._prepare_input_dict(func, *args, **kwargs)
                    trace_id = _trace_id_var.get()
                    parent_span_id = _parent_span_id_var.get()
                    
                    final_name = name or func.__name__
                    span_args = {"name": final_name, "input": input_dict}
                    if trace_id: 
                        span_args["trace_context"] = {"trace_id": trace_id, "parent_span_id": parent_span_id}
                    else: 
                        logging.warning(f"No active trace found for '{final_name}'. Starting a new trace.")

                    if as_type == "generation":
                        async with self.start_as_current_generation(**span_args) as span:
                            try:
                                result = await func(*args, **kwargs)
                                if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                    output = result.model_dump() if hasattr(result, 'model_dump') else result
                                    span.update(output=output)
                                return result
                            except Exception as e:
                                span.update(level="ERROR", status_message=str(e))
                                raise
                    else:
                        async with self.start_as_current_span(**span_args) as span:
                            try:
                                result = await func(*args, **kwargs)
                                if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                    output = result.model_dump() if hasattr(result, 'model_dump') else result
                                    span.update(output=output)
                                return result
                            except Exception as e:
                                span.update(level="ERROR", status_message=str(e))
                                raise
                return cast(F, async_wrapper)
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    if not self.client: 
                        return func(*args, **kwargs)

                    input_dict = self._prepare_input_dict(func, *args, **kwargs)
                    trace_id = _trace_id_var.get()
                    parent_span_id = _parent_span_id_var.get()

                    final_name = name or func.__name__
                    span_args = {"name": final_name, "input": input_dict}
                    if trace_id: 
                        span_args["trace_context"] = {"trace_id": trace_id, "parent_span_id": parent_span_id}
                    else: 
                        logging.warning(f"No active trace found for '{final_name}'. Starting a new trace.")

                    if as_type == "generation":
                        with self.start_as_current_generation(**span_args) as span:
                            try:
                                result = func(*args, **kwargs)
                                if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                    output = result.model_dump() if hasattr(result, 'model_dump') else result
                                    span.update(output=output)
                                return result
                            except Exception as e:
                                span.update(level="ERROR", status_message=str(e))
                                raise
                    else:
                        with self.start_as_current_span(**span_args) as span:
                            try:
                                result = func(*args, **kwargs)
                                if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                    output = result.model_dump() if hasattr(result, 'model_dump') else result
                                    span.update(output=output)
                                return result
                            except Exception as e:
                                span.update(level="ERROR", status_message=str(e))
                                raise
                return cast(F, sync_wrapper)

        if func is not None:
            return decorator(func)
        return decorator

    def get_handler(self, session_id: Optional[str] = None) -> Optional[CallbackHandler]:
        """
        Gets a Langchain CallbackHandler that is automatically linked to the current trace/span.
        """
        if not self.client:
            return None
        
        # CallbackHandler with no arguments should pick up the active span from the OTEL context.
        return CallbackHandler()

    def start_as_current_span(self, name: str, **kwargs):
        if not self.client: return nullcontext()
        return self.client.start_as_current_span(name=name, **kwargs)
    
    def start_as_current_generation(self, name: str, **kwargs):
        if not self.client: return nullcontext()
        return self.client.start_as_current_generation(name=name, **kwargs)

    def gen_trace_id(self):
        return secrets.token_hex(16)

    def gen_session_id(self):
        return uuid.uuid4().hex

    def gen_span_id(self):
        return secrets.token_hex(8)

tracing = Tracing()

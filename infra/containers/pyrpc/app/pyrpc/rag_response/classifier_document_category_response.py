from typing import List, Type
from jinja2 import Template
from pydantic import BaseModel, Field, create_model
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from google import genai
from enum import Enum
from pyrpc.utils.tracing import tracing
from openai import OpenAI
import json

import logging

from pyrpc.utils.get_env_var import get_env_var
logging.basicConfig(level=logging.INFO)


class DocumentChunkInput(BaseModel):
    """A single document chunk input"""
    chunk: str = Field(description="The document chunk as provided in the input list.")

class DocumentInput(BaseModel):
    """A single document input"""
    chunks: List[DocumentChunkInput] = Field(description="The document chunks as provided in the input list.")

class CategoryInput(BaseModel):
    """A single category input"""
    id: str = Field(description="The category ID as provided in the input list.")
    name: str = Field(description="The category name as provided in the input list.")
    description: str = Field(description="The category description as provided in the input list.")

def create_enum(name: str, values: list[str]) -> Type[Enum]:
    # Create enum keys safely
    return Enum(name, {v.upper().replace(" ", "_"): v for v in values})


def build_model_with_enum(category_values: list[str]) -> Type[BaseModel]:
    CategoryEnum = create_enum("CategoryEnum", category_values)

    # Inner response model
    CategoryAnalyzeResponse = create_model(
        'CategoryAnalyzeResponse',
        type=(CategoryEnum, Field(description="One of the dynamic categories. If there are no match, do not assign any categories.")),
        name=(str, Field(description="The category name as provided in the input list.")),  
        reason=(str, Field(description="A single paragraph explanation of the reason why the document was assigned to this category. Must be concise, short, and logical.")),
    )

    # Outer model
    DocumentCategoryAnalyzeResponse = create_model(
        'DocumentCategoryAnalyzeResponse',
        categories=(List[CategoryAnalyzeResponse], Field(description="List of category responses"))
    )

    return DocumentCategoryAnalyzeResponse

tool_prompt = """ 
You are a helpful assistant, specializing in classifying financial documents.

You will be given a document, in JSON format.
The document contains a document ID, a document content, and a document metadata.

Your task is to first reconstruct the document, and then assign up to as many categories as you can to the document, based on the category names and description, by analyzing the document content.
Assign the categories whos summaries best describe the document. If the document does not fit into any of the categories, then do not assign any categories.

Output Format: Return your results as valid JSON.

Formatting:
- Do not include any extra text or explanations outside of the JSON block.
Validation:
- Do not create any new categories. Only use the categories provided.
- Do not assign a document to a category that does not exist in the list of categories.
- The category name that you assign must match the associated categoryId, as passed in the input.
- After assigning a categoryId, look it up in the input list of categories to get the associated category name. Then, compare it to your assigned category name. If they do not match, repeat the process until they match.

Here is the document name:
{{ name }}

Here is the document chunks:

{{ chunks }}

"""

class DocumentInput(BaseModel):
    """A single document input"""
    id: str = Field(description="The document ID as provided in the input list.")
    chunks: List[str] = Field(description="The document chunks as provided in the input list.")
    name: str = Field(description="The document name as provided in the input list.")

class CategoryInput(BaseModel):
    """A single category input"""
    id: str = Field(description="The category ID as provided in the input list.")
    name: str = Field(description="The category name as provided in the input list.")
    description: str = Field(description="The category description as provided in the input list.")

class CategoryResponse(BaseModel):
    """A single response for category assignment"""
    id: str = Field(description="The category ID as provided in the input list.")
    name: str = Field(description="The category name as provided in the input list.")
    reason: str = Field(description="A single paragraph explanation of the reason why the document was assigned to this category. Must be concise, short, and logical.")

class ClassifyDocumentCategoryResponse(BaseModel):
    categories: List[CategoryResponse] = Field(description="A list of responses, one for each question.")

def classifier_categories_runpod(
    name: str,
    chunks: List[str],
    categories: List[CategoryInput],
    model_hyperparameters: ModelHyperparameters,
    secret: dict = None,
    **kwargs
) -> ClassifyDocumentCategoryResponse:
    logging.info('Classifier document category response with RunPod...')

    try:
        template = Template(tool_prompt)
        prompt_content = template.render(
            name=name,
            chunks="\n\n".join(chunks),
        )

        # Call RunPod endpoint using OpenAI compatibility
        runpod_api_key = get_env_var("RUNPOD_API_KEY")
        endpoint_id = get_env_var("RUNPOD_ENDPOINT_ID")
        
        # Format categories for the prompt
        structured_output_schema = build_model_with_enum([c['name'] for c in categories]).model_json_schema()
        
        # Use OpenAI client with RunPod endpoint
        client = OpenAI(
            api_key=runpod_api_key,
            base_url=f"https://api.runpod.ai/v2/{endpoint_id}/openai/v1"
        )
        
        runpod_model = get_env_var("RUNPOD_MODEL")
        
        response = client.chat.completions.create(
            model=runpod_model,
            messages=[
                {
                    "role": "user",
                    "content": prompt_content
                }
            ],
            max_tokens=2048,
            temperature=model_hyperparameters.temperature,
            top_p=model_hyperparameters.top_p,
            extra_body={"guided_json": structured_output_schema},
        )
        
        generated_text = response.choices[0].message.content
        print(f"Generated text: {generated_text}")
        
        # Parse the JSON response
        try:
            json_start = generated_text.find('{')
            json_end = generated_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_text = generated_text[json_start:json_end]
                response_data = json.loads(json_text)
            else:
                response_data = json.loads(generated_text)
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON response: {generated_text}")
            raise Exception(f"Invalid JSON response from RunPod: {e}")

        print(f"RunPod response received successfully")
        
        responses = []
        available_categories = {str(c["name"]).lower(): c for c in categories}

        for item in response_data.get('categories', []):
            if not item:
                continue

            categoryName = item.get('name', '')
            if not categoryName:
                continue

            category_input = available_categories.get(str(categoryName).lower())
            if not category_input:
                logging.error(f"Category {categoryName} not found in input list")
                continue

            try:
                responses.append(CategoryResponse(
                    id=str(category_input.get('id', '')),
                    name=category_input.get('name', ''),
                    reason=item.get('reason', ''),
                ))
                print(f"Found category: {category_input.get('name', '')}")
            except Exception as e:
                print(f"classifier_document_category_runpod: Error creating CategoryResponse: {e}")
                logging.error(f"classifier_document_category_runpod: Error creating CategoryResponse: {e}")
                raise Exception(f"Error creating CategoryResponse: {e}")
                
        return ClassifyDocumentCategoryResponse(categories=responses)

    except Exception as e:
        print(f"classifier_document_category_runpod: Error generating answer: {e}")
        logging.error(f"classifier_document_category_runpod: Error generating answer: {e}")
        raise e

def classifier_categories(
    name: str,
    chunks: List[str],
    categories: List[CategoryInput],
    model_hyperparameters: ModelHyperparameters,
    secret: dict = None,
    **kwargs
) :
    logging.info('Classifier document category response...')

    try:
        template = Template(tool_prompt)
        prompt_content = template.render(
            name=name,
            chunks="\n\n".join(chunks),
        )

        structured_output_schema = build_model_with_enum([c['name'] for c in categories]).model_json_schema()

        client = genai.Client(api_key=get_env_var("GEMINI_API_KEY", secret).split(",")[0])
        model = get_env_var("GEMINI_MODEL", secret)

        trace_context =kwargs.get("trace_context", {})
        # Create a generation span for the LLM call
        with tracing.start_as_current_span(
            name="gemini_classify_categories",
            trace_context=trace_context,
            input={
                "model": model,
                "prompt": prompt_content,
                "temperature": model_hyperparameters.temperature,
                "top_p": model_hyperparameters.top_p,
                "document_name": name,
                "categories_count": len(categories),
                "chunks_count": len(chunks)
            },
        ) as generation_span:
            try:
                response = client.models.generate_content(
                    model=model,
                    contents=prompt_content,
                    config={
                        "response_mime_type": "application/json",
                        "response_schema": structured_output_schema,
                        "temperature": model_hyperparameters.temperature,
                        "top_p": model_hyperparameters.top_p,
                    },
                )

                # Update generation span with response metadata
                usage_metadata = getattr(response, 'usage_metadata', None)
                if generation_span and usage_metadata:
                    generation_span.update(
                        model=model,
                        output=response.parsed,
                        usage={
                            "input": usage_metadata.prompt_token_count,
                            "output": usage_metadata.candidates_token_count,
                            "total": usage_metadata.total_token_count,
                            "unit": "TOKENS"
                        }
                    )

                    logging.info(
                        f"Gemini API usage: {usage_metadata.prompt_token_count} input + {usage_metadata.candidates_token_count} output = {usage_metadata.total_token_count} total tokens")
                elif generation_span:
                    # Fallback if no usage metadata available
                    generation_span.update(
                        model=model,
                        output=response.parsed,
                        usage={}
                    )
            except Exception as llm_error:
                # Update generation span with error information
                if generation_span:
                    generation_span.update(
                        level="ERROR",
                        status_message=str(llm_error)
                    )
                raise llm_error

        print(f"Classifier Usage token count:")
        print(f"- Candidates token count: {response.usage_metadata.candidates_token_count}")
        print(f"- Prompt token count: {response.usage_metadata.prompt_token_count}")
        print(f"- Total token count: {response.usage_metadata.total_token_count}")

        response_list = response.parsed
        responses = []

        available_categories = {str(c["name"]).lower(): c for c in categories}

        for item in response_list.get('categories', []):
            if not item:
                continue

            categoryName = item.get('name', '')
            if not categoryName:
                continue

            category_input = available_categories.get(str(categoryName).lower())
            if not category_input:
                logging.error(f"Category {categoryName} not found in input list")
                continue

            try:
                responses.append(CategoryResponse(
                    id=str(category_input.get('id', '')),
                    name=category_input.get('name', ''),
                    reason=item.get('reason', ''),
                ))
                print(f"Found category: {category_input.get('name', '')}")
            except Exception as e:
                print(f"classifier_document_category: Error creating CategoryResponse: {e}")
                logging.error(f"classifier_document_category: Error creating CategoryResponse: {e}")
                raise Exception(f"Error creating CategoryResponse: {e}")
                
        return ClassifyDocumentCategoryResponse(categories=responses)

    except Exception as e:
        print(f"classifier_document_category: Error generating answer: {e}")
        logging.error(f"classifier_document_category: Error generating answer: {e}")
        raise e


def classifier_document_category(
    document: DocumentInput,
    categories: List[CategoryInput],
    model_hyperparameters: ModelHyperparameters,
    secret: dict = None,
    **kwargs
) -> ClassifyDocumentCategoryResponse:
    # Check if RunPod is enabled
    runpod_enabled = get_env_var("RUNPOD_ENABLE", secret, "false").lower() == "true"
    
    if runpod_enabled:
        return classifier_categories_runpod(document.name, document.chunks, categories, model_hyperparameters, secret, **kwargs)
    else:
        return classifier_categories(document.name, document.chunks, categories, model_hyperparameters, secret, **kwargs)

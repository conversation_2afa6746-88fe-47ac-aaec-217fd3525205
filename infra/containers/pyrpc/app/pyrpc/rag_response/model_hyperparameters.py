from pydantic import BaseModel, Field

from pyrpc.rag_response.chat_model import ChatModel
from pyrpc.rag_response.response_style import ResponseStyle


class ModelHyperparameters(BaseModel):
    model: str = Field(default=ChatModel.CLAUDE_40_LATEST)
    responseStyle: str = Field(default=ResponseStyle.NORMAL)
    temperature: float = Field(default=0.0)
    top_p: float = Field(default=1e-9, ge=1e-9, le=1.0)  # Range: 1e-9 to 1
    thinking_mode: bool = Field(default=False)
    agentic_mode_langgraph: bool = Field(default=False)
    citation_verification_mode: bool = Field(default=False)
    answer_reflexion_mode: bool = Field(default=False)
from enum import Enum
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from pyrpc.rag_response.chat_model import ChatModel
from pyrpc.rag_response.response_style import ResponseStyle
import os

from pydantic import BaseModel, Field

from pyrpc.utils.aws_secret import get_secret_value
from pyrpc.utils.get_env_var import get_env_var
from pyrpc.utils.round_robin import round_robin_api_keys








def new_llm(model_hyperparams: ModelHyperparameters = ModelHyperparameters(), streaming: bool=False, secret: dict = None):
    if model_hyperparams.model == ChatModel.GPT_4O_MINI or model_hyperparams.model == ChatModel.GPT_4O or model_hyperparams.model == ChatModel.GPT_41_NANO:
        return new_openai_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p,
            secret=secret
        )

    if model_hyperparams.model == ChatModel.GPT_O3_MINI:
        return new_openai_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            top_p=model_hyperparams.top_p,
            secret=secret
        )
    if model_hyperparams.model == ChatModel.CLAUDE_40_LATEST:
        return new_anthropic_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p,
            secret=secret
        )
    if model_hyperparams.model == ChatModel.GEMINI_25_FLASH:
        return new_gemini_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p,
            secret=secret
        )
        
    if model_hyperparams.model == ChatModel.GEMINI_25_PRO:
        return new_gemini_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p,
            secret=secret
        )
    raise ValueError(f"Unsupported model: {model_hyperparams.model}")

def new_openai_llm(model="gpt-4.1-nano", streaming: bool=False, temperature: float=0.7, top_p: float=1e-9, secret: dict = None):
    return ChatOpenAI(
        model=model,
        api_key=get_env_var("OPENAI_API_KEY", secret),
        # temperature=temperature,
        # top_p=top_p,
        streaming=streaming,
    )

def new_anthropic_llm(model, streaming: bool, temperature: float=0.7, top_p: float=1e-9, secret: dict = None):
    return ChatAnthropic(
        model=model,
        api_key=get_env_var("ANTHROPIC_API_KEY", secret),
        temperature=temperature,
        top_p=top_p,
        streaming=streaming,
        model_kwargs={
            "extra_headers": {
                "anthropic-beta": "output-128k-2025-02-19"
            }
        },
        max_tokens_to_sample=32000
    )

def new_gemini_llm(model, streaming: bool, temperature: float=0.7, top_p: float=1e-9, secret: dict = None):
    get_key = round_robin_api_keys(get_env_var("GEMINI_API_KEY", secret))
    return ChatGoogleGenerativeAI(
        model=model,
        api_key=get_key(),
        temperature=temperature,
        top_p=top_p,
        streaming=streaming,

    )

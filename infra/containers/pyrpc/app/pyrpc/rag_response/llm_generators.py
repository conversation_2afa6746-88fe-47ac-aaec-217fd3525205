import json
import logging

import json_repair
import requests
from google import genai

from pyrpc.rag_response.chat_model import ChatModel
from pyrpc.utils.get_env_var import get_env_var


def anthropic_generate_responses(prompt_content, model_hyperparams, structured_output_schema, secret) :
    # Prepare the request payload for Anthropic API
    logging.info(f"Preparing payload...")

    payload = {
        "model": model_hyperparams.model_name if hasattr(model_hyperparams, 'model_name') else ChatModel.CLAUDE_40_LATEST,
        "max_tokens": model_hyperparams.max_tokens if hasattr(model_hyperparams, 'max_tokens') else 64000,
        "temperature": model_hyperparams.temperature if hasattr(model_hyperparams, 'temperature') else 0.0,
        "messages": [
            {
                "role": "user",
                "content": prompt_content
            }
        ],
        # "system": "You are a helpful assistant that generates structured JSON responses for DDQ questions.",

        "tools": [
            {
                "name": "anthropic_responses",
                "description": "Generate structured responses for DDQ questions using the provided schema.",
                "input_schema": structured_output_schema
            }
        ],
        "tool_choice": {"type": "tool", "name": "anthropic_responses"}
    }

    logging.info(f"payload: {payload}")

    # Get API key from secret
    api_key = get_env_var("ANTHROPIC_API_KEY", secret)
    if not api_key:
        raise ValueError("Anthropic API key not found in secret")

    # Make the API call
    headers = {
        "Content-Type": "application/json",
        "x-api-key": api_key,
        "anthropic-beta": "output-128k-2025-02-19",
        "max_tokens": "32000",
        "anthropic-version": "2023-06-01"
    }

    logging.info(f"Making API call...")
    response = requests.post(
        "https://api.anthropic.com/v1/messages",
        headers=headers,
        json=payload,
        timeout=300  # 5 minute timeout
    )

    if response.status_code != 200:
        raise Exception(f"Anthropic API error: {response.status_code} - {response.text}")

    # Parse the response
    response_data = json_repair.loads(json.dumps(response.json()))
    logging.info(f"Response Data: {response_data}")

    # Extract structured output from the response
    if 'content' not in response_data or len(response_data['content']) == 0:
        raise Exception("No content found in Anthropic API response")

    content = response_data['content'][0]
    logging.info(f"Content type: {content.get('type')}")

    if content.get('type') != 'tool_use':
        raise Exception(f"Expected tool_use content type, got: {content.get('type')}")

    # Extract structured output from tool_use
    structured_output = json_repair.loads(json.dumps(content['input']).replace("\n", ""))
    logging.info(f"Structured output: {structured_output}")

    try:
        responses = json_repair.loads(json.dumps(structured_output.get('responses', [])).replace("\n", ""))
        # Need to figure out why responses_data sometime contains newlines and sometimes doesn't. Newlines break parsing down the line.
        logging.info(f"Responses: {responses} {type(responses)}")
        if isinstance(responses, list):
            responses = responses
        elif isinstance(responses, str):
            responses = json_repair.loads(responses)
        else:
            raise Exception(f"Unexpected responses data type: {type(responses)}")
    except Exception as e:
        logging.info(f"generate_anthropic_response: Error parsing structured output: {e}")
        logging.error(f"generate_anthropic_response: Error parsing structured output: {e}")
        raise Exception(f"Error parsing structured output: {e}")

    return responses


def gemini_generate_responses(prompt_content, model_hyperparams, structured_output_schema, secret) :
    client = genai.Client(api_key=get_env_var("GEMINI_API_KEY", secret))
    model = get_env_var("GEMINI_MODEL", secret)

    response = client.models.generate_content(
        model=model,
        contents=prompt_content,
        config={
            "response_mime_type": "application/json",
            "response_schema": structured_output_schema,
        },
    )

    logging.info(f"Response: {response.parsed}")
    responses = response.parsed

    return responses

import boto3
import logging
from pyrpc.rag_response.classifier_document_category_response import DocumentInput
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from pyrpc.utils.get_env_var import get_env_var

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

secret = None

def lambda_handler(event, context):
    print(f'\nEvent:\n{event}')
    print(f'\nContext:\n{context}')
    try:
        global secret

        try:
            # Validate secret_name
            if not event.get("secret"):
                raise ValueError("Secret must be provided.")
            
            # Retrieve the secret by name
            client = boto3.client("secretsmanager")
            wrapper = GetSecretWrapper(client)
            secret = event.get("secret")
            
        except Exception as e:
            logger.error(f"Error retrieving secret: {e}")
            raise

        try:
            mode = get_env_var("CLASSIFIER_MODE", None, "default")
        except Exception as e:
            mode = "default"

        try:
            document = DocumentInput(**event.get("document", {}))
            categories = event.get("categories", [])
            model_params = event.get("modelParameters", {})
            model_hyperparams = ModelHyperparameters(**model_params)

            if mode == "boolean":
                from pyrpc.rag_response.classifier_document_category_response_with_boolean import classifier_document_category
            elif mode == "binary":
                from pyrpc.rag_response.classifier_document_category_response_with_binary import classifier_document_category
            else:
                from pyrpc.rag_response.classifier_document_category_response import classifier_document_category
            result = classifier_document_category(document, categories, model_hyperparams, secret)

            return {
                "statusCode": 200,
                "body": result.model_dump()
            }
        except Exception as e:
            logger.error(f"classifierCategory: {str(e)}.")
            return {
                "statusCode": 500,
                "body": "classifierCategory failed: " + str(e)
            }
    
    except Exception as e:
        logger.error(f"classifierCategory: {str(e)}.")
        return {
            "statusCode": 500,
            "body": "classifierCategory failed: " + str(e)
        }

class GetSecretWrapper:
    def __init__(self, secretsmanager_client):
        self.client = secretsmanager_client


    def get_secret(self, secret_name):
        """
        Retrieve individual secrets from AWS Secrets Manager using the get_secret_value API.

        :param secret_name: The name of the secret fetched.
        :type secret_name: str
        """
        try:
            get_secret_value_response = self.client.get_secret_value(
                SecretId=secret_name
            )
            logging.info("Secret retrieved successfully.")
            return get_secret_value_response["SecretString"]
        except self.client.exceptions.ResourceNotFoundException:
            msg = f"The requested secret {secret_name} was not found."
            logger.info(msg)
            return msg
        except Exception as e:
            logger.error(f"generateChunks: GetSecretWrapper: {str(e)}.")
            raise
import { DocumentChunkType } from "@prisma/client";
import { APIGatewayProxyResult, Context } from "aws-lambda";
import {
  deleteObjectFromS3,
  getChunksFromS3,
  initWithSecrets,
} from "../../../src/lib/integrations/aws/utils";
import type { Document } from "langchain/document";
import { insertEmbeddedIntoDb } from "~/lib/vectorizer.insert-embedded";
import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { env } from "~/env";
import { ClassifierLambdaEvent } from "../classifier";

export type StoreChunksLambdaEvent = {
  documentId: string;
  secretId: string;
  chunkPath: string;
};

export const handler = async (
  event: StoreChunksLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  console.log("EVENT", JSON.stringify(event, null, 2));
  console.log("CONTEXT", JSON.stringify(context, null, 2));

  const { db, secret } = await initWithSecrets(event.secretId);
  const lambdaClient = new LambdaClient();

  const document = await db.document.findUniqueOrThrow({
    where: {
      id: event.documentId,
    },
  });

  const documentChunks: Document[] = await getChunksFromS3(
    event.chunkPath,
    secret.AWS_S3_BUCKET_NAME,
  );

  console.log(`Retrieved ${documentChunks.length} chunks from S3`);

  if (!document) {
    return {
      statusCode: 404,
      body: JSON.stringify({
        message: `Document not found for documentId ${event.documentId}`,
      }),
    };
  }

  try {
    await insertEmbeddedIntoDb(
      documentChunks,
      {
        db: db,
        document: {
          id: event.documentId,
          name: document.name,
        },
        orgId: document.orgId,
      },
      secret,
      DocumentChunkType.JUMBO,
    );

    console.log("Deleting chunk file from S3", event.chunkPath);
    await deleteObjectFromS3(secret.AWS_S3_BUCKET_NAME ?? "", event.chunkPath);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Successfully stored ${documentChunks.length} chunks for document ${event.documentId}`,
      }),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Error getting file contents for document ${event.documentId}: ${err}`,
      }),
    };
  }
};

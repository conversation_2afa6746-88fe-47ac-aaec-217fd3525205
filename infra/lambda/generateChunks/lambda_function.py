import asyncio
import logging
import os
import sys

import boto3

from pyrpc.utils.get_env_var import get_env_var

sys.path.append('/mnt/efs/python/site-packages/')


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

secret = None


def lambda_handler(event, context):
  
    try:
        global secret

        try:
            # Validate secret_name
            if not event.get("secret"):
                raise ValueError("Secret must be provided.")
            
            # Retrieve the secret by name
            client = boto3.client("secretsmanager")
            wrapper = GetSecretWrapper(client)
            secret = event.get("secret")
            
        except Exception as e:
            logger.error(f"Error retrieving secret: {e}")
            raise

        # print(json.dumps(secret))

        openai_api_key = (
                get_env_var("OPENAI_API_KEY_JUMBO_CHUNK", secret=secret)
                or get_env_var("OPENAI_API_KEY", secret=secret)
        )

        gemini_api_key = (
                get_env_var("GEMINI_API_KEY", secret=secret)
                or get_env_var("GOOGLE_API_KEY", secret=secret)
        )

        # Manually force-set the env var for AWS environments
        os.environ["OPENAI_API_KEY"] = openai_api_key
        os.environ["GEMINI_API_KEY"] = gemini_api_key

        # Import dependencies here to avoid lambda startup timeout (takes more than 10 seconds to import via EFS)
        from pyrpc.context_based_doc_chunking import chunking_pipeline
        from pyrpc.utils.tracing import tracing

        document_id = event["documentId"]
        tracing_context = event.get("tracingContext", {})
        trace_id = tracing_context.get("traceId", None)
        session_id = tracing_context.get("sessionId", None)
        parent_span_id = tracing_context.get("parentSpanId", None)

        try: 
            logging.info(f'Sending async chunk generation request for {document_id}')
            with tracing.continue_trace(trace_id=trace_id, session_id=session_id, parent_span_id=parent_span_id):
                asyncio.run(chunking_pipeline.worker(doc_id=document_id, tracing_context=tracing_context, secret=secret))
                logging.info(f'Done sending chunk generation request for {document_id}')

            return {
                "statusCode": 200,
                "body": "Chunk generation request sent successfully"
            }
        except Exception as e:
            logger.error(f"generateChunks: Unable to create chunking pipeline worker: {str(e)}.")
            return {
                "statusCode": 500,
                "body": "Unable to create chunking pipeline worker: " + str(e)
            }
    
    except Exception as e:
        logger.error(f"generateChunks: {str(e)}.")
        return {
            "statusCode": 500,
            "body": "Chunk generation request failed: " + str(e)
        }

class GetSecretWrapper:
    def __init__(self, secretsmanager_client):
        self.client = secretsmanager_client


    def get_secret(self, secret_name):
        """
        Retrieve individual secrets from AWS Secrets Manager using the get_secret_value API.

        :param secret_name: The name of the secret fetched.
        :type secret_name: str
        """
        try:
            get_secret_value_response = self.client.get_secret_value(
                SecretId=secret_name
            )
            logging.info("Secret retrieved successfully.")
            return get_secret_value_response["SecretString"]
        except self.client.exceptions.ResourceNotFoundException:
            msg = f"The requested secret {secret_name} was not found."
            logger.info(msg)
            return msg
        except Exception as e:
            logger.error(f"generateChunks: GetSecretWrapper: {str(e)}.")
            raise


import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { DocumentSource } from "@prisma/client";
import { APIGatewayProxyResult, Context } from "aws-lambda";
import { env } from "~/env";
import { indexSharepoint } from "~/lib/integrations/azure/indexer";
import { indexEgnyte } from "~/lib/integrations/egnyte/indexer";
import {
  initWithSecrets,
  writeFileToS3,
} from "../../../src/lib/integrations/aws/utils";
import { LambdaEvent } from "../vectorizer";
import { getDocumentContents } from "~/lib/fileUtils";
import { markDocumentStatusError } from "~/lib/vectorizer";

export type GetFileContentsLambdaEvent = {
  documentId: string;
  secretId: string;
};

export const handler = async (
  event: GetFileContentsLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  console.log("EVENT", JSON.stringify(event, null, 2));
  console.log("CONTEXT", JSON.stringify(context, null, 2));

  const { db, secret } = await initWithSecrets(event.secretId);

  try {
    const document = await db.document.findUniqueOrThrow({
      where: {
        id: event.documentId,
      },
    });

    console.log("getFileContents: Document", document);

    if (!document.orgId) {
      console.error("getFileContents: Document has no orgId", document);
      throw new Error("getFileContents: Document has no orgId");
    }

    const documentContents = await getDocumentContents(
      document,
      db,
      secret?.AWS_S3_BUCKET_NAME,
      secret,
    );

    console.log("getFileContents: Writing file contents to S3");

    const documentPath = await writeFileToS3(
      document.id,
      {
        data: documentContents.body,
        contentType: documentContents.contentType,
      },
      secret?.AWS_S3_BUCKET_NAME ?? env.AWS_S3_BUCKET_NAME,
    );

    console.log("getFileContents: S3 Document path", documentPath);

    return {
      statusCode: 200,
      body: JSON.stringify({
        documentPath,
      }),
    };
  } catch (err) {
    console.log(err);

    console.error(
      "Error getting file contents for document",
      event.documentId,
      err,
    );

    console.error("Marking document as error", event.documentId);

    await markDocumentStatusError({
      db,
      documentId: event.documentId,
    });

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Error getting file contents for document ${event.documentId}: ${err}`,
      }),
    };
  }
};

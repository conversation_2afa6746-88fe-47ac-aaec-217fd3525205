import { type APIGatewayProxyResult, type Context } from "aws-lambda";

import {
  type TableInput,
  generateResponseFromDocumentQuestions,
  generateEntireTableResponse,
} from "~/lib/rag-response";
import { type ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import {
  GetDocumentWithEmptyAnswersInclude,
  GetDocumentWithEmptyAnswersIncludeForTables,
  type GetDocumentWithEmptyAnswersType,
  GetDocumentWithEmptyAnswersTypeForTables,
  overwriteExistingResponseContent,
  overwriteExistingTableResponseContent,
} from "~/server/api/routers/document";
import { backoffDelay } from "~/server/utils/backoff";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";
import { WorkSheetTableStatus } from "@prisma/client";
import { markDocumentStatusReady } from "~/lib/vectorizer";

export type TableGeneratorLambdaEvent = {
  documentId: string;
  orgId: string;
  secretId: string;
  userId: string;
  batchPayload: TableInput[];
  tagIds: string[];
  fundIds: string[];
  categoryIds: string[];
  modelParameters: ModelParameters;
  batchIndex: number;
};

export const handler = async (
  event: TableGeneratorLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    console.log("*** tableGenerator: batchIndex", event.batchIndex);

    const delay = event.batchIndex * 1000;
    console.log("*** tableGenerator: Waiting for", delay, "ms");

    await new Promise((resolve) => setTimeout(resolve, delay));

    const { db, secret } = await initWithSecrets(event.secretId);

    const MAX_RETRIES = 5;
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        const response = await generateEntireTableResponse({
          db,
          orgId: event.orgId,
          documentId: event.documentId,
          tables: event.batchPayload,
          tagIds: event.tagIds,
          fundIds: event.fundIds,
          categoryIds: event.categoryIds,
          modelParameters: event.modelParameters,
          secret,
          userId: event.userId,
        });

        console.log("*** tableGenerator: responses", response?.length);

        if (response === null || response === undefined) {
          throw new Error("*** tableGenerator: Response is null");
        }

        // Validate response payload table IDsmatches input table IDs
        const validatedResponseIds = event.batchPayload.map((p) =>
          response.find((r) => r.tableId === p.tableId),
        );

        if (validatedResponseIds.length !== event.batchPayload.length) {
          throw new Error("Response table IDs do not match input table IDs");
        }

        const documentWithTablesAndEmptyAnswers: GetDocumentWithEmptyAnswersTypeForTables =
          await db.document.findFirstOrThrow({
            where: {
              id: event.documentId,
              orgId: event.orgId,
            },
            include: GetDocumentWithEmptyAnswersIncludeForTables,
          });

        console.log(
          "documentWithTablesAndEmptyAnswers",
          documentWithTablesAndEmptyAnswers.worksheets.map((r) =>
            r.tables.map((t) => t.cells.map((c) => c.prompt)).flat(),
          ),
        );

        const answersAndResponses = response.map((response) => ({
          answer: response?.answer ?? "",
          reason: response?.reason ?? "",
          citations: response?.citations ?? [],
          tableId: response?.tableId ?? "",
          response: documentWithTablesAndEmptyAnswers.worksheets.find((r) =>
            r.tables.find((t) => t.id === response?.tableId),
          ),
          cells: response?.cells ?? [],
        }));

        console.log(
          "*** tableGenerator: answersAndResponses",
          answersAndResponses,
        );

        if (answersAndResponses.length > 0) {
          console.log(
            "processTablesInSpreadsheet: Overwriting existing response contents",
            answersAndResponses,
          );

          answersAndResponses.map((a) => {
            console.log(
              `Cells for response ${a.tableId}: ${JSON.stringify(
                a.cells.map((c) => ({
                  cellAddress: c.cellAddress,
                  cellValue: c.cellValue,
                })),
              )}`,
            );
          });

          console.time(
            `processTablesInSpreadsheet: Overwriting existing response contents for ${event.documentId}. Total responses generated: ${response.length}`,
          );
          const updatedResponseContents =
            await overwriteExistingTableResponseContent(
              db,
              answersAndResponses.map((r) => ({
                tableId: r.tableId,
                answer: r.answer,
                reason: r.reason,
                citations: r.citations,
                cells: r.cells,
              })),
              event.userId,
              event.orgId,
            );

          // console.log("updatedResponseContents", updatedResponseContents);
          console.timeEnd(
            `processTablesInSpreadsheet: Overwriting existing response contents for ${event.documentId}. Total responses generated: ${response.length}`,
          );
        }

        // Check to see if there are any ungenerated tables left
        const ungeneratedTables = await db.workSheetTable.count({
          where: {
            workSheet: {
              document: {
                id: event.documentId,
              },
            },

            status: WorkSheetTableStatus.GENERATING_ANSWERS,
          },
        });

        console.log(
          "*** tableGenerator: tables left to generate",
          ungeneratedTables,
        );

        if (ungeneratedTables === 0) {
          console.log(
            "*** tableGenerator: All tables generated, marking document as ready",
          );

          await markDocumentStatusReady({
            db,
            documentId: event.documentId,
            orgId: event.orgId,
          });
        }

        return {
          statusCode: 200,
          body: JSON.stringify(response),
        };
      } catch (err) {
        console.log(err);
        retries++;

        await backoffDelay(retries);
      }
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Error generating responses after ${MAX_RETRIES} retries`,
      }),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};

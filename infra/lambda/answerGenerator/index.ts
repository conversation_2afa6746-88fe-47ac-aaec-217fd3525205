import { type APIGatewayProxyResult, type Context } from "aws-lambda";

import {
  type QuestionInput,
  generateResponseFromDocumentQuestions,
} from "~/lib/rag-response";
import { type ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import {
  GetDocumentWithEmptyAnswersInclude,
  type GetDocumentWithEmptyAnswersType,
  overwriteExistingResponseContent,
} from "~/server/api/routers/document";
import { backoffDelay } from "~/server/utils/backoff";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";
import { markDocumentStatusReady } from "~/lib/vectorizer";
import { AnswerGenerationType } from "@prisma/client";

export type AnswerGeneratorLambdaEvent = {
  documentId: string;
  orgId: string;
  secretId: string;
  userId: string;
  batchPayload: QuestionInput[];
  tagIds: string[];
  categoryIds: string[];
  fundIds: string[];
  modelParameters: ModelParameters;
  batchIndex: number;
};

export const handler = async (
  event: AnswerGeneratorLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    console.log(
      "*** generateResponseFromDocumentQuestions: batchIndex",
      event.batchIndex,
    );

    // const delay = event.batchIndex * 1000;
    // console.log(
    //   "*** generateResponseFromDocumentQuestions: Waiting for",
    //   delay,
    //   "ms",
    // );

    // await new Promise((resolve) => setTimeout(resolve, delay));

    const { db, secret } = await initWithSecrets(event.secretId);

    const MAX_RETRIES = 5;
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        const response = await generateResponseFromDocumentQuestions({
          db,
          orgId: event.orgId,
          documentId: event.documentId,
          questions: event.batchPayload,
          tagIds: event.tagIds,
          categoryIds: event.categoryIds,
          fundIds: event.fundIds,
          modelParameters: event.modelParameters,
          secret,
          userId: event.userId,
        });

        console.log(
          "*** generateResponseFromDocumentQuestions: responses",
          response?.length,
        );

        if (response === null) {
          throw new Error(
            "*** generateResponseFromDocumentQuestions: Response is null",
          );
        }
        // Validate response payload question IDs matche input question IDs
        const validatedResponseIds = event.batchPayload.map((p) =>
          response.find((r) => r.questionId === p.questionId),
        );

        if (validatedResponseIds.length !== event.batchPayload.length) {
          throw new Error(
            "Response question IDs do not match input question IDs",
          );
        }

        const documentWithQuestionsAndEmptyAnswers: GetDocumentWithEmptyAnswersType =
          await db.document.findFirstOrThrow({
            where: {
              id: event.documentId,
              orgId: event.orgId,
            },
            include: {
              responses: {
                where: {
                  documentId: event.documentId,
                },

                include: GetDocumentWithEmptyAnswersInclude,
              },
            },
          });

        const answersAndResponses = response.map((response) => ({
          answer: response?.answer ?? "",
          reason: response?.reason ?? "",
          citations: response?.citations ?? [],
          response: documentWithQuestionsAndEmptyAnswers.responses.find((r) =>
            r.response.questions.find((q) => q.id === response?.questionId),
          ),
          insufficientData: response?.insufficientData ?? false,
        }));

        console.log(
          "*** generateResponseFromDocumentQuestions: answersAndResponses",
          answersAndResponses,
        );

        console.time(
          `Overwriting existing response contents for ${documentWithQuestionsAndEmptyAnswers.name}. Total responses generated: ${response.length}`,
        );
        const updatedResponseContents = await overwriteExistingResponseContent(
          db,
          answersAndResponses,
          event.userId,
          event.orgId,
          secret,
        );

        console.timeEnd(
          `Overwriting existing response contents for ${documentWithQuestionsAndEmptyAnswers.name}. Total responses generated: ${response.length}`,
        );

        // Check to see if there are any unanswered questions left
        const extractedAnswers = await db.responseContent.count({
          where: {
            response: {
              documents: {
                some: {
                  documentId: event.documentId,
                },
              },
            },
            answerGenerationType: AnswerGenerationType.EXTRACTED,
          },
        });

        console.log("Responses left to generate:", extractedAnswers);
        // If there are no extracted answers, mark the document as ready

        if (extractedAnswers === 0) {
          console.log(
            "Marking document as ready",
            documentWithQuestionsAndEmptyAnswers.name,
          );

          await markDocumentStatusReady({
            db,
            documentId: event.documentId,
            orgId: event.orgId,
          });
        }
        return {
          statusCode: 200,
          body: JSON.stringify(response),
        };
      } catch (err) {
        console.log("answerGenerator error", err);
        retries++;

        await backoffDelay(retries);
      }
    }

    console.log("Error generating responses after", MAX_RETRIES, "retries");

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Error generating responses after ${MAX_RETRIES} retries`,
      }),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};

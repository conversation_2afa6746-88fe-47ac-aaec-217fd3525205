import asyncio

import boto3
import logging
import sys

sys.path.append('/mnt/efs/python/site-packages/')


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

secret = None


def lambda_handler(event, context):
  
    
    try:
        from pyrpc.context_based_doc_chunking.step_1_pre_chunk import raw_chunk

        global secret

        try:
            # Validate secret_name
            if not event.get("secret"):
                raise ValueError("Secret must be provided.")
            
            # Retrieve the secret by name
            client = boto3.client("secretsmanager")
            wrapper = GetSecretWrapper(client)
            secret = event.get("secret")
            
        except Exception as e:
            logger.error(f"Error retrieving secret: {e}")
            raise

        try:
            doc_id = event.get("id", None)
            # TODO DORON/GILAD 050825 we need to be careful here. I made worker async to allow process raw to be called as a task
            #                         we need to make sure this does not create a problem when running as lambda.
            elements = asyncio.run(raw_chunk(doc_id, secret))
            return {
                "statusCode": 200,
                "body": elements
            }
        
        except Exception as e:
            logger.error(f"getUnstructureFileContents: {str(e)}.")
            return {
                "statusCode": 500,
                "body": "getUnstructureFileContents failed: " + str(e)
            }
    
    except Exception as e:
        logger.error(f"getUnstructureFileContents: {str(e)}.")
        return {
            "statusCode": 500,
            "body": "getUnstructureFileContents failed: " + str(e)
        }

class GetSecretWrapper:
    def __init__(self, secretsmanager_client):
        self.client = secretsmanager_client


    def get_secret(self, secret_name):
        """
        Retrieve individual secrets from AWS Secrets Manager using the get_secret_value API.

        :param secret_name: The name of the secret fetched.
        :type secret_name: str
        """
        try:
            get_secret_value_response = self.client.get_secret_value(
                SecretId=secret_name
            )
            logging.info("Secret retrieved successfully.")
            return get_secret_value_response["SecretString"]
        except self.client.exceptions.ResourceNotFoundException:
            msg = f"The requested secret {secret_name} was not found."
            logger.info(msg)
            return msg
        except Exception as e:
            logger.error(f"generateChunks: GetSecretWrapper: {str(e)}.")
            raise